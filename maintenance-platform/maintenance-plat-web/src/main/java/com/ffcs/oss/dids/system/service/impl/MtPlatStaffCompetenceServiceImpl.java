package com.ffcs.oss.dids.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.ai.common.core.util.FileUtil;
import com.ffcs.oss.ai.common.core.util.StringUtils;
import com.ffcs.oss.ai.dfs.service.CtdfsService;
import com.ffcs.oss.dids.common.entity.MtPlatStaffCompetenceD;
import com.ffcs.oss.dids.system.mapper.staff.MtPlatStaffCompetenceMapper;
import com.ffcs.oss.dids.system.service.DifyService;
import com.ffcs.oss.dids.system.service.MtPlatStaffCompetenceService;
import com.ffcs.oss.dids.system.web.rest.constants.CommonConstant;
import com.ffcs.oss.dids.system.web.rest.evt.dify.DifyCreateAppApiKeyEvt;
import com.ffcs.oss.dids.system.web.rest.evt.dify.DifyCreateAppEvt;
import com.ffcs.oss.dids.system.web.rest.evt.dify.DifyUpdateAppEvt;
import com.ffcs.oss.dids.system.web.rest.evt.dify.DifyUploadEvt;
import com.ffcs.oss.dids.system.web.rest.evt.staff.MtPlatStaffCompetenceEvt;
import com.ffcs.oss.dids.system.web.rest.evt.staff.MtPlatStaffCompetenceQueryEvt;
import com.ffcs.oss.dids.system.web.rest.vm.dify.DifyCreateAppApiKeyVm;
import com.ffcs.oss.dids.system.web.rest.vm.dify.DifyCreateAppVm;
import com.ffcs.oss.dids.system.web.rest.vm.dify.DifyLoginVm;
import com.ffcs.oss.dids.system.web.rest.vm.dify.DifyUploadVm;
import com.ffcs.oss.dids.system.web.rest.vm.staff.DiFlowDefinitionVm;
import com.ffcs.oss.dids.system.web.rest.vm.staff.PlatStaffCompetenceDifyVm;
import com.ffcs.oss.dids.system.web.rest.vm.staff.PlatStaffCompetenceVm;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 员工技能Service实现类
 *
 * <AUTHOR>
 */
@Service
public class MtPlatStaffCompetenceServiceImpl extends ServiceImpl<MtPlatStaffCompetenceMapper, MtPlatStaffCompetenceD> implements MtPlatStaffCompetenceService {

    @Autowired
    private DifyService difyService;

    @Autowired
    private CtdfsService ctdfsService;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 分页查询员工技能
     *
     * @param evt
     * @return
     */
    @Override
    public PageInfo<PlatStaffCompetenceVm> queryPage(MtPlatStaffCompetenceQueryEvt evt) {
        PageHelper.startPage(evt.getPageNo(), evt.getPageSize());
        List<PlatStaffCompetenceVm> list = baseMapper.queryPage(evt);
        PageInfo<PlatStaffCompetenceVm> pageInfo = new PageInfo<>(list);
        pageInfo.getList().stream().forEach(a -> {
            if (CommonConstant.COMPETENCE_TYPE_5.equals(a.getCompetenceType())) {
                DiFlowDefinitionVm diFlowDefinitionVm = baseMapper.getMaxFlow(a.getAppId());
                a.setAppUrl(a.getAppUrl() + "&id=" + diFlowDefinitionVm.getDiFlowDefinitionInfoId() + "&flowName=" + diFlowDefinitionVm.getFlowName());
            }
        });
        return pageInfo;
    }

    /**
     * 新增员工技能
     *
     * @param evt
     * @return
     */
    @Override
    public PlatStaffCompetenceDifyVm saveCompetence(MtPlatStaffCompetenceEvt evt) throws Exception {
        PlatStaffCompetenceDifyVm platStaffCompetenceDifyVm = new PlatStaffCompetenceDifyVm();
        String competenceType = evt.getCompetenceType();
        if (Arrays.asList(CommonConstant.COMPETENCE_TYPE_0, CommonConstant.COMPETENCE_TYPE_1, CommonConstant.COMPETENCE_TYPE_2, CommonConstant.COMPETENCE_TYPE_3).contains(competenceType)) {
            //获取dify登录信息
            DifyLoginVm difyLoginVm = difyService.login();

            //创建app
            DifyCreateAppEvt difyCreateAppEvt = new DifyCreateAppEvt();
            difyCreateAppEvt.setAccessToken(difyLoginVm.getAccessToken());

            //上传文件
            String competenceUrl = evt.getCompetenceUrl();
            if (StringUtils.isNotEmpty(competenceUrl)) {
                InputStream inputStream = ctdfsService.downloadStream(competenceUrl);
                DifyUploadEvt difyUploadEvt = new DifyUploadEvt();
                difyUploadEvt.setAccessToken(difyLoginVm.getAccessToken());
                difyUploadEvt.setResource(IOUtils.toByteArray(inputStream));
                difyUploadEvt.setFileName(FileUtil.getFileSimpleName(competenceUrl));
                DifyUploadVm difyUploadVm = difyService.upload(difyUploadEvt);
                difyCreateAppEvt.setIcon(difyUploadVm.getId());
            }

            difyCreateAppEvt.setDescription(evt.getCompetenceDesc());
            difyCreateAppEvt.setName(evt.getCompetenceName());
            //字典值competence_type 0简报类 1问答类 2Dify工作流  3Agent 4定制推理能力  5智行工作流
            //字典值app_mode 聊天助手(chat) Agent(agent-chat) 文本生成(completion) Chatflow(advanced-chat) 工作流(workflow)
            /**
             * 简报类	     Dify：文本生成应用	　completion
             * 问答类	     Dify：聊天助手　       chat
             * 工作流-智行    智行：流程编排	　
             * 工作流-Dify	Dify：工作流	　        workflow
             * Agent	    Dify：Agent           agent-chat
             * 定制推理能力	服务API
             */
            String appMode = null;
            if ("0".equals(competenceType)) {
                appMode = "completion";
            } else if ("1".equals(competenceType)) {
                appMode = "chat";
            } else if ("2".equals(competenceType)) {
                appMode = "workflow";
            } else if ("3".equals(competenceType)) {
                appMode = "agent-chat";
            }
            difyCreateAppEvt.setMode(appMode);
            DifyCreateAppVm difyCreateAppVm = difyService.createApp(difyCreateAppEvt);

            // 应用模式  聊天助手(chat) Agent(agent-chat) 文本生成(completion)
            //http://192.168.35.162:31671/app/4e4348e4-9a9d-4ec6-91c4-9c03de8d4294/configuration
            // 应用模式  Chatflow(advanced-chat) 工作流(workflow)
            //http://192.168.35.162:31671/app/90968224-617f-40b2-b682-0ea80b73d661/workflow
            String appUrl = null;
            if ("chat".equals(appMode) || "agent-chat".equals(appMode) || "completion".equals(appMode)) {
                appUrl = "/app/" + difyCreateAppVm.getId() + "/configuration";
            } else if ("workflow".equals(appMode) || "advanced-chat".equals(appMode)) {
                appUrl = "/app/" + difyCreateAppVm.getId() + "/workflow";
            }

            //生成app-keys
            DifyCreateAppApiKeyEvt difyCreateAppApiKeyEvt = new DifyCreateAppApiKeyEvt();
            difyCreateAppApiKeyEvt.setAccessToken(difyLoginVm.getAccessToken());
            difyCreateAppApiKeyEvt.setAppId(difyCreateAppVm.getId());
            DifyCreateAppApiKeyVm difyCreateAppApiKeyVm = difyService.createAppApiKey(difyCreateAppApiKeyEvt);

            //保存到数据库
            MtPlatStaffCompetenceD mtPlatStaffCompetenceD = BeanUtil.copyProperties(evt, MtPlatStaffCompetenceD.class);
            mtPlatStaffCompetenceD.setAppId(difyCreateAppVm.getId());
            mtPlatStaffCompetenceD.setAppToken(difyCreateAppApiKeyVm.getToken());
            mtPlatStaffCompetenceD.setAppMode(appMode);
            mtPlatStaffCompetenceD.setAppUrl(appUrl);
            baseMapper.insert(mtPlatStaffCompetenceD);

            //设置返回数据
            platStaffCompetenceDifyVm.setAccessToken(difyLoginVm.getAccessToken());
            platStaffCompetenceDifyVm.setPlatStaffCompetenceVm(BeanUtil.copyProperties(mtPlatStaffCompetenceD, PlatStaffCompetenceVm.class));
        } else {
            //保存到数据库
            MtPlatStaffCompetenceD mtPlatStaffCompetenceD = BeanUtil.copyProperties(evt, MtPlatStaffCompetenceD.class);
            if (CommonConstant.COMPETENCE_TYPE_4.equals(competenceType)) {
                //mtPlatStaffCompetenceD.setAppUrl("/cndids-portal/#/csbDetail?id=" + evt.getAppId());
                if ("dev".equals(env)) {
                    mtPlatStaffCompetenceD.setAppUrl("/portal-ui/#/csbDetail?id=" + evt.getAppId());
                } else {
                    mtPlatStaffCompetenceD.setAppUrl("/cndids-portal/#/csbDetail?id=" + evt.getAppId());
                }
            } else if (CommonConstant.COMPETENCE_TYPE_5.equals(competenceType)) {
                //mtPlatStaffCompetenceD.setAppUrl("/cndids-portal/#/OMKnowledge?flowCode=" + evt.getAppId() + "&type=edit");
                if ("dev".equals(env)) {
                    mtPlatStaffCompetenceD.setAppUrl("/portal-ui/#/OMKnowledge?flowCode=" + evt.getAppId() + "&type=edit");
                } else {
                    mtPlatStaffCompetenceD.setAppUrl("/cndids-portal/#/OMKnowledge?flowCode=" + evt.getAppId() + "&type=edit");
                }
            }
            baseMapper.insert(mtPlatStaffCompetenceD);
            //设置返回数据
            platStaffCompetenceDifyVm.setPlatStaffCompetenceVm(BeanUtil.copyProperties(mtPlatStaffCompetenceD, PlatStaffCompetenceVm.class));
        }
        //回参特殊处理
        if (CommonConstant.COMPETENCE_TYPE_5.equals(platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().getCompetenceType())) {
            DiFlowDefinitionVm diFlowDefinitionVm = baseMapper.getMaxFlow(platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().getAppId());
            platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().setAppUrl(platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().getAppUrl() + "&id=" + diFlowDefinitionVm.getDiFlowDefinitionInfoId() + "&flowName=" + diFlowDefinitionVm.getFlowName());
        }
        return platStaffCompetenceDifyVm;
    }

    @Override
    public PlatStaffCompetenceDifyVm updateCompetence(MtPlatStaffCompetenceEvt evt) throws Exception {
        //定义返回
        PlatStaffCompetenceDifyVm platStaffCompetenceDifyVm = new PlatStaffCompetenceDifyVm();

        //查询源数据
        MtPlatStaffCompetenceD oldMtPlatStaffCompetenceD = baseMapper.selectById(evt.getMtPlatStaffCompetenceId());

        //判断数据是否修改
        if (StringUtils.equals(oldMtPlatStaffCompetenceD.getCompetenceName(), evt.getCompetenceName())
                && StringUtils.equals(oldMtPlatStaffCompetenceD.getCompetenceUrl(), evt.getCompetenceUrl())
                && StringUtils.equals(oldMtPlatStaffCompetenceD.getCompetenceDesc(), evt.getCompetenceDesc())
                && StringUtils.equals(oldMtPlatStaffCompetenceD.getLabel(), evt.getLabel())
                && StringUtils.equals(oldMtPlatStaffCompetenceD.getAppId(), evt.getAppId())
        ) {
            //设置返回数据
            platStaffCompetenceDifyVm.setPlatStaffCompetenceVm(BeanUtil.copyProperties(oldMtPlatStaffCompetenceD, PlatStaffCompetenceVm.class));
        } else {
            String competenceType = evt.getCompetenceType();
            if (Arrays.asList(CommonConstant.COMPETENCE_TYPE_0, CommonConstant.COMPETENCE_TYPE_1, CommonConstant.COMPETENCE_TYPE_2, CommonConstant.COMPETENCE_TYPE_3).contains(competenceType)) {
                //判断数据是否修改
                if (!StringUtils.equals(oldMtPlatStaffCompetenceD.getCompetenceName(), evt.getCompetenceName())
                        || !StringUtils.equals(oldMtPlatStaffCompetenceD.getCompetenceUrl(), evt.getCompetenceUrl())
                        || !StringUtils.equals(oldMtPlatStaffCompetenceD.getCompetenceDesc(), evt.getCompetenceDesc())
                ) {
                    //获取dify登录信息
                    DifyLoginVm difyLoginVm = difyService.login();

                    DifyUpdateAppEvt difyUpdateAppEvt = new DifyUpdateAppEvt();
                    difyUpdateAppEvt.setAccessToken(difyLoginVm.getAccessToken());

                    //上传文件
                    String competenceUrl = evt.getCompetenceUrl();
                    if (StringUtils.isNotEmpty(competenceUrl)) {
                        InputStream inputStream = ctdfsService.downloadStream(competenceUrl);
                        DifyUploadEvt difyUploadEvt = new DifyUploadEvt();
                        difyUploadEvt.setAccessToken(difyLoginVm.getAccessToken());
                        difyUploadEvt.setResource(IOUtils.toByteArray(inputStream));
                        difyUploadEvt.setFileName(FileUtil.getFileSimpleName(competenceUrl));
                        DifyUploadVm difyUploadVm = difyService.upload(difyUploadEvt);
                        difyUpdateAppEvt.setIcon(difyUploadVm.getId());
                    }

                    difyUpdateAppEvt.setDescription(evt.getCompetenceDesc());
                    difyUpdateAppEvt.setName(evt.getCompetenceName());
                    difyUpdateAppEvt.setAppId(oldMtPlatStaffCompetenceD.getAppId());
                    difyService.updateApp(difyUpdateAppEvt);

                    //设置token
                    platStaffCompetenceDifyVm.setAccessToken(difyLoginVm.getAccessToken());
                }
            } else {
                if (!StringUtils.equals(oldMtPlatStaffCompetenceD.getAppId(), evt.getAppId())) {
                    oldMtPlatStaffCompetenceD.setAppId(evt.getAppId());
                    if (CommonConstant.COMPETENCE_TYPE_4.equals(competenceType)) {
                        if ("dev".equals(env)) {
                            oldMtPlatStaffCompetenceD.setAppUrl("/portal-ui/#/csbDetail?id=" + evt.getAppId());
                        } else {
                            oldMtPlatStaffCompetenceD.setAppUrl("/cndids-portal/#/csbDetail?id=" + evt.getAppId());
                        }
                    } else if (CommonConstant.COMPETENCE_TYPE_5.equals(competenceType)) {
                        if ("dev".equals(env)) {
                            oldMtPlatStaffCompetenceD.setAppUrl("/portal-ui/#/OMKnowledge?flowCode=" + evt.getAppId() + "&type=edit");
                        } else {
                            oldMtPlatStaffCompetenceD.setAppUrl("/cndids-portal/#/OMKnowledge?flowCode=" + evt.getAppId() + "&type=edit");
                        }
                    }
                }
            }
            //修改到数据库
            oldMtPlatStaffCompetenceD.setCompetenceName(evt.getCompetenceName());
            oldMtPlatStaffCompetenceD.setCompetenceDesc(evt.getCompetenceDesc());
            oldMtPlatStaffCompetenceD.setCompetenceUrl(evt.getCompetenceUrl());
            oldMtPlatStaffCompetenceD.setLabel(evt.getLabel());
            baseMapper.updateById(oldMtPlatStaffCompetenceD);
            //设置返回数据
            platStaffCompetenceDifyVm.setPlatStaffCompetenceVm(BeanUtil.copyProperties(oldMtPlatStaffCompetenceD, PlatStaffCompetenceVm.class));
        }
        //回参特殊处理
        if (CommonConstant.COMPETENCE_TYPE_5.equals(platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().getCompetenceType())) {
            DiFlowDefinitionVm diFlowDefinitionVm = baseMapper.getMaxFlow(platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().getAppId());
            platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().setAppUrl(platStaffCompetenceDifyVm.getPlatStaffCompetenceVm().getAppUrl() + "&id=" + diFlowDefinitionVm.getDiFlowDefinitionInfoId() + "&flowName=" + diFlowDefinitionVm.getFlowName());
        }
        return platStaffCompetenceDifyVm;
    }

}