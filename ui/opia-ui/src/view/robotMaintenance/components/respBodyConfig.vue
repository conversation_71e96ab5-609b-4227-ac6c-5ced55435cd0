<template>
<div>
    <el-row>
        <el-col :span="24">
            <el-table
                ref="fieldAttrTable"
                :data="attrTree"
                style="width: 100%"
                :indent="8"
                border
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                :row-key="'id'"
                :default-expand-all="true"
                :row-class-name="setReqBodySize"
            >
                <el-table-column prop="index" label="序号" width="120">
                    <template slot-scope="scope">
                        <span v-html="formatIndex(scope.row)"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="headerKey" label="参数中文名" align="center">
                    <template slot-scope="scope">
                        <el-input v-if="isEdit" v-model="scope.row.cnName"></el-input>
                        <span v-if="!isEdit">{{ scope.row.cnName }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="参数编码" width="120">
                    <template slot-scope="scope">
                        <el-input
                            v-if="isEdit"
                            v-model="scope.row.outParam"
                            @blur="scope.row.outParam = checkNameComon($event)"
                            maxlength="50"
                            placeholder="只能输入字母或数字与字母组合、中间可以使用短横线、下划线"
                            title="只能输入字母或数字与字母组合、中间可以使用短横线、下划线"
                        ></el-input>
                        <span v-if="!isEdit">{{ scope.row.outParam }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="参数类型" width="120">
                    <template slot-scope="scope">
                        <el-select size="mini" v-if="isEdit" v-model="scope.row.parameterType" @change="fieldTypeChange(scope.row, 1)">
                            <el-option v-for="(item, index) in allDictionary.parameterType" :key="index" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                        <span v-if="!isEdit">{{ transform(scope.row.parameterType, allDictionary.parameterType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="desc" label="参数默认值" width="120">
                        <template slot-scope="scope">
                            <el-input v-if="isEdit" :disabled="scope.row.indexNo.indexOf('.') > -1"  maxlength="2000" v-model="scope.row.outValue"></el-input>
                            <span v-if="!isEdit">{{ scope.row.outValue }}</span>
                        </template>
                </el-table-column>
                <el-table-column prop="analyzingType" label="解析类型" width="120">
                    <template slot-scope="scope">
                        <el-select
                            v-if="isEdit"
                            v-model="scope.row.analyzingType"
                            placeholder="请选择或输入"
                            filterable
                            allow-create
                            default-first-option
                            style="width: 100%"
                            :disabled="scope.row.parameterType!='字符串'"
                            @change="fieldTypeChange(scope.row, 2)"
                        >
                            <el-option v-for="(item, index) in analyzingTypeOptions" :key="index" :label="item.codeName" :value="item.codeValue"></el-option>
                        </el-select>
                        <span v-if="!isEdit">{{ scope.row.analyzingType }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" width="120">
                        <template slot-scope="scope">
                            <el-input v-if="isEdit" maxlength="200" v-model="scope.row.remark"></el-input>
                            <span v-if="!isEdit">{{scope.row.remark}}</span>
                        </template>
                </el-table-column>
                <el-table-column prop="isShow" label="回显" align="center" width="120">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.isShow" v-if="isEdit" :placeholder="$t('cndids.choosePlaceholder')">
                            <el-option
                                v-for="item in options"
                                :key="item.codeValue"
                                :label="item.codeName"
                                :value="item.codeValue">
                            </el-option>
                        </el-select>
                        <span v-if="!isEdit">{{!scope.row.isShow ?  '' : scope.row.isShow === '0' ? '否' : '是'}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="description" label="会话语境" width="120">
                    <template slot-scope="scope">
                        <el-cascader
                            v-model="scope.row.variableName"
                            ref="cascaderAddr"
                            clearable
                            :options="sessionContextVariableOptions"
                            :props="{value: 'id', label: 'label', checkStrictly: 'true'}"
                            :disabled="!isEdit"
                            @change="handleChange(scope.row)" filterable></el-cascader>
                    </template>
                </el-table-column>
                <el-table-column prop="behaviorObjNameArr" label="运维对象/属性" align="center" min-width="120%">
                    <template slot-scope="scope">
                        <el-cascader
                            v-model="scope.row.behaviorObjNameArr"
                            ref="cascaderAddr1"
                            clearable
                            @change="handleObjCascadeChange(scope.row)"
                            :options="operatiMaintBehaviorLibraryOptions"
                            :disabled="!isEdit"
                            :props="{value: 'id', label: 'label', checkStrictly: 'true'}"
                            filterable></el-cascader>
                    </template>
                </el-table-column>
				<el-table-column prop="showPopup" label="是否弹窗" align="center" width="120">
					<template slot-scope="scope">
						<el-select v-model="scope.row.showPopup" v-if="isEdit" :placeholder="$t('cndids.choosePlaceholder')">
							<el-option
								v-for="item in showPopupOptions"
								:key="item.codeValue"
								:label="item.codeName"
								:value="item.codeValue">
							</el-option>
						</el-select>
						<span v-if="!isEdit">{{!scope.row.isShow ?  '' : scope.row.isShow === '0' ? '否' : '是'}}</span>
					</template>
				</el-table-column>
				<el-table-column prop="isShow" label="摘要长度" align="center" width="120">
                    <template slot-scope="scope">
						<el-input-number v-if="isEdit" v-model="scope.row.summaryLength" controls-position="right" :min="1" :max="10000"></el-input-number>
                        <span v-if="!isEdit">{{ scope.row.summaryLength }}</span>
                    </template>
				</el-table-column>
                <el-table-column label="操作" width="140" v-if="isEdit" align="left">
                    <template slot-scope="scope">
                        <i v-if="scope.row.parameterType=='列表' || scope.row.parameterType=='对象' || scope.row.analyzingType=='4'|| scope.row.analyzingType=='6'" class="icon iconfont pto-plus mr10 c-success cursor f-20"
                            @click.stop="addFieldAttr(scope.row)" title="新增"></i>
                        <i v-else class="icon iconfont mr30"></i>
                        <i class="icon iconfont pto-delete c-danger cursor f-20"  @click.stop="deleteFieldAttr(scope.row)" title="删除"></i>
                            <i class="el-icon-top" v-show="scope.row.indexNo.indexOf('.') === -1" style="cursor: pointer" @click="upRespondTable(scope.row)"></i>
                            <i class="el-icon-bottom" v-show="scope.row.indexNo.indexOf('.') === -1" style="cursor: pointer"  @click="downRespondTable(scope.row)"></i>
                    </template>
                </el-table-column>
                <!--<template slot="empty">
                        加上此行可以去除自带的暂无数据的行
                    </template>-->
            </el-table>
        </el-col>
        <el-col :span="24">
            <el-button class="cursor" v-if="isEdit" style="margin-top: 12px; width: 100%; color: #4477ee" plain @click.stop="addFieldAttr()"><i class="icon iconfont pto-plus"></i> 添加参数</el-button>
        </el-col>
    </el-row>
    <el-dialog
        title="属性配置"
        :visible.sync="attrDialogVisible"
        width="80%"
        :before-close="closeAttrDialog"
        append-to-body
        >
        <el-table :data="currentAttrList" border>
            <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
            <el-table-column label="KEY" width="200">
                <template slot-scope="scope1">
                    <el-input v-model="scope1.row.attrKey" placeholder="请输入KEY" size="small"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="会话语境" width="250">
                <template slot-scope="scope1">
                    <el-cascader
                        v-model="scope1.row.attrElCode"
                        :options="sessionContextVariableOptions"
                        :props="{ value: 'id', label: 'label', checkStrictly: 'true' }"
                        clearable
                        @change="handleChange2(scope1.row)"
                        filterable>
                    </el-cascader>
                </template>
            </el-table-column>
            <el-table-column label="默认值" width="200">
                <template slot-scope="scope1">
                    <el-input v-model="scope1.row.attrValue" placeholder="请输入默认值" size="small"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="说明" min-width="200">
                <template slot-scope="scope1">
                    <el-input v-model="scope1.row.attrDesc" placeholder="请输入说明" size="small"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope1">
                    <i class="icon iconfont pto-delete c-danger cursor f-20" @click="deleteSceneParam(scope1.$index, currentAttrList)" title="删除"></i>
                </template>
            </el-table-column>
        </el-table>
        <el-button style="margin-top: 10px; width: 100%" type="dashed" @click="addAttrItem">
            <i class="icon iconfont pto-plus"></i> 添加属性
        </el-button>
        <div slot="footer">
            <el-button @click="closeAttrDialog">取消</el-button>
            <el-button type="primary" @click="saveAttrConfig">确定</el-button>
        </div>
    </el-dialog>
</div>
</template>
<script>
import { Validator } from '../../common/validate/validate'
import { getTreePathList, guid } from '@/libs/tools.js'
import operatiMaintBehaviorLibraryManageApi from '../../operatiMaintBehaviorLibraryManage/operatiMaintBehaviorLibraryManageApi'
export default {
    props: {
        isEdit: Boolean,
        respondTable: Array,
        sessionContextVariableOptions: {
            type: Array,
            default() {
                return []
            }
        },
        operatiMaintBehaviorLibraryOptions: {
            type: Array,
            default() {
                return []
            }
        },
        analyzingTypeOptions: {
            type: Array,
            default() {
                return []
            }
        }
    },
    data() {
        return {
            isRequiredList: [
                { label: '是', value: '是' },
                { label: '否', value: '否' }
            ],
            options: [
                {
                    codeName: '否',
                    codeValue: '0'
                },
                {
                    codeName: '是',
                    codeValue: '1'
                }
            ],
            showPopupOptions: [
				{
                    codeName: '是',
                    codeValue: '1'
                },
                {
                    codeName: '否',
                    codeValue: '0'
                }
            ],
            attrTree: [],
            reqBodySize: 0,
            allDictionary: {
                parameterType: [
                    { label: '列表', value: '列表' },
                    { label: '对象', value: '对象' },
                    { label: '字符串', value: '字符串' },
                    { label: '布尔值', value: '布尔值' },
                    { label: '数值', value: '数值' }
                ],
                inputWay: [
                    { label: '请求体', value: 'body' },
                    { label: '路径参数', value: 'path' },
                    { label: 'URL参数', value: 'query' }
                ]
            },
            rules: {
                name: [{ name: 'required', message: '属性名称必填' }],
                outParam: [
                    { name: 'required', message: '属性编码必填' },
                    { name: 'pattern', pattern: '[a-zA-Z][a-zA-Z0-9_]*', message: '属性编码只能输入英文、数字、下划线，只能英文开头' }
                ],
                parameterType: [{ name: 'required', message: '属性类型必填' }]
            },
            attrDialogVisible: false,
            currentAttrList: [],
            currentRow: null,
            parameterType: null
        }
    },
    created() {
        this.initAttrTree(this.respondTable)
    },
    methods: {
        async handleChange(val) {
            if (val.variableName.length === 1) {
                for (let item of this.sessionContextVariableOptions) {
                    if (item.id === val.variableName[0]) {
                        val.variableCombinedCode = item.value
                    }
                }
            } else if (val.variableName.length === 2) {
                let value = ''
                let value1 = ''
                for (let item of this.sessionContextVariableOptions) {
                    if (item.id === val.variableName[0]) {
                        value = item.value
                    }
                    for (let child of item.children) {
                        if (child.id === val.variableName[1]) {
                            value1 = child.value
                        }
                    }
                }
                val.variableCombinedCode = value + '.' + value1
            } else {
                val.variableCombinedCode = ''
            }
            val.diBehaviorSessionVariableId = val ? val.variableName[0] : ''
            val.diBehaviorVariableId = val ? val.variableName[1] : ''
            if (!val.behaviorObjNameArr) {
                let param = {
                    diBehaviorVariableId: val.diBehaviorVariableId
                }
                let res = await operatiMaintBehaviorLibraryManageApi.generateBehaviorVariableBySessionVariableId(param)
                if (res.success) {
                    if (res.body.diBehaviorCode) {
                        this.$nextTick(() => {
                            this.$set(val, 'behaviorObjNameArr', [res.body.diBehaviorCode, res.body.propertyCode])
                        })
                    }
                }
            }
        },
        // 会话语境
        handleChange2(val) {
            if (val.attrElCode.length === 1) {
                for (let item of this.sessionContextVariableOptions) {
                    if (item.id === val.attrElCode[0]) {
                        val.attrEl = item.value
                    }
                }
            } else if (val.attrElCode.length === 2) {
                let value = ''
                let value1 = ''
                for (let item of this.sessionContextVariableOptions) {
                    if (item.id === val.attrElCode[0]) {
                        value = item.value
                    }
                    for (let child of item.children) {
                        if (child.id === val.attrElCode[1]) {
                            value1 = child.value
                        }
                    }
                }
                val.attrEl = value + '.' + value1
            } else {
                val.attrEl = ''
            }
        },
        handleObjCascadeChange(val) {
            this.$set(val, 'behaviorObjName', val.behaviorObjNameArr.toString())
        },
        // 验证姓名是否合法只能输入字母、数字、中间可以使用短横线、下划线；
        checkNameComon(e) {
            let reg = /[a-zA-Z0-9][a-zA-Z0-9_-]*/g /// 1[34578]\d{9}/g
            var value = e.target.value.match(/[a-zA-Z0-9][a-zA-Z0-9:_-]*/g)
            if (value == null || value == '') {
                return ''
            } else {
                return value.join('')
            }
        },
        setReqBodySize(e) {
            this.reqBodySize = e.rowIndex + 1
        },
        transform(value, list, label) {
            if (!value) return ''
            if (list && list.length) {
                let data = list.find((item) => {
                    return item.value === value
                })
                if (data) {
                    if (label) {
                        return data[label]
                    } else {
                        return data.label
                    }
                }
            } else {
                return value
            }
        },
        // 请求参数类型变更时清除子项内容
        // flag ==1 请求参数
        fieldTypeChange(row, flag) {
            if (flag == 2) { // 字符串类型 解析类型切换
                if (row.analyzingType == '4' || row.analyzingType == '6') {
                    this.$set(row, 'attrList', [])
                    // 如果已经包含attrList则删除attrList
                    if (row.children) {
                        delete row.children
                    }
                } else {
                    // 如果已经包含attrList则删除attrList
                    if (row.attrList) {
                        delete row.attrList
                    }
                }
            }
            // object或array
            if (row.parameterType != '对象' && row.parameterType != '列表') {
                this.$set(row, 'children', [])
                delete row.children
                this.$set(row, 'outValue', '')
            } else {
                if (flag == 1) {
                    // delete row.outValue
                    this.$set(row, 'children', [])
                    // 如果已经包含attrList则删除attrList
                    if (row.attrList) {
                        delete row.attrList
                    }
                }
            }
        },
        formatIndex(row, column, cellValue, $index) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            var index = ''
            // for (var idx in fieldPath) {
            //     index += index == '' ? '' : '.'
            //     index += fieldPath[idx].indexNo
            // }
            Object.keys(fieldPath).forEach((key) => {
                index += index == '' ? '' : '.'
                index += fieldPath[key].indexNo
            })
            row.indexNo = index
            if (row.children && row.children.length > 0) {
                return index
            } else {
                if (fieldPath.length == 1) {
                    return "<span style='padding-left: 0px;'>" + index + '</span>'
                } else {
                    return "<span style='padding-left: 0px;'>" + index + '</span>'
                }
            }
        },
        formatIndexNum(row, column, cellValue, $index) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            var index = ''
            for (var idx in fieldPath) {
                index += index == '' ? '' : '.'
                index += fieldPath[idx].indexNo
            }
            return index.indexOf('.') == -1
        },
        addFieldAttr(row) {
            this.parameterType = true
            let child = {
                outValue: '',
                outParam: '',
                isShow: '0',
                parameterType: '字符串',
                id: guid(),
                isRequired: this.isRequiredList[0].value,
                description: '',
                remark: '',
                showPopup: '0'
                // position: this.allDictionary.inputWay[0].value
            }
            if (row == null) {
                this.attrTree.push(child)
            } else {
                if (row.parameterType == '列表' || row.parameterType == '对象') {
                    if (row.children == null || row.children.length === 0) {
                        this.$set(row, 'children', [])
                    }
                    row.children.push(child)
                    // 判断当前行是否已展开
                    const expandedRows = this.$refs.fieldAttrTable.store.states.expandRows
                    const isExpanded = expandedRows && expandedRows.includes(row)

                    // 如果未展开，则展开
                    if (!isExpanded) {
                        this.$nextTick(() => {
                            this.$refs.fieldAttrTable.toggleRowExpansion(row, true)
                        })
                    }
                } else {
                    this.parameterType = false
                    // 打开弹框进行属性配置
                    this.openAttrDialog(row)
                }
            }
        },
        moveFieldAttr(row, flag) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            if (fieldPath.length > 0) {
                var parentList = fieldPath.length > 1 ? fieldPath[fieldPath.length - 2].node.children : this.attrTree
                var index = fieldPath[fieldPath.length - 1].indexNo - 1
                if (flag == 'up') {
                    if (index > 0) {
                        var obj1 = parentList[index]
                        var obj2 = parentList[index - 1]
                        parentList.splice(index - 1, 1, obj1)
                        parentList.splice(index, 1, obj2)
                    }
                } else {
                    if (index < parentList.length - 1) {
                        var obj1 = parentList[index]
                        var obj2 = parentList[index + 1]
                        parentList.splice(index + 1, 1, obj1)
                        parentList.splice(index, 1, obj2)
                    }
                }
            }
        },
        deleteFieldAttr(row) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            if (fieldPath.length > 0) {
                var message = row.children && row.children.length > 0 ? '确认移除该参数及其子参数吗？' : '确认移除该参数吗？'
                this.$confirm(message, '提示', {
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    type: 'info'
                }).then(() => {
                    if (fieldPath.length > 1) {
                        fieldPath[fieldPath.length - 2].node.children.splice(fieldPath[fieldPath.length - 1].indexNo - 1, 1)
                    } else if (fieldPath.length > 0) {
                        this.attrTree.splice(fieldPath[0].indexNo - 1, 1)
                    }
                })
            }
        },
        upRespondTable(row) {
            if (parseInt(row.indexNo) === 1) {
                this.$message.error('此列暂无法向上调整顺序')
                return
            }
            this.$emit('changeUpRespondTable', parseInt(row.indexNo) - 1)
        },
        downRespondTable(row) {
            if (parseInt(row.indexNo) === this.respondTable.length) {
                this.$message.error('此列暂无法向下调整顺序')
                return
            }
            this.$emit('changeDownRespondTable', row.indexNo - 1)
        },
        validate() {
            var validator = new Validator(this.rules)
            var promiseList = this.recursiveAttrTree(this.attrTree, validator)
            return promiseList
        },
        recursiveAttrTree(list, validator) {
            var promiseList = []
            for (var idx in list) {
                const { children } = list[idx]
                var promise = validator.validate(list[idx])
                promiseList.push(promise)
                if (children && children.length) {
                    const res = this.recursiveAttrTree(children, validator)
                    if (res) {
                        promiseList.push(res)
                    }
                }
            }
            return promiseList.flat()
        },
        initAttrTree(dataList) {
            // Y的头晕晕的递归不写了，直接这一种将ID干进去
            for (let i = 0; i < dataList.length; i++) {
                dataList[i].id = guid()
                if (!dataList[i].parameterType) {
                    this.$set(dataList[i], 'parameterType', '字符串')
                }
                if (dataList[i].children != null) {
                    this.initAttrTree(dataList[i].children)
                }
            }
            this.attrTree = dataList
        },
        // 如果值为空则返回--
        fmatValueNull(value, flag) {
            if (flag) {
                return value
            }
            if (value == null) {
                return '--'
            } else {
                value = value
                    .toString()
                    .replace(/^\s\s*/, '')
                    .replace(/\s\s*$/, '')
                if (value.length == 0) {
                    return '--'
                }
                return value
            }
        },
        deleteSceneParam(index, children) {
            children.splice(index, 1)
            // 重新设置索引
            children.forEach((item, idx) => {
                item.index = idx + 1
            })
        },
        openAttrDialog(row) {
            this.currentRow = row
            // 如果有现有数据则回填，否则创建空数组
            if (row.attrList && row.attrList.length > 0) {
                this.currentAttrList = JSON.parse(JSON.stringify(row.attrList))
            } else {
                this.currentAttrList = []
            }
            this.attrDialogVisible = true
        },
        closeAttrDialog() {
            this.attrDialogVisible = false
            this.currentAttrList = []
            this.currentRow = null
        },
        saveAttrConfig() {
            if (this.currentRow) {
                this.$set(this.currentRow, 'attrList', this.currentAttrList)
            }
            this.closeAttrDialog()
        },
        addAttrItem() {
            this.currentAttrList.push({
                index: this.currentAttrList.length + 1,
                attrKey: '',
                attrElCode: '',
                attrValue: '',
                attrDesc: '',
                id: guid()
            })
        }
    },
    watch: {
        attrTree: {
            handler(newValue, oldValue) {
                this.$emit('field-attrs-changed', this.attrTree)
            },
            deep: true
        }
    }
}
</script>
<style lang="scss" scoped type="text/css">
@import 'oss-common/theme/const-theme.scss';
.c-success {
    color: $--color-success;
}
.c-primary {
    color: $--color-primary;
}
.c-danger {
    color: $--color-danger;
}
/deep/ .el-input-number{
	width: 100%;
}
// /deep/ .el-table__expand-icon {
//   display: none !important;
// }

/deep/ .el-table__expanded-cell{
    height: auto !important;
    padding: 0 !important;
}
/deep/ .param-table{
    padding: 10px 120px;
}

</style>
