<template>
    <el-row>
        <el-col :span="24">
            <el-table
                ref="fieldAttrTable"
                :data="attrTree"
                style="width: 100%"
                :indent="8"
                border
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                :row-key="'id'"
                :default-expand-all="true"
                :row-class-name="setReqBodySize"
            >
                <el-table-column prop="index" label="序号" width="80" align="center">
                    <template slot-scope="scope">
                        <span v-html="formatIndex(scope.row)"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="headerKey" label="参数中文名" width="150" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-input v-if="isEdit" v-model="scope.row.cnName"></el-input>
                        <span v-if="!isEdit">{{ scope.row.cnName }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="参数编码" width="150" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <!-- <el-input
                            v-if="isEdit"
                            v-model="scope.row.inputParam"
                            @blur="scope.row.inputParam = checkNameComon($event)"
                            maxlength="50"
                            placeholder="只能输入字母或数字与字母组合、中间可以使用短横线、下划线"
                            title="只能输入字母或数字与字母组合、中间可以使用短横线、下划线"
                        ></el-input> -->
                        <span>{{ scope.row.inputParam }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="参数类型" width="100" align="center">
                    <template slot-scope="scope">
                        <!-- <el-select size="mini" v-if="isEdit" v-model="scope.row.parameterType" @change="fieldTypeChange(scope.row, 1)">
                            <el-option v-for="(item, index) in allDictionary.parameterType" :key="index" :label="item.label" :value="item.value"></el-option>
                        </el-select> -->
                        <span >{{ transform(scope.row.parameterType, allDictionary.parameterType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="必填" width="80" align="center">
                        <template slot-scope="scope">
                            <!-- <el-select :filterable="true"    width="120px" v-model="scope.row.isRequired"
                                       size="mini" v-if="isEdit">
                                <el-option v-for="(item,index) in isRequiredList" :key="index" :label="item.label"
                                           :value="item.value"></el-option>
                            </el-select> -->
                            <span>{{scope.row.isRequired}}</span>
                        </template>
                    </el-table-column>
                <el-table-column prop="desc" label="参数默认值" width="180" align="center" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <el-input v-if="isEdit" :disabled="scope.row.parameterType=='对象' || scope.row.parameterType=='列表'"  maxlength="2000" v-model="scope.row.value"></el-input>
                            <span v-if="!isEdit">{{ scope.row.value }}</span>
                        </template>
                    </el-table-column>
                <el-table-column prop="description" label="参数说明" width="200" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-input v-if="isEdit" maxlength="200" v-model="scope.row.description"></el-input>
                        <span v-if="!isEdit">{{ scope.row.description }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="description" label="会话语境" width="220" align="center">
                    <template slot-scope="scope">
                        <el-cascader
                            v-model="scope.row.variableName"
                            ref="cascaderAddr"
                            clearable
                            :options="sessionContextVariableOptions"
                            :props="{value: 'id', label: 'label', checkStrictly: 'true'}"
                            :disabled="!isEdit"
                            @change="handleChange(scope.row)" filterable></el-cascader>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="150" align="center" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <el-input v-if="isEdit" maxlength="200" v-model="scope.row.remark"></el-input>
                            <span v-else>{{scope.row.remark}}</span>
                        </template>
                    </el-table-column>
                <!-- <el-table-column label="操作" width="140" v-if="isEdit" align="left">
                        <template slot-scope="scope">
                            <i v-if="scope.row.parameterType=='列表' || scope.row.parameterType=='对象'" class="icon iconfont pto-plus mr10 c-success cursor f-20"
                               @click.stop="addFieldAttr(scope.row)" title="新增"></i>
                            <i v-else class="icon iconfont mr30"></i>
                            <i class="icon iconfont pto-delete c-danger cursor f-20"  @click.stop="deleteFieldAttr(scope.row)" title="删除"></i>
                        </template>
                    </el-table-column> -->
                <!--<template slot="empty">
                        加上此行可以去除自带的暂无数据的行
                    </template>-->
            </el-table>
        </el-col>
        <!-- <el-col :span="24" v-if="isEdit">
            <el-button class="cursor" style="margin-top: 12px; width: 100%; color: #4477ee" plain @click.stop="addFieldAttr()"><i class="icon iconfont pto-plus"></i> 添加参数</el-button>
        </el-col> -->
    </el-row>
</template>
<script>
import { Validator } from '../../common/validate/validate'
import { getTreePathList, guid } from '@/libs/tools.js'
export default {
    props: {
        isEdit: Boolean,
        requestTable: Array,
        sessionContextVariableOptions: {
            type: Array,
            default() {
                return []
            }
        }
    },
    data() {
        return {
            isRequiredList: [
                { label: '是', value: '是' },
                { label: '否', value: '否' }
            ],
            attrTree: [],
            reqBodySize: 0,
            allDictionary: {
                parameterType: [
                    { label: '列表', value: '列表' },
                    { label: '对象', value: '对象' },
                    { label: '表达式', value: '表达式' }
                ],
                inputWay: [
                    { label: '请求体', value: 'body' },
                    { label: '路径参数', value: 'path' },
                    { label: 'URL参数', value: 'query' }
                ]
            },
            rules: {
                name: [{ name: 'required', message: '属性名称必填' }],
                inputParam: [
                    { name: 'required', message: '属性编码必填' },
                    { name: 'pattern', pattern: '[a-zA-Z][a-zA-Z0-9_]*', message: '属性编码只能输入英文、数字、下划线，只能英文开头' }
                ],
                parameterType: [{ name: 'required', message: '属性类型必填' }]
            }
        }
    },
    created() {
        this.initAttrTree(this.requestTable)
    },
    methods: {
        handleChange(val) {
            if (val.variableName.length === 1) {
                for (let item of this.sessionContextVariableOptions) {
                    if (item.id === val.variableName[0]) {
                        val.variableCombinedCode = item.value
                    }
                }
            } else if (val.variableName.length === 2) {
                let value = ''
                let value1 = ''
                for (let item of this.sessionContextVariableOptions) {
                    if (item.id === val.variableName[0]) {
                        value = item.value
                    }
                    for (let child of item.children) {
                        if (child.id === val.variableName[1]) {
                            value1 = child.value
                        }
                    }
                }
                val.variableCombinedCode = value + '.' + value1
            } else {
                val.variableCombinedCode = ''
            }
            val.diBehaviorSessionVariableId = val ? val.variableName[0] : ''
            val.diBehaviorVariableId = val ? val.variableName[1] : ''
        },
        // 验证姓名是否合法只能输入字母、数字、中间可以使用短横线、下划线；
        checkNameComon(e) {
            let reg = /[a-zA-Z0-9][a-zA-Z0-9_-]*/g /// 1[34578]\d{9}/g
            var value = e.target.value.match(/[a-zA-Z0-9][a-zA-Z0-9:_-]*/g)
            if (value == null || value == '') {
                return ''
            } else {
                return value.join('')
            }
        },
        setReqBodySize(e) {
            this.reqBodySize = e.rowIndex + 1
        },
        transform(value, list, label) {
            if (!value) return ''
            if (list && list.length) {
                let data = list.find((item) => {
                    return item.value === value
                })
                if (data) {
                    if (label) {
                        return data[label]
                    } else {
                        return data.label
                    }
                }
            } else {
                return value
            }
        },
        // 请求参数类型变更时清除子项内容
        // flag ==1 请求参数
        fieldTypeChange(row, flag) {
            // object或array
            if (row.parameterType != '对象' && row.parameterType != '列表') {
                delete row.children
                this.$set(row, 'value', '')
            } else {
                if (flag == 1) {
                    delete row.value
                    this.$set(row, 'children', [])
                }
            }
        },
        formatIndex(row, column, cellValue, $index) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            var index = ''
            // for (var idx in fieldPath) {
            //     index += index == '' ? '' : '.'
            //     index += fieldPath[idx].indexNo
            // }
            Object.keys(fieldPath).forEach((key) => {
                index += index == '' ? '' : '.'
                index += fieldPath[key].indexNo
            })
            // row.indexNo = index
            if (row.children && row.children.length > 0) {
                return index
            } else {
                if (fieldPath.length == 1) {
                    return "<span style='padding-left: 0px;'>" + index + '</span>'
                } else {
                    return "<span style='padding-left: 0px;'>" + index + '</span>'
                }
            }
        },
        formatIndexNum(row, column, cellValue, $index) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            var index = ''
            for (var idx in fieldPath) {
                index += index == '' ? '' : '.'
                index += fieldPath[idx].indexNo
            }
            return index.indexOf('.') == -1
        },
        addFieldAttr(row) {
            let child = {
                value: '',
                inputParam: '',
                parameterType: '表达式',
                id: guid(),
                isRequired: this.isRequiredList[0].value,
                description: '',
                remark: ''
                // position: this.allDictionary.inputWay[0].value
            }
            if (row == null) {
                this.attrTree.push(child)
            } else {
                if (row.children == null || row.children.length === 0) {
                    this.$set(row, 'children', [])
                }
                row.children.push(child)
            }
            console.log(this.attrTree)
        },
        moveFieldAttr(row, flag) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            if (fieldPath.length > 0) {
                var parentList = fieldPath.length > 1 ? fieldPath[fieldPath.length - 2].node.children : this.attrTree
                var index = fieldPath[fieldPath.length - 1].indexNo - 1
                if (flag == 'up') {
                    if (index > 0) {
                        var obj1 = parentList[index]
                        var obj2 = parentList[index - 1]
                        parentList.splice(index - 1, 1, obj1)
                        parentList.splice(index, 1, obj2)
                    }
                } else {
                    if (index < parentList.length - 1) {
                        var obj1 = parentList[index]
                        var obj2 = parentList[index + 1]
                        parentList.splice(index + 1, 1, obj1)
                        parentList.splice(index, 1, obj2)
                    }
                }
            }
        },
        deleteFieldAttr(row) {
            var fieldPath = getTreePathList(this.attrTree, row.id, 'id')
            if (fieldPath.length > 0) {
                var message = row.children && row.children.length > 0 ? '确认移除该参数及其子参数吗？' : '确认移除该参数吗？'
                this.$confirm(message, '提示', {
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    type: 'info'
                }).then(() => {
                    if (fieldPath.length > 1) {
                        fieldPath[fieldPath.length - 2].node.children.splice(fieldPath[fieldPath.length - 1].indexNo - 1, 1)
                    } else if (fieldPath.length > 0) {
                        this.attrTree.splice(fieldPath[0].indexNo - 1, 1)
                    }
                })
            }
        },
        validate() {
            var validator = new Validator(this.rules)
            var promiseList = this.recursiveAttrTree(this.attrTree, validator)
            return promiseList
        },
        recursiveAttrTree(list, validator) {
            var promiseList = []
            for (var idx in list) {
                const { children } = list[idx]
                var promise = validator.validate(list[idx])
                promiseList.push(promise)
                if (children && children.length) {
                    const res = this.recursiveAttrTree(children, validator)
                    if (res) {
                        promiseList.push(res)
                    }
                }
            }
            return promiseList.flat()
        },
        initAttrTree(dataList) {
            // Y的头晕晕的递归不写了，直接这一种将ID干进去
            for (let i = 0; i < dataList.length; i++) {
                dataList[i].id = guid()
                if (!dataList[i].parameterType) {
                    this.$set(dataList[i], 'parameterType', '表达式')
                }
                if (dataList[i].children != null) {
                    this.initAttrTree(dataList[i].children)
                }
            }
            this.attrTree = dataList
        },
        // 如果值为空则返回--
        fmatValueNull(value, flag) {
            if (flag) {
                return value
            }
            if (value == null) {
                return '--'
            } else {
                value = value
                    .toString()
                    .replace(/^\s\s*/, '')
                    .replace(/\s\s*$/, '')
                if (value.length == 0) {
                    return '--'
                }
                return value
            }
        }
    },
    watch: {
        attrTree: {
            handler(newValue, oldValue) {
                this.$emit('field-attrs-changed', this.attrTree)
            },
            deep: true
        }
    }
}
</script>
<style lang="scss" scoped type="text/css">
@import 'oss-common/theme/const-theme.scss';
.c-success {
    color: $--color-success;
}
.c-primary {
    color: $--color-primary;
}
.c-danger {
    color: $--color-danger;
}
</style>
