import util from '@/libs/util.js'
import cndidsConfig from '../cndidsConfig'
const robotName = cndidsConfig.API_ROBOT_NAME
const commonName = cndidsConfig.API_COMMON_NAME
const axios = util.ajaxPostData
import axiosUtil from 'axios'
// 问题处理
export default {
	// 分页查询待处理问题列表
	queryPageInfo(param) {
		return axios(`${robotName}/api/v1/question/process/page`, param)
	},
	detail(param) {
		return util.ajaxGetData2(`${robotName}/api/v1/question/process/` + param.mtQuestionId)
	},
	process(param) {
		return axios(`${robotName}/api/v1/question/process/process`, param)
	},
	delete(param) {
		return axios(`${robotName}/api/v1/question/process/delete`, param)
	},
	// 下载文件
	downloadFile(params) {
		return axiosUtil.get(`${commonName}/api/dfs/downloadFile?fileName=` + params.resourceUrl, {
			responseType: "blob"
		}).then(res => {
			// 创建 blob 链接
			const blob = new Blob([res.data])
			const link = document.createElement("a")
			link.href = window.URL.createObjectURL(blob)
			// 文件名优先用参数，没有就用 fileUrl
			link.download = params.resourceName || decodeURIComponent(params.resourceUrl.split('/').pop())
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)
			window.URL.revokeObjectURL(link.href)
		})

	}
}
