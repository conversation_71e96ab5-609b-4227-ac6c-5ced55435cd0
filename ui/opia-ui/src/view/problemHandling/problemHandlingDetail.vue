<template>
    <el-dialog :title="isView ? '问题-查看' : '问题-处理'" :close-on-click-modal="false" :visible.sync="isShow" width="75%" :before-close="handleClose" style="margin-top: -7vh">
        <div class="dialog-flex">
            <div class="dialog-scroll-content">
                <FormDisplay label="内容描述" labelWidth="100">
                    <el-input style="width: 86%" type="textarea" v-model="info.questionDesc" disabled :rows="4" placeholder="请输入问题内容描述"></el-input>
                </FormDisplay>

                <FormDisplay label="" labelWidth="100">
                    <div class="attachment-list" style="width: 86%">
                        <div class="image-list">
                            <el-image
                                class="image-item"
                                v-for="(item, index) in imageList"
                                :key="index"
                                :src="'/portal-gateway/cn-dids-web/api/dfs/downloadFile?fileName=' + item.resourceUrl"
                                fit="cover"
                                :preview-src-list="imageList.map((img) => '/portal-gateway/cn-dids-web/api/dfs/downloadFile?fileName=' + item.resourceUrl)"
                                :initial-index="index"
                                style="cursor: pointer"
                            ></el-image>
                        </div>
                        <div class="file-list">
                            <div class="file-item" v-for="(item, index) in fileList" :key="index">
                                <i class="el-icon-document"></i>
                                <span class="name" @click="onDownload(item)">{{ item.resourceName }}</span>
                            </div>
                        </div>
                    </div>
                </FormDisplay>
                <FormDisplay label="问题归属" labelWidth="100">
                    <el-select style="width: 86%" disabled v-model="info.questionBelong" placeholder="请选择问题归属">
                        <el-option v-for="item in options.questionBelongOptions" :key="item.codeValue" :label="item.codeName" :value="item.codeValue"> </el-option>
                    </el-select>
                </FormDisplay>

                <FormDisplay label="问题类型" labelWidth="100">
                    <el-select disabled style="width: 86%" v-model="info.questionType" placeholder="请选择问题类型">
                        <el-option v-for="item in options.questionTypeOptions" :key="item.codeValue" :label="item.codeName" :value="item.codeValue"> </el-option>
                    </el-select>
                </FormDisplay>

                <div class="feedback-row" style="width: 86%">
                    <FormDisplay label="反馈人姓名:" labelWidth="100">
                        <el-input disabled v-model="info.feedbackUserName" placeholder="请输入反馈人姓名"></el-input>
                    </FormDisplay>
                    <FormDisplay label="反馈人电话:" labelWidth="100">
                        <el-input disabled v-model="info.feedbackUserPhone" placeholder="请输入反馈人电话"></el-input>
                    </FormDisplay>
                    <FormDisplay label="反馈时间:" labelWidth="100">
                        <el-input disabled v-model="info.feedbackTime" placeholder="请输入反馈时间"></el-input>
                    </FormDisplay>
                </div>
                <div class="feedback-row">
                    <FormDisplay label="处理结果:" labelWidth="100" :isRequired="true">
                        <el-select style="width: 86%" :disabled="isView" v-model="info.processStatus" placeholder="请选择处理结果">
                            <el-option v-for="item in options.processStatusOptions" :key="item.codeValue" :label="item.codeName" :value="item.codeValue"> </el-option>
                        </el-select>
                    </FormDisplay>
                </div>
                <div class="feedback-row">
                    <FormDisplay label="处理意见:" labelWidth="100" :isRequired="true">
                        <el-input style="width: 86%" :disabled="isView" type="textarea" v-model="info.processOpinion" :rows="3" placeholder="请输入处理意见"></el-input>
                    </FormDisplay>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" v-if="!isView" @click="onSubmit">确认</el-button>
                <el-button @click="handleClose" v-if="!isView">取消</el-button>
                <el-button @click="handleClose" v-if="isView">关闭</el-button>
            </span>
        </div>
    </el-dialog>
</template>

<script>
import FormDisplay from 'oss-common/components/FormDisplay'
import api from './problemHandlingApi.js'
export default {
    props: {
        isShow: {
            type: Boolean,
            default: false,
        },
        pData: {
            type: Object,
            default: () => ({}),
        },
        pType: {
            type: String,
            default: 'view',
        },
        options: {
            type: Object,
            default: () => null,
        },
    },
    components: {
        FormDisplay,
    },
    watch: {
        isShow(newVal) {
            if (newVal) {
                this.setInfo()
            }
        },
    },
    computed: {
        isView() {
            return this.pType === 'view'
        },
    },
    data() {
        return {
            problemBelong: '',
            problemType: '',
            imageList: [],
            fileList: [],
            feedbackName: '',
            feedbackPhone: '',
            feedbackTime: '',
            handleResult: '',
            handleComment: '',
            info: {
                mtQuestionId: null,
                questionDesc: '',
                questionBelong: '',
                questionType: '',
                feedbackUserName: '',
                feedbackUserPhone: '',
                feedbackTime: '',
                processStatus: '',
                processUserName: null,
                processTime: null,
                processOpinion: null,
                createdUserName: '',
                createdTime: '',
                updatedUserName: '',
                updatedTime: '',
                resourceList: [],
            },
        }
    },

    methods: {
        setInfo() {
            if (this.pData) {
                this.imageList = []
                this.fileList = []
                api.detail(this.pData)
                    .then((res) => {
                        if (res && res.success) {
                            Object.assign(this.info, res.body)
                            // 分类图片和文件
                            this.info.resourceList.forEach((item) => {
                                if (this.isUrlImage(item.resourceUrl)) {
                                    this.imageList.push(item)
                                } else {
                                    this.fileList.push(item)
                                }
                            })
                        } else {
                            this.$message.error('获取数据异常')
                        }
                    })
                    .catch((error) => {
                        this.$message.error('请求异常：' + error)
                    })
            }
        },
        handleClose() {
            this.$emit('changeDetailVisible')
        },
        isUrlImage(url) {
            // 常见图片后缀判断
            return /\.(jpg|jpeg|png|gif|bmp|webp|svg|apng)$/i.test(url)
        },
        onSubmit() {
            let param = {
                mtQuestionId: this.info.mtQuestionId,
                processOpinion: this.info.processOpinion,
                processStatus: this.info.processStatus,
            }
            api.process(param)
                .then((res) => {
                    if (res && res.success) {
                        this.$message.success(res.head.respMsg)
                        this.$emit('changeDetailVisible')
                    }
                })
                .catch((error) => {
                    this.$message.error('请求异常：' + error)
                })
        },
        onDownload(item) {
            api.downloadFile(item)
                .then((res) => {})
                .catch((error) => {
                    this.$message.error('请求异常：' + error)
                })
        },
    },
}
</script>
<style lang="scss" scoped>
.dialog-flex {
    display: flex;
    flex-direction: column;
    height: 500px;
}
.dialog-scroll-content {
    flex: 1 1 auto;
    overflow-y: auto;
}
.dialog-footer {
    flex-shrink: 0;
    padding: 16px 0 0 0;
    background: #fff;
    text-align: center;
}
.attachment-list {
    .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 10px;

        .image-item {
            width: 100px;
            height: 100px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            overflow: hidden;

            .el-image {
                width: 100%;
                height: 100%;
            }
        }
    }

    .file-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .file-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-bottom: 0;
            background: #fafbfc;
            .name {
                text-decoration: underline;
                cursor: pointer;
                &:active {
                    opacity: 0.7;
                }
            }
        }
    }
}
.feedback-row {
    display: flex;
    gap: 24px;
    margin-bottom: 12px;
}
</style>
