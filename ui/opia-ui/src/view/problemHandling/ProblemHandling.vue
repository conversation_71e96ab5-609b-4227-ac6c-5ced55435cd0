<!--
    文件用途: 问题处理
-->
<template>
    <div
        class="basic-search fullContent mtb10 mlr10 problemHanding"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
    >
        <Layout
            :total="totalCount"
            placeholder="关键词搜索"
            :isToggleSearch="true"
            @setHeight="setTableHeight"
            @keyWordSearch="keyWordSearch"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
            ref="layout"
        >
            <template slot="btnSort">
                <el-button @click="del('batch')">批量删除</el-button>
            </template>
            <template slot="expandSearch">
                <!-- 条件搜索 -->
                <FormGenerate ref="form" @searchResult="searchResult" @reset="resetResult" :renderFormRule="renderFormRule" :span="8" :searchGroup="true" :lableWidth="150" @changeName="changeName" />
            </template>
            <template slot="mainContent">
                <el-table
                    border
                    ref="filterTable"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                    :data="tableData"
                    style="width: 100%"
                    tooltip-effect="dark"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                >
                    <template slot="empty">
                        <p>{{ dataText }}</p>
                    </template>
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column label="问题归属" prop="questionBelong" align="center" width="170">
                        <template #default="scope">
                            {{ questionBelongMapper[scope.row.questionBelong] }}
                        </template>
                    </el-table-column>
                    <el-table-column label="问题类型" prop="questionType" show-overflow-tooltip align="center" width="120">
                        <template #default="scope">
                            {{ questionTypeMapper[scope.row.questionType] }}
                        </template>
                    </el-table-column>
                    <el-table-column label="问题描述" prop="questionDesc" show-overflow-tooltip align="center" width="270" />
                    <el-table-column label="反馈人姓名" prop="feedbackUserName" align="center" width="120" />
                    <el-table-column label="反馈人电话" prop="feedbackUserPhone" align="center" width="140" />
                    <el-table-column label="反馈时间" prop="feedbackTime" align="center" width="220" />
                    <el-table-column label="处理状态" prop="processStatus" width="120" align="center">
                        <template #default="scope">
                            {{ processStatusMapper[scope.row.processStatus] }}
                        </template>
                    </el-table-column>
                    <el-table-column label="处理人" prop="processUserName" width="120" align="center" />
                    <el-table-column label="处理时间" prop="processTime" show-overflow-tooltip align="center" width="220" />
                    <el-table-column label="处理意见" prop="processOpinion" show-overflow-tooltip align="center" min-width="170" />
                    <el-table-column label="操作" width="180" align="center" fixed="right">
                        <template slot-scope="scope">
                            <el-button type="text" @click="view(scope.row)">查看</el-button>
                            <el-button type="text" @click="handle(scope.row)">处理</el-button>
                            <el-button type="text" @click="del('one', scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </Layout>

        <ProblemHandlingDetail :isShow="showDetail" @changeDetailVisible="onDetailClose" :pData="currentInfo" :pType="pType" :options="allOptions" />
    </div>
</template>

<script>
import Layout from 'oss-common/components/Layout'
import OssIcon from 'oss-common/components/OssIcon'
import FormGenerate from 'oss-common/components/FormGenerate'
import api from './problemHandlingApi.js'
import commonApi from '../common/commonApi.js'
import ProblemHandlingDetail from './problemHandlingDetail.vue'
export default {
    components: {
        Layout,
        OssIcon,
        FormGenerate,
        ProblemHandlingDetail,
    },
    data() {
        return {
            searchParam: {
                feedbackTime: [],
                feedbackEndTime: '',
                feedbackStartTime: '',
                feedbackUserName: '',
                feedbackUserPhone: '',
                keyWord: '',
                pageNo: 1,
                pageSize: 20,
                processStatus: '',
                questionBelong: '',
                questionDesc: '',
                questionType: '',
            },
            loading: false,
            tableHeight: '200',
            renderFormRule: {
                questionDesc: {
                    label: '问题描述',
                    value: null,
                    type: 'input',
                    placeholder: '请输入要查询的主题',
                },
                questionBelong: {
                    label: '问题归属',
                    value: null,
                    type: 'select',
                    filterable: true,
                    placeholder: this.$t('cndids.choosePlaceholder'),
                    option: [],
                    // labelName: 'codeName',
                    // valueKey: 'codeValue',
                },
                questionType: {
                    label: '问题类型',
                    value: null,
                    type: 'select',
                    filterable: true,
                    placeholder: this.$t('cndids.choosePlaceholder'),
                    option: [],
                    // labelName: 'codeName',
                    // valueKey: 'codeValue',
                },
                feedbackUserName: {
                    label: '反馈人姓名',
                    value: null,
                    type: 'input',
                    placeholder: '请输入要查询的反馈人姓名',
                },
                feedbackUserPhone: {
                    label: '反馈人电话',
                    value: null,
                    type: 'input',
                    placeholder: '请输入要查询的反馈人电话',
                },
                feedbackTime: {
                    label: '反馈时间',
                    value: [],
                    type: 'datepicker',
                    format: 'yyyy-MM-dd HH:mm:ss',
                },
                processStatus: {
                    label: '处理状态',
                    value: null,
                    type: 'select',
                    filterable: true,
                    placeholder: this.$t('cndids.choosePlaceholder'),
                    option: [],
                    // labelName: 'codeName',
                    // valueKey: 'codeValue',
                },
            },
            dataText: '',
            totalCount: 0,
            tableData: [],
            selectedIds: [],
            showDetail: false,
            currentInfo: {},
            pType: '',
            questionBelongMapper: {},
            processStatusMapper: {},
            questionTypeMapper: {},
            allOptions: {},
        }
    },
    created() {},
    mounted() {
        this.getAllDictionary()
        this.queryPageInfo()
    },
    methods: {
        view(row) {
            this.currentInfo = row
            this.pType = 'view'
            this.showDetail = true
        },
        handle(row) {
            this.pType = 'edit'
            this.currentInfo = row
            this.showDetail = true
        },
        del(type, row) {
            let param = {}
            if (type === 'batch') {
                if (this.selectedIds.length > 0) {
                    param = {
                        mtQuestionIdList: this.selectedIds,
                    }
                } else {
                    this.$message.warning(this.$t('cndids.pleaseSelectTheDataYouWantToDelete'))
                    return
                }
            } else {
                param = {
                    mtQuestionIdList: [row.mtQuestionId],
                }
            }

            this.$confirm(this.$t('cndids.confirmDeletion'), this.$t('cndids.prompt'), {
                confirmButtonText: this.$t('cndids.confirm'),
                cancelButtonText: this.$t('cndids.cancel'),
                type: 'warning',
            })
                .then(async (_) => {
                    let res = await api.delete(param)
                    if (res.success) {
                        this.$message.success(res.head.respMsg)
                        this.queryPageInfo()
                    } else {
                        this.$message.error(res.head.respMsg)
                    }
                })
                .catch(() => {})
        },
        changeName(val) {
            console.log(val)
            this.searchResult(val.searchResult)
        },
        // 获取字典值
        async getAllDictionary() {
            let param = ['process_status', 'question_belong', 'question_type']
            let res = await commonApi.getAllDictionary(param)
            if (res.success) {
                // processStatusOptions  questionBelongMapper  questionBelongMapper questionTypeMapper
                let processStatusOptions = res.body.process_status || []
                this.renderFormRule.processStatus.option = processStatusOptions.map((item) => {
                    return { label: item.codeName, value: item.codeValue }
                })
                let questionBelongOptions = res.body.question_belong || []
                this.renderFormRule.questionBelong.option = questionBelongOptions.map((item) => {
                    return { label: item.codeName, value: item.codeValue }
                })
                let questionTypeOptions = res.body.question_type || []
                this.renderFormRule.questionType.option = questionTypeOptions.map((item) => {
                    return { label: item.codeName, value: item.codeValue }
                })
                for (let item of processStatusOptions) {
                    this.processStatusMapper[item.codeValue] = item.codeName
                }
                for (let item of questionBelongOptions) {
                    this.questionBelongMapper[item.codeValue] = item.codeName
                }
                for (let item of questionTypeOptions) {
                    this.questionTypeMapper[item.codeValue] = item.codeName
                }

                this.allOptions = {
                    processStatusOptions: processStatusOptions,
                    questionBelongOptions: questionBelongOptions,
                    questionTypeOptions: questionTypeOptions,
                }
            }
        },
        handleSelectionChange(val) {
            console.log(val)
            this.selectedIds = val.map((item) => item.mtQuestionId)
        },
        resetResult(obj) {
            this.searchParam.keyWord = ''
            this.$refs.layout.keyWord = ''
            this.searchParam.pageNo = 1
            this.$refs.layout.currentPage = 1
            this.searchResult(obj)
        },
        searchResult(val) {
            this.searchParam.pageNo = 1
            this.$refs.layout.currentPage = 1
            this.searchParam.feedbackTime = val.feedbackTime
            this.searchParam.feedbackEndTime = val.feedbackTime.length > 0 ? val.feedbackTime[1] : ''
            this.searchParam.feedbackStartTime = val.feedbackTime.length > 0 ? val.feedbackTime[0] : ''
            this.searchParam.feedbackUserName = val.feedbackUserName
            this.searchParam.feedbackUserPhone = val.feedbackUserPhone
            this.searchParam.processStatus = val.processStatus
            this.searchParam.questionBelong = val.questionBelong
            this.searchParam.questionDesc = val.questionDesc
            this.searchParam.questionType = val.questionType
            this.queryPageInfo()
        },
        async queryPageInfo() {
            this.loading = true
            this.dataText = ''
            try {
                let res = await api.queryPageInfo(this.searchParam)
                this.loading = false
                if (res.success) {
                    if (res.body.list.length === 0) {
                        this.dataText = '暂无数据'
                    }
                    this.tableData = res.body.list ? res.body.list : []
                    this.totalCount = res.body.total ? res.body.total : 0
                    this.loading = false
                } else {
                    this.$message.error(res.head.respMsg)
                }
            } catch (e) {
                this.$message.error('请求异常：' + e)
                this.loading = false
            }
        },
        handleSizeChange(val) {
            this.searchParam.pageNo = 1
            this.$refs.layout.currentPage = 1
            this.searchParam.pageSize = val
            this.queryPageInfo()
        },
        // 分页查询
        handleCurrentChange(val) {
            this.searchParam.pageNo = val
            this.queryPageInfo()
        },
        // 关键字查询
        keyWordSearch(val) {
            this.searchParam.pageNo = 1
            this.$refs.layout.currentPage = 1
            this.searchParam.keyWord = val
            this.queryPageInfo()
        },
        // 设置表格高度
        setTableHeight(height) {
            this.$nextTick(() => {
                console.log('height', height)
                this.tableHeight = height - 10
            })
        },
        onDetailClose() {
            this.showDetail = false
            this.queryPageInfo()
        },
    },
}
</script>

<style lang="scss" scoped>
.problemHanding {
}
@media screen and (min-width: 1921px) {
    .el-button {
        user-select: unset;
        font-size: 18px;
    }
    .el-table {
        font-size: 18px;
    }
}
>>> .oss-form-generate .el-form-item__content {
    margin-top: 5px !important;
}
>>> .el-form-item__label {
    font-size: 16px;
}
</style>
