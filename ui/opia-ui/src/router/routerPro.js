export var routerPro = [
    {
        path: '/operatiMaintRobotHome',
        name: 'OperatiMaintRobotHome',
        component: () => import('@/view/operatiMaintRobotHome/OperatiMaintRobotHome.vue'),
        meta: {
            title: '机器人首页'
        }
    },
    {
        path: '/robotMaintenance',
        name: 'RobotMaintenance',
        component: () => import('@/view/robotMaintenance/RobotMaintenance.vue'),
        meta: {
            title: '智能体维护'
        }
    },
    {
        path: '/configMgt',
        name: 'configMgt',
        component: () => import('@/view/configMgt/configMgt.vue'),
        meta: {
            title: '接口配置管理'
        }
    },
    {
        path: '/sessionScenarioMaintenance',
        name: 'SessionScenarioMaintenance',
        component: () => import('@/view/sessionScenarioMaintenance/SessionScenarioMaintenance.vue'),
        meta: {
            title: '会话场景维护'
        }
    },
    {
        path: '/configurationCenter',
        name: 'ConfigurationCenter',
        component: () => import('@/view/configurationCenter/ConfigurationCenter.vue'),
        meta: {
            title: '灵犀助手配置中心'
        }
    },
    {
        path: '/sessionMessageClickAnalysis',
        name: 'SessionMessageClickAnalysis',
        component: () => import('@/view/sessionMessageClickAnalysis/SessionMessageClickAnalysis.vue'),
        meta: {
            title: '会话消息赞踩分析'
        }
    },
    {
        path: '/sessionScenarioTypeManage',
        name: 'SessionScenarioTypeManage',
        component: () => import('@/view/sessionScenarioTypeManage/SessionScenarioTypeManage.vue'),
        meta: {
            title: '会话场景类型管理'
        }
    },
    {
        path: '/operatiMaintBehaviorLibraryManage',
        name: 'OperatiMaintBehaviorLibraryManage',
        component: () => import('@/view/operatiMaintBehaviorLibraryManage/NewOperatiMaintBehaviorLibraryManage.vue'),
        meta: {
            title: '运维对象知识图谱'
        }
    },
    {
        path: '/sessionContextVariableManage',
        name: 'SessionContextVariableManage',
        component: () => import('@/view/sessionContextVariableManage/SessionContextVariableManage.vue'),
        meta: {
            title: '会话语境管理'
        }
    },
    {
        path: '/paramsTemplateManage',
        name: 'ParamsTemplateManage',
        component: () => import('@/view/paramsTemplateManage/ParamsTemplateManage.vue'),
        meta: {
            title: '参数模板'
        }
    },
    {
        path: '/permissionConfig',
        name: 'PermissionConfig',
        component: () => import('@/view/permissionConfig/PermissionConfig.vue'),
        meta: {
            title: '权限设置'
        }
    },
    {
        path: '/robotMaintenanceDetail',
        name: 'RobotMaintenanceDetail',
        component: () => import('@/view/robotMaintenance/robotMaintenanceDetail.vue'),
        meta: {
            title: '智能体维护'
        }
    },
    {
        path: '/sessionScenarioTypeManageDetail',
        name: 'SessionScenarioTypeManageDetail',
        component: () => import('@/view/sessionScenarioTypeManage/sessionScenarioTypeManageDetail.vue'),
        meta: {
            title: '会话场景类型详情'
        }
    },
    {
        path: '/sessionScenarioMaintenanceDetail',
        name: 'SessionScenarioMaintenanceDetail',
        component: () => import('@/view/sessionScenarioMaintenance/sessionScenarioMaintenanceDetail.vue'),
        meta: {
            title: '会话场景维护'
        }
    },
    {
        path: '/paramsTemplateManageDetail',
        name: 'ParamsTemplateManageDetail',
        component: () => import('@/view/paramsTemplateManage/paramsTemplateManageDetail.vue'),
        meta: {
            title: '参数模板详情'
        }
    },
    {
        path: '/executeService',
        name: 'ExecuteService',
        component: () => import('@/view/paramsTemplateManage/components/executeService.vue'),
        beforeEnter: (to, from, next) => {
            to.meta.title = '执行服务-' + to.query.name
            next()
        }
    },
    {
        path: '/operatiMaintBehaviorLibraryDetail',
        name: 'OperatiMaintBehaviorLibraryDetail',
        component: () => import('@/view/operatiMaintBehaviorLibraryManage/operatiMaintBehaviorLibraryDetail.vue'),
        meta: {
            title: '运维行为库'
        }
    },
    {
        path: '/operatiMaintBehaviorLibraryView',
        name: 'OperatiMaintBehaviorLibraryView',
        component: () => import('@/view/operatiMaintBehaviorLibraryManage/operatiMaintBehaviorLibraryView.vue'),
        meta: {
            title: '运维行为库'
        }
    },
    {
        path: '/sessionContextVariableDetail',
        name: 'SessionContextVariableDetail',
        component: () => import('@/view/sessionContextVariableManage/sessionContextVariableDetail.vue'),
        meta: {
            title: '会话语境'
        }
    },
    {
        path: '/problemHandling',
        name: 'ProblemHandling',
        component: () => import('@/view/problemHandling/ProblemHandling.vue'),
        meta: {
            title: '问题处理'
        }
    }
]
