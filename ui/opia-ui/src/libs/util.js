/* eslint-disable */
import axios from 'axios'

import env from '../../build/env'
import { vm } from '@/main.js'
import Cookies from 'js-cookie'
import { MD5 } from 'crypto-js'

let util = {}

util.permissionSwitch = true

util.title = function (title) {
    window.document.title = title
}

//密码加密公钥，为空时不加密
const rsaPubKey = ''
// 浏览器语言获取
const browserLanguage = localStorage.getItem('ELEMENT_LANGUAGE') || window.navigator.language || 'en-US'
util.browserLanguage = browserLanguage

util.getLocalUrl = window.location.protocol + '//' + window.location.host

util.getLocal = util.getLocalUrl + '/portal-gateway'
// util.getLocal = util.getLocalUrl

const ajaxUrl = process.env.SERVER_BASEURL ? process.env.SERVER_BASEURL : env === 'development' ? util.getLocal : env === 'production' ? util.getLocal + '/portal-gateway' : util.getLocal + '/portal-gateway'

const statisticUrl = process.env.STATISTIC_URL ? process.env.STATISTIC_URL : 'https://s.ctbiyi.com/img'

const userName = 'oss-vue-example'

util.baseUrl = ajaxUrl

util.ajax = axios.create({
    baseURL: ajaxUrl,
    timeout: 180000
})
util.ajax1 = axios.create({
    baseURL: util.getLocalUrl,
    timeout: 180000
})
let loading = null
let requestCount = 0
util.ajax.interceptors.request.use(
    function (config) {
        // 每请求一个接口，请求数量加一，打开遮罩
        if (config.config && config.config.showLoading) {
            requestCount++
            // loading = vm.$loading({
            //     fullscreen: true,
            //     lock: true,
            //     text: 'Loading',
            //     spinner: 'el-icon-loading',
            //     background: 'rgba(0, 0, 0, 0.3)'
            // })
        }

        config.headers.Authorization = localStorage.token
        return config
    },
    function (error) {
        // 请求失败的处理
        return Promise.reject(error)
    }
)

util.ajax.interceptors.response.use(
    function (res) {
        //在这里对返回的数据进行处理
        if (!res.config.url.includes('refreshToken')) {
            //记录ajax调用的时间
            sessionStorage.lastAjaxTime = new Date().getTime()
        }
        // 每请求成功一个接口，请求数量减一，数量为零关闭遮罩
        if (res.config.config && res.config.config.showLoading) {
            setTimeout(function () {
                requestCount--
                if (requestCount <= 0) {
                    // loading.close()
                }
            }, 10)
        }

        return res
    },
    function (error) {
        // 统一处理接口超时
        let originalRequest = error.config
        if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1 && !originalRequest._retry) {
            // eslint-disable-next-line
            //    loading.close()
            return Promise.reject('timeout')
        }

        // 每请求成功一个接口，请求数量减一，数量为零关闭遮罩
        if (error.response.config.config && error.response.config.config.showLoading) {
            setTimeout(function () {
                requestCount--
                if (requestCount <= 0) {
                    // loading.close()
                }
            }, 10)
        }
        if (error.response.data.status === 401 || error.response.status === 401) {
            if (vm.$route.name !== 'login') {
                self.location.href = window.location.protocol + '//' + window.location.host + '/cndids-portal/#/login'
            }
            // vm.$message.warning('接口请求认证失败')
        } else {
            if (error.response.data.status === 403) {
                vm.$message({ type: 'warning', message: error.response.data.message })
            }
        }
        return Promise.reject(error)
    }
)

util.ajaxMethodWidthParams = function (url, method, params) {
    let instance = axios.create()
    return new Promise((resolver, reject) => {
        var res = resolver
        util.ajax({
            method: method,
            url: url,
            data: JSON.stringify(params),
            config: {
                showLoading: true
            }
        })
            .then((response) => {
                res(response.data)
            })
            .catch((error) => {
                reject(error)
            })
    })
}

util.ajaxGetData = function (url, params) {
    let instance = axios.create()
    instance.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'

    return new Promise((resolve, reject) => {
        var p = new URLSearchParams()
        for (var key in params) {
            p.append(key, params[key])
        }
        util.ajax
            .post(url, p)
            .then((response) => {
                resolve(response.data)
            })
            .catch((error) => {
                reject(error)
            })
    })
}

// 针对后台规范，强制使用@RequestBody编写的方法
util.ajaxPostData = function (url, params, isMask = true) {
    let instance = axios.create()
    instance.defaults.headers.post['Content-Type'] = 'application/json'
    let signTime = Date.now().toString()
    // instance.defaults.headers.post['signTime'] = signTime
    util.ajax.defaults.headers.post['signTime'] = signTime

    let a = 'Cndids@'
    let portalOssToken = Cookies.get('portal-oss-token') ? Cookies.get('portal-oss-token') : ''

    let searchParam = ''
    if (Object.prototype.toString.call(params) !== '[object FormData]') {
        let tmpParam = params ? JSON.parse(JSON.stringify(params)) : {}
        if (portalOssToken) {
            tmpParam.token = portalOssToken
        }
        tmpParam.signTime = signTime
        let gateway = "/portal-gateway";
        if (url.includes(gateway)) {
            let index = url.indexOf(gateway);
            let result = url.substring(index + gateway.length);
            tmpParam.apiPath = result;
        } else {
            tmpParam.apiPath = url;
        }

        tmpParam.secret = `${a}#2024`
        let sortPram = tmpParam ? objKeySort(tmpParam) : {}
        let finaParam = sortPram ? JSON.stringify(sortPram) : ''
        if (params) {
            searchParam = { sign: MD5(finaParam).toString(), ...params }
        } else {
            searchParam = { sign: MD5(finaParam).toString() }
        }
        if (Array.isArray(params)) {
            searchParam = params
        }
    } else {
        searchParam = params
    }


    return new Promise((resolve, reject) => {
        util.ajax({
            method: 'post',
            url: url,
            data: searchParam,
            config: {
                showLoading: isMask
            }
        })
            .then((response) => {
                resolve(response.data)
            })
            .catch((error) => {
                reject(error)
            })
    })
}
// 针对后台规范，强制使用@RequestBody编写的方法
util.ajaxPostData1 = function (url, params, isMask = true) {
    let instance = axios.create()
    instance.defaults.headers.post['Content-Type'] = 'application/json'

    return new Promise((resolve, reject) => {
        util.ajax1({
            method: 'post',
            url: url,
            data: params,
            config: {
                showLoading: isMask
            }
        })
            .then((response) => {
                resolve(response.data)
            })
            .catch((error) => {
                reject(error)
            })
    })
}
// 针对文件下载，强制使用@RequestBody编写的方法
util.downloadPostData = function (url, params, isMask = true) {
    let instance = axios.create()
    instance.defaults.headers.post['Content-Type'] = 'application/json'
    let signTime = Date.now().toString()
    // instance.defaults.headers.post['signTime'] = signTime
    util.ajax.defaults.headers.post['signTime'] = signTime
    let a = 'Cndids@'
    let portalOssToken = Cookies.get('portal-oss-token') ? Cookies.get('portal-oss-token') : ''

    let searchParam = ''
    if (Object.prototype.toString.call(params) !== '[object FormData]') {
        let tmpParam = params ? JSON.parse(JSON.stringify(params)) : {}
        if (portalOssToken) {
            tmpParam.token = portalOssToken
        }
        tmpParam.signTime = signTime
        let gateway = "/portal-gateway";
        if (url.includes(gateway)) {
            let index = url.indexOf(gateway);
            let result = url.substring(index + gateway.length);
            tmpParam.apiPath = result;
        } else {
            tmpParam.apiPath = url;
        }

        tmpParam.secret = `${a}#2024`
        let sortPram = tmpParam ? objKeySort(tmpParam) : {}
        let finaParam = sortPram ? JSON.stringify(sortPram) : ''
        if (params) {
            searchParam = { sign: MD5(finaParam).toString(), ...params }
        } else {
            searchParam = { sign: MD5(finaParam).toString() }
        }
        if (Array.isArray(params)) {
            searchParam = params
        }
    } else {
        searchParam = params
    }


    return new Promise((resolve, reject) => {
        util.ajax({
            method: 'post',
            url: url,
            data: searchParam,
            responseType: 'blob',
            config: {
                showLoading: isMask
            }
        })
            .then((response) => {
                resolve(response)
            })
            .catch((error) => {
                reject(error)
            })
    })
}
util.ajaxMethod = function (method, url, params, headers) {
    let Params = {}
    let Data = {}
    let reg1 = new RegExp('^(G|g)(E|e)(T|t)$')
    let reg2 = new RegExp('^(D|d)(E|e)(L|l)(E|e)(T|t)(E|e)')
    if (reg1.test(method) || reg2.test(method)) {
        Params = params
    } else {
        Data = params
    }
    return new Promise((resolve, reject) => {
        util.ajax({
            method: method,
            url: url,
            params: Params,
            data: Data,
            headers: headers || {},
            config: {
                showLoading: true
            }
        })
            .then((response) => {
                resolve(response.data)
            })
            .catch((error) => {
                reject(error)
            })
    })
}
util.ajaxGetData2 = function (url, params) {
    let instance = axios.create()
    instance.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'

    return new Promise((resolve, reject) => {
        var p = new URLSearchParams()
        for (var key in params) {
            p.append(key, params[key])
        }
        url = url + '?' + p.toString()
        util.ajax
            .get(url)
            .then((response) => {
                resolve(response.data)
            })
            .catch((error) => {
                reject(error)
            })
    })
}

function objKeySort(obj) { // 排序的函数
    var newkey = Object.keys(obj).sort()
    // 先用Object内置类的keys方法获取要排序对象的属性名，再利用Array原型上的sort方法对获取的属性名进行排序，newkey是一个数组
    var newObj = {} // 创建一个新的对象，用于存放排好序的键值对
    for (var i = 0; i < newkey.length; i++) { // 遍历newkey数组
        newObj[newkey[i]] = obj[newkey[i]] // 向新创建的对象中按照排好的顺序依次增加键值对
    }
    return newObj // 返回排好序的新对象
}
export default util
