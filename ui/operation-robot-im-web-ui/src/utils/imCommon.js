import { GroupSystemMessageTypes, CustomMessageType } from "@/constants/enum"
import { getSDK, MessageType, SessionType } from "open-im-sdk-wasm"
import useContactStore from "@/store/modules/contact"
import useConversationStore from "@/store/modules/conversation"
import useUserStore from "@/store/modules/user"
import useWebsocketStore from "@/store/modules/websocket"
import { dateFormateChat } from "@/utils/common"
import { getApiToken } from "@/utils/storage"
import router from "@/router"

export const IMSDK = getSDK({
    debug: false,
    coreWasmPath: "./openIM.wasm",
    sqlWasmPath: process.env.NODE_ENV === "development" ? "/sql-wasm.wasm" : "../sql-wasm.wasm"
})

export const initStore = async () => {
    const userStore = useUserStore()
    const conversationStore = useConversationStore()
    const contactStore = useContactStore()
    try {
        await userStore.getSelfInfoFromReq()
        const websocketStore = useWebsocketStore()
        websocketStore.initWebsocket()
    } catch (error) {}
    conversationStore.getGlobalConfig()
    if (!userStore.onlychat) {
        conversationStore.getConversationListFromReq()
        conversationStore.getUnReadCountFromReq()
        getArtificialAndAutomatedPoolList()
        contactStore.getGroupListFromReq()
        contactStore.getFriendListFromReq()
        contactStore.getRecvFriendApplicationListFromReq()
        contactStore.getSendFriendApplicationListFromReq()
        contactStore.getGroupNotificationListFromStorage()
        contactStore.getOrgTreeFromReq()
    }
}

// 获取自动化池和人工池的会话列表
export const getArtificialAndAutomatedPoolList = () => {
    const userStore = useUserStore()
    const conversationStore = useConversationStore()
    if (userStore.storeRoleCode == "ZCRY" || userStore.storeRoleCode == "GLRY") {
        conversationStore.getArtificialPoolListFromReq()
        conversationStore.getAutomatedPoolListFromReq()
        conversationStore.getSessionTipFromReq()
    }
}

export const conversationSort = (conversationList) => {
    const arr = []
    const filterArr = conversationList.filter((c) => !arr.includes(c.conversationID) && arr.push(c.conversationID))
    filterArr.sort((a, b) => {
        if (a.isPinned === b.isPinned) {
            const aCompare = a.draftTextTime > a.latestMsgSendTime ? a.draftTextTime : a.latestMsgSendTime
            const bCompare = b.draftTextTime > b.latestMsgSendTime ? b.draftTextTime : b.latestMsgSendTime
            if (aCompare > bCompare) {
                return -1
            } else if (aCompare < bCompare) {
                return 1
            } else {
                return 0
            }
        } else if (a.isPinned && !b.isPinned) {
            return -1
        } else {
            return 1
        }
    })
    return filterArr
}

export const getConversationContent = (message) => {
    const userStore = useUserStore()
    let result = formatMessageByType(message)
    if (
        !message.groupID ||
        GroupSystemMessageTypes.includes(message.contentType) ||
        message.sendID === userStore.storeSelfInfo.userID ||
        message.contentType === MessageType.GroupAnnouncementUpdated
    ) {
        return result
    }
    return message.senderNickname && result ? `${message.senderNickname}：${result}` : ""
}

// 判断当前会话是否为单聊
export const checkIsSingle = () => {
    const conversationStore = useConversationStore()
    return conversationStore.storeCurrentConversation.conversationType === SessionType.Single
}
// 判断当前会话是否禁用
export const currentConversationDisabled = () => {
    let disabled = true
    if (checkIsSingle()) {
        disabled = false
    } else {
        const userStore = useUserStore()
        const conversationStore = useConversationStore()
        disabled =
            !conversationStore.storeCurrentConversation.groupID ||
            (conversationStore.storeCurrentConversation.groupType == "1" && userStore.storeRoleCode != "GLRY") ||
            conversationStore.storeCurrentConversation.groupType == "2" ||
            !conversationStore.storeCurrentMemberInGroup.groupID
    }
    console.log("disabled:", disabled)
    return disabled
}
// 判断是否可以开启单聊会话
export const isSignalConversationAccessible = (userId) => {
    const userStore = useUserStore()
    let isSelf = userId === userStore.selfInfo.userID
    if (userStore.storeOnlychat || (checkIsSingle() && !isSelf)) {
        return false
    } else {
        // let isSelf = userId === userStore.selfInfo.userID
        // return !isSelf && !userIsRobot(userId)
        return !userIsRobot(userId)
    }
}
// 判断用户是否为机器人
export const userIsRobot = (userId) => {
    if (!userId) return false
    const conversationStore = useConversationStore()
    const userList = conversationStore.storeCurrentUserList
    const user = userList.find((item) => item.imUserId == userId)
    return user && user.userType == "2"
}
// 判断当前群组人员中是否包含机器人
export const isRobotInGroup = () => {
    const conversationStore = useConversationStore()
    const groupMemberList = conversationStore.storeCurrentGroupMemberList
    return groupMemberList?.some((item) => item.userType == "2")
}

export const getCleanText = (text) => {
    return text.replace(/<\/?([a-z][a-z0-9]*)\b[^>]*>/gi, "")
}

// 会话列表消息
export const formatMessageByType = (message) => {
    const userStore = useUserStore()
    const selfUserID = userStore.storeSelfInfo.userID

    // const isSelf = (id) => id === userStore.storeSelfInfo.userID;
    const getName = (user) => {
        return user.userID === selfUserID ? "你" : user.nickname
    }

    switch (message.contentType) {
        case MessageType.TextMessage:
            return message.textElem?.content
        case MessageType.PictureMessage:
            return "[图片]"
        case MessageType.FileMessage:
            return "[文件]" + message.fileElem?.fileName
        case MessageType.VoiceMessage:
            return "[语音]"
        case MessageType.LocationMessage:
            return "[位置]"
        case MessageType.MemberEnter:
            if (!message.notificationElem.detail) return ""
            const enterDetails = JSON.parse(message.notificationElem.detail)
            const enterUser = enterDetails.entrantUser
            return `${getName(enterUser)}加入了群聊`
        case MessageType.GroupCreated:
            if (!message.notificationElem.detail) return ""
            const groupCreatedDetail = JSON.parse(message.notificationElem.detail)
            const groupCreatedUser = groupCreatedDetail.opUser
            return getName(groupCreatedUser) + "创建了群聊"
        case MessageType.MemberInvited:
            if (!message.notificationElem.detail) return ""
            const inviteDetails = JSON.parse(message.notificationElem.detail)
            const inviteOpUser = inviteDetails.opUser
            const invitedUserList = inviteDetails.invitedUserList ?? []
            let inviteStr = ""
            invitedUserList.slice(0, 3).map((user) => (inviteStr += `${getName(user)}、`))
            inviteStr = inviteStr.slice(0, -1)
            const invitedUser = `${inviteStr}${invitedUserList.length > 3 ? `等${invitedUserList.length}人` : ""}`
            return `${getName(inviteOpUser)}邀请了${invitedUser}加入群聊`
        case MessageType.GroupNameUpdated:
            if (!message.notificationElem.detail) return ""
            const groupNameDetails = JSON.parse(message.notificationElem.detail)
            return `${getName(groupNameDetails.opUser)}修改了群信息为${groupNameDetails.group.groupName}`
        case MessageType.AtTextMessage:
            let data = message.atTextElem ? message.atTextElem.text : {}
            if (data) {
                try {
                    let obj = JSON.parse(data)
                    return obj.message
                } catch (error) {
                    return data
                }
            }
        case MessageType.CustomMessage:
            try {
                let data = JSON.parse(message.customElem.data)
                let type = data.type
                if (
                    type == CustomMessageType.TextListMsg ||
                    type == CustomMessageType.CircuitCheckResultMsg ||
                    type == CustomMessageType.ChartMsg ||
                    type == CustomMessageType.StreamingMsg ||
                    type == CustomMessageType.FaultTreeMsg ||
                    type == CustomMessageType.FaultImpactAnalysisMsg ||
                    type == CustomMessageType.EmergencyPlanMsg ||
                    type == CustomMessageType.CaseLibraryMsg ||
                    type == CustomMessageType.IframePageMsg ||
                    type == CustomMessageType.VIPCircuitImpactMsg ||
                    type == CustomMessageType.StreamingForJtMsg
                ) {
                    let toolName = data?.tool?.toolName || ""
                    return toolName ? `${toolName}返回结果` : "[卡片]"
                } else if (type == CustomMessageType.CardFormMsg) {
                    let toolName = data?.tool?.toolName || ""
                    return toolName ? `${toolName}参数输入` : "[卡片]"
                } else if (
                    type == CustomMessageType.ToolListMsg ||
                    type == CustomMessageType.ToolMsg ||
                    type == CustomMessageType.ContextMsg ||
                    type == CustomMessageType.HistoryMsg ||
                    type == CustomMessageType.SupportedMsg ||
                    type == CustomMessageType.TextMsg
                ) {
                    return data.message || ""
                } else if (type == CustomMessageType.LocationMsg || type == CustomMessageType.ScanMsg) {
                    let objCode = data?.commonTool?.objCode
                    if (objCode == "Location") {
                        return "[位置]"
                    } else if (objCode == "Scan") {
                        return "[扫一扫]"
                    }
                    return ""
                } else if (type == CustomMessageType.NoticeMsg) {
                    let noticeData = data || {}
                    if (noticeData) {
                        return "[群公告]" + noticeData.content
                    }
                    return ""
                } else if (type == CustomMessageType.SolitaireMsg) {
                    return `${data.message}`
                } else {
                    return message.customElem?.data
                }
            } catch (error) {
                return message.customElem?.data
            }
        case MessageType.MemberKicked:
            if (!message.notificationElem.detail) return ""
            const kickDetails = JSON.parse(message.notificationElem.detail)
            const kickOpUser = kickDetails.opUser
            const kickdUserList = kickDetails.kickedUserList ?? []
            let kickStr = ""
            kickdUserList.slice(0, 3).map((user) => (kickStr += `${getName(user)}、`))
            kickStr = kickStr.slice(0, -1)
            return `${getName(kickOpUser)}踢出了${kickStr}${kickdUserList.length > 3 ? `等${kickdUserList.length}人` : ""}`
        case MessageType.MemberQuit:
            if (!message.notificationElem.detail) return ""
            const quitDetails = JSON.parse(message.notificationElem.detail)
            const quitUser = quitDetails.quitUser
            // 已退出的会话，不在会话列表展示
            if (quitUser.userID === selfUserID && !message.groupType) {
                const conversationStore = useConversationStore()
                conversationStore.hideConversation({ groupId: message.groupID })
            }
            return getName(quitUser) + "退出了群聊"
        case MessageType.GroupDismissed:
            if (!message.notificationElem.detail) return ""
            const dismissDetails = JSON.parse(message.notificationElem.detail)
            const dismissUser = dismissDetails.opUser
            // 已解散的会话，不在会话列表展示
            if (!message.groupType) {
                const conversationStore = useConversationStore()
                conversationStore.deleteConversation({ groupId: message.groupID })
            }
            return getName(dismissUser) + "解散了群聊"
        case MessageType.FriendAdded:
            return "你们已经是好友了，开始聊天吧"
        case MessageType.GroupOwnerTransferred:
            const transferDetails = JSON.parse(message.notificationElem.detail)
            const transferOpUser = transferDetails.opUser
            const newOwner = transferDetails.newGroupOwner
            return `${getName(transferOpUser)}转让了群主给${getName(newOwner)}`
        case MessageType.GroupMuted:
            const mutedDetails = JSON.parse(message.notificationElem.detail)
            console.log("mutedDetails:", mutedDetails)
            const mutedOpUser = mutedDetails.opUser
            return `${getName(mutedOpUser)}开启了全体禁言`
        case MessageType.GroupCancelMuted:
            const cancelMutedDetails = JSON.parse(message.notificationElem.detail)
            const cancelMutedOpUser = cancelMutedDetails.opUser
            return `${getName(cancelMutedOpUser)}关闭了全体禁言`
        default:
            return ""
    }
}

// 聊天窗口系统提示消息
export const tipMessaggeFormat = (msg) => {
    const userStore = useUserStore()
    const selfID = userStore.selfInfo.userID

    const getName = (user) => {
        return user.userID === selfID ? "你" : user.nickname
    }

    switch (msg.contentType) {
        case MessageType.GroupCreated:
            const groupCreatedDetail = JSON.parse(msg.notificationElem.detail)
            const groupCreatedUser = groupCreatedDetail.opUser
            return getName(groupCreatedUser) + "创建了群聊"
        case MessageType.MemberQuit:
            const quitDetails = JSON.parse(msg.notificationElem.detail)
            const quitUser = quitDetails.quitUser
            return getName(quitUser) + "退出了群聊"
        case MessageType.MemberInvited:
            const inviteDetails = JSON.parse(msg.notificationElem.detail)
            const inviteOpUser = inviteDetails.opUser
            const invitedUserList = inviteDetails.invitedUserList ?? []
            let inviteStr = ""
            invitedUserList.slice(0, 3).map((user) => (inviteStr += `${getName(user)}、`))
            inviteStr = inviteStr.slice(0, -1)
            const invitedUser = `${inviteStr}${invitedUserList.length > 3 ? `等${invitedUserList.length}人` : ""}`
            return `${getName(inviteOpUser)}邀请了${invitedUser}加入群聊`
        case MessageType.GroupNameUpdated:
            const groupNameDetails = JSON.parse(msg.notificationElem.detail)
            return `${getName(groupNameDetails.opUser)}修改了群信息为${groupNameDetails.group.groupName}`
        case MessageType.MemberKicked:
            const kickDetails = JSON.parse(msg.notificationElem.detail)
            const kickOpUser = kickDetails.opUser
            const kickdUserList = kickDetails.kickedUserList ?? []
            let kickStr = ""
            kickdUserList.slice(0, 3).map((user) => (kickStr += `${getName(user)}、`))
            kickStr = kickStr.slice(0, -1)
            return `${getName(kickOpUser)}踢出了${kickStr}${kickdUserList.length > 3 ? `等${kickdUserList.length}人` : ""}`
        case MessageType.MemberEnter:
            const enterDetails = JSON.parse(msg.notificationElem.detail)
            const enterUser = enterDetails.entrantUser
            return `${getName(enterUser)}加入了群聊`
        case MessageType.GroupDismissed:
            const dismissDetails = JSON.parse(msg.notificationElem.detail)
            const dismissUser = dismissDetails.opUser
            return getName(dismissUser) + "解散了群聊"
        case MessageType.FriendAdded:
            return "你们已经是好友了，开始聊天吧"
        case MessageType.GroupOwnerTransferred:
            const transferDetails = JSON.parse(msg.notificationElem.detail)
            const transferOpUser = transferDetails.opUser
            const newOwner = transferDetails.newGroupOwner
            return `${getName(transferOpUser)}转让了群主给${getName(newOwner)}`
        case MessageType.GroupMuted:
            const mutedDetails = JSON.parse(msg.notificationElem.detail)
            console.log("mutedDetails:", mutedDetails)
            const mutedOpUser = mutedDetails.opUser
            return `${getName(mutedOpUser)}开启了全体禁言`
        case MessageType.GroupCancelMuted:
            const cancelMutedDetails = JSON.parse(msg.notificationElem.detail)
            const cancelMutedOpUser = cancelMutedDetails.opUser
            return `${getName(cancelMutedOpUser)}关闭了全体禁言`
        default:
            return ""
    }
}

export const formatConversionTime = (timestemp, isSimple) => {
    if (!timestemp) return ""
    return dateFormateChat(timestemp, isSimple)
}

export const parseBr = (text) => {
    return text.replace(new RegExp("\\n", "g"), "<br>").replace(new RegExp("\n", "g"), "<br>")
}

function isString(value) {
    return typeof value === "string"
}

function sanitizeUrl(url) {
    // 如果URL没有协议头，则添加默认的HTTPS协议
    return url.startsWith("http") ? url : `https://${url}`
}

export const escapeHtml = (text) => {
    // HTML实体转义
    return text.replace(/[&<>"']/g, function (s) {
        return { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#39;" }[s]
    })
}

function createLinkElement(href, text) {
    // 创建链接元素
    return `<a class="link-el" href="${sanitizeUrl(href)}" target="_blank">${text}</a>`
}

// 对非HTML标签的部分进行HTML转义
function escapeHtmlNonTagParts(content) {
    // 匹配成对的HTML标签
    const pairedTagRegex = /<([a-zA-Z0-9-]+)(\s+[^>]*)?>(?:(?!<\/\1>)[\s\S])*<\/\1>/g

    // 匹配自闭合标签
    const selfClosingTags = ["br", "hr", "img", "input", "link", "meta"]
    const selfClosingTagRegex = new RegExp(`<(${selfClosingTags.join("|")})(\\s+[^>]*)?(?:>|\\/>)`, "g")

    // 提取所有成对的HTML标签
    let pairedTagMatches = []
    let pairedTagMatch
    while ((pairedTagMatch = pairedTagRegex.exec(content)) !== null) {
        pairedTagMatches.push({
            start: pairedTagMatch.index,
            end: pairedTagRegex.lastIndex,
            match: pairedTagMatch[0]
        })
    }

    // 提取所有自闭合的HTML标签
    let selfClosingTagMatches = []
    let selfClosingTagMatch
    while ((selfClosingTagMatch = selfClosingTagRegex.exec(content)) !== null) {
        selfClosingTagMatches.push({
            start: selfClosingTagMatch.index,
            end: selfClosingTagRegex.lastIndex,
            match: selfClosingTagMatch[0]
        })
    }

    // 合并所有标签并按位置排序
    const allTagMatches = [...pairedTagMatches, ...selfClosingTagMatches]
    allTagMatches.sort((a, b) => a.start - b.start)

    let result = ""
    let lastEnd = 0

    allTagMatches.forEach(({ start, end, match }, index) => {
        // 对非HTML标签部分进行HTML转义并添加到结果中
        if (lastEnd < start) {
            result += escapeHtml(content.slice(lastEnd, start))
        }
        // 添加HTML标签部分到结果中
        result += match
        lastEnd = end
    })

    // 如果最后一个HTML标签之后还有内容，则进行HTML转义后添加到结果中
    if (lastEnd < content.length) {
        result += escapeHtml(content.slice(lastEnd))
    }

    return result
}

export const formatLink = (content) => {
    // 检查输入是否为字符串，如果不是则返回空字符串
    if (!isString(content)) {
        return ""
    }
    // 保存原始内容的副本
    let originContent = content
    // 快速检查：如果内容中不包含 http:// 或 www. 则直接返回转义后的内容
    if (!/(https?:\/\/|www\.)/i.test(content)) {
        return escapeHtmlNonTagParts(originContent)
    }
    // 定义URL匹配的正则表达式
    // (?:https?:\/\/|www\.) - 匹配 http://, https:// 或 www.
    // [^\s<>[\](){}'"]*    - 匹配任意数量的非空白字符和特殊字符
    // [^\s<>[\](){}'".,;!?] - 确保URL结尾不是标点符号
    const urlRegex = /(?:https?:\/\/|www\.)[^\s<>[\](){}'"]*[^\s<>[\](){}'".,;!?]/gi
    // 定义用于排除已有HTML链接标签的正则表达式
    // 匹配 <img>, <a> 和 </a> 标签
    const regexExclude = /<(?:img|a)[^>]*>|<\/a>/gi
    // 移除内容中已存在的链接标签，避免重复处理
    content = content.replace(regexExclude, "")
    // 初始化存储URL位置信息的数组
    const urlPositions = []
    let match
    // 循环查找所有匹配的URL
    while ((match = urlRegex.exec(content)) !== null) {
        // 获取匹配到的完整URL
        const url = match[0]
        // 移除URL末尾的标点符号
        const cleanUrl = url.replace(/[.,;!?]+$/, "")
        // 保存URL的位置信息
        urlPositions.push({
            start: match.index, // URL开始位置
            end: match.index + cleanUrl.length, // URL结束位置
            url: cleanUrl // 清理后的URL
        })
    }
    // 按位置从后向前排序，这样替换时不会影响前面URL的位置
    urlPositions.sort((a, b) => b.start - a.start)
    // 替换所有找到的URL为HTML链接
    urlPositions.forEach(({ start, end, url }) => {
        // 获取URL前面的内容
        const before = originContent.slice(0, start)
        // 获取URL后面的内容
        const after = originContent.slice(end)
        // 将URL替换为HTML链接标签
        originContent = before + createLinkElement(url, url) + after
    })
    // 对非HTML标签的部分进行HTML转义并返回结果
    return escapeHtmlNonTagParts(originContent)
}

// 将经过HTML转义的字符串还原
export const unescapeHtml = (str) => {
    return str
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, " ")
}

export const goLoginPage = (message) => {
    const userStore = useUserStore()
    const loginType = userStore.storeLoginType
    if (window.electronWindow || loginType) {
        let path = "/login"
        if (loginType) {
            path = path + "?type=" + loginType
        }
        router.replace(path)
        return
    }
    let code = userStore.storeUrlCode
    if (code) {
        if (userStore.storeOnlychat) {
            window.parent?.postMessage({ action: "chatLogout" }, "*")
        } else {
            router.replace({ path: "/error", query: { message } })
        }
    } else {
        router.replace("/error")
    }
}

// 判断消息是否需要反馈
export const checkIsFeedbackMessage = (message) => {
    let isFeedback = false
    try {
        if (message.contentType == MessageType.CustomMessage) {
            let data = message.customElem ? message.customElem.data : {}
            if (data) {
                let obj = JSON.parse(data)
                obj.type = obj.type ? parseInt(obj.type) : ""
                isFeedback = (CustomMessageType.TextListMsg == obj.type || CustomMessageType.ChartMsg == obj.type) && (obj.robotCode || (obj.feedBackFlag == "1" && obj.serialNumber))
            }
        }
    } catch (error) {
        console.error(error)
    }
    return isFeedback
}

// 设置透明度
export const setOpacity = (opacity) => {
    if (opacity) {
        document.querySelector("body").style.opacity = opacity / 100
    }
}

// 将结果转换为对象的辅助函数
export const convertResultToObject = (resultArray, keyField) => {
    return resultArray.reduce((acc, curr) => {
        acc[curr[keyField]] = curr
        return acc
    }, {})
}

// 获取当前页面的域名
export const getCurrentDomain = () => {
    if (process.env.NODE_ENV === "production") {
        return `${window.location.protocol}//${window.location.host}`
    } else {
        // 斗门环境的域名
        return "https://**************:32703"
    }
}

// 打开重定向的地址
export const openRedirectUrl = (projectUrl) => {
    if (!projectUrl) return
    const token = getApiToken()
    let domainName = getCurrentDomain()
    const redirectUrl = `${domainName}/portal-gateway/api/loginByMaintenance?token=${token}&redirect_uri=${encodeURIComponent(domainName + projectUrl)}`
    window.open(redirectUrl, "_blank")
}

/*
 * 通知新消息
 * @param {*} message
 */
export const notificationNewMessage = async (message) => {
    try {
        let content = ""
        switch (message.contentType) {
            case MessageType.TextMessage:
                let text = message.textElem?.content
                if (text?.indexOf("img") !== -1) {
                    content = text?.replace(/<img[^>]*alt="([^"]*)"[^>]*>/g, "$1")
                } else {
                    content = message.textElem?.content
                }
                break
            case MessageType.PictureMessage:
                content = "[图片]"
                break
            case MessageType.FileMessage:
                content = "[文件]" + message.fileElem?.fileName
                break
            case MessageType.VoiceMessage:
                content = "[语音]"
                break
            case MessageType.LocationMessage:
                content = "[位置]"
                break
            case MessageType.AtTextMessage:
                let data = message.atTextElem ? message.atTextElem.text : ""
                if (data) {
                    try {
                        let obj = JSON.parse(data)
                        content = obj?.message?.replace(/<span[^>]*>(.*?)<\/span>/g, "$1").replace(/<img[^>]*alt="([^"]*)"[^>]*>/g, "$1")
                    } catch (error) {
                        console.log(error)
                        break
                    }
                }
                break
            case MessageType.CustomMessage:
                content = "[自定义消息]"
                break
        }
        console.log("接收的消息内容:" + content)
        await window.electronWindow.sendMessage({
            title: message?.senderNickname,
            content,
            ...message
        })
    } catch (error) {
        console.error("发送消息失败:", error)
    }
}
