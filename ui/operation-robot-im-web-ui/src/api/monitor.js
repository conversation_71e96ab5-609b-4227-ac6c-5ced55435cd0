import request from "@/utils/request"

// 查询会话监控列表
export const getContentList = (params) => {
    return request.post("/session/getContentList", params)
}

// 会话监控详情统计
export const getContent = (params) => {
    return request.post("/session/getContent", params)
}

// 获取会话监控日志List
export const getContentLogList = (params) => {
    return request.post("/session/getContentLogList", params)
}

// 忽略异常
export const ignoreError = (params) => {
    return request.post("/session/ignoreError", params)
}

// 查询所有会话场景类型
export const getSceneTypeList = (params) => {
    return request.post("/session/getSceneTypeList", params)
}
