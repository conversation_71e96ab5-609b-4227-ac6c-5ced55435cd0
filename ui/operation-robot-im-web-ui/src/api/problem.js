import request from "@/utils/request"
// 查询问题反馈列表
export const getContentList = (params) => {
    return request.post("/api/v1/question/page", params)
}
// 详情
export const getDetail = (params) => {
    return request.get("/api/v1/question/detail/" + params.mtQuestionId)
}
// 新增
export const add = (params) => {
    return request.post("/api/v1/question/add", params)
}
// 删除
export const del = (params) => {
    return request.post("/api/v1/question/delete", params)
}

// 上传文件
export const uploadFile = (params) => {
    return request.post("/api/dfs/uploadFilePath", params)
}

// 下载文件
export const downloadFile = (params) => {
    return request.get("/api/dfs/downloadFile?fileName=" + params.resourceUrl, {
        responseType: "blob"
    }).then(res => {
        // 创建 blob 链接
        const blob = new Blob([res])
        const link = document.createElement("a")
        link.href = window.URL.createObjectURL(blob)
        // 文件名优先用参数，没有就用 fileUrl
        link.download = params.resourceName || decodeURIComponent(params.resourceUrl.split('/').pop())
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(link.href)
    })

}
