import { IMSDK, setOpacity, isSignalConversationAccessible } from "@/utils/imCommon"
import { defineStore } from "pinia"
import store from "../index"
import useContactStore from "./contact"
import useUserStore from "./user"
import { ConversationType, TipTypes } from "@/constants/enum"
import { MessageType, SessionType } from "open-im-sdk-wasm"
import { getSessionList, getSessionTip } from "@/api/conversation"
import { getConfig, getSysConfigValue } from "@/api/config"
import useConversationToggle from "@/hooks/useConversationToggle"

const useStore = defineStore("conversation", {
    state: () => ({
        conversationList: [],
        currentConversation: {},
        unReadCount: 0,
        currentGroupInfo: {},
        currentMemberInGroup: {},
        currentConversationType: ConversationType.MyTalk,
        currentGroupMemberList: [],
        currentUserList: [],
        automatedConversationInfo: {
            list: [],
            loading: false,
            pageNo: 1,
            pageSize: 100,
            isLastPage: false
        },
        artificialConversationInfo: {
            list: [],
            loading: false,
            pageNo: 1,
            pageSize: 100,
            isLastPage: false
        },
        otherConversationTip: {},
        globalConfig: {},
        toolInfo: { showToolBar: false, showFloatTool: false, floatToolHeight: 0, data: {} },
        emergencySchedulingInfo: {}, // 应急指挥调度相关的数据存储
        refreshGroupMemberTimestamp: ""
    }),
    getters: {
        storeConversationList: (state) => state.conversationList,
        storeCurrentConversation: (state) => state.currentConversation,
        storeUnReadCount: (state) => state.unReadCount,
        storeCurrentGroupInfo: (state) => state.currentGroupInfo,
        storeCurrentMemberInGroup: (state) => state.currentMemberInGroup,
        storeCurrentConversationType: (state) => state.currentConversationType,
        storeCurrentGroupMemberList: (state) => state.currentGroupMemberList,
        storeCurrentUserList: (state) => state.currentUserList,
        storeAutomatedConversationInfo: (state) => state.automatedConversationInfo,
        storeArtificialConversationInfo: (state) => state.artificialConversationInfo,
        storeOtherConversationTip: (state) => state.otherConversationTip,
        storeGlobalConfig: (state) => state.globalConfig,
        storeToolInfo: (state) => state.toolInfo,
        storeEmergencySchedulingInfo: (state) => state.emergencySchedulingInfo,
        storeRefreshGroupMemberTimestamp: (state) => state.refreshGroupMemberTimestamp
    },
    actions: {
        updateCurrentConversationType(data) {
            this.currentConversationType = data
        },
        // 获取所有会话列表
        async getConversationListFromReq(isScrollLoad = false) {
            try {
                // let pageSize = 9999
                // const { data } = await IMSDK.getConversationListSplit({
                //     offset: isScrollLoad ? this.conversationList.length : 0,
                //     count: pageSize
                // })
                // const cves = data
                // this.conversationList = [...(isScrollLoad ? this.conversationList : []), ...cves]
                // return cves.length === pageSize

                // 获取所有会话列表
                const { data } = await IMSDK.getAllConversationList()
                const cves = data
                this.conversationList = [...cves]
                console.log("conversationList:", this.conversationList)
            } catch (error) {
                console.error(error)
                return false
            }
        },
        // 分页获取会话列表
        async getConversationListSplitFromReq(isScrollLoad = false) {
            try {
                let pageSize = 9999
                const { data } = await IMSDK.getConversationListSplit({
                    offset: isScrollLoad ? this.conversationList.length : 0,
                    count: pageSize
                })
                const cves = data
                this.conversationList = [...(isScrollLoad ? this.conversationList : []), ...cves]
                console.log("conversationList:", this.conversationList)
                return cves.length === pageSize
            } catch (error) {
                console.error(error)
                return false
            }
        },
        async getUnReadCountFromReq() {
            try {
                const { data } = await IMSDK.getTotalUnreadMsgCount()
                this.unReadCount = data
            } catch (error) {
                console.error(error)
            }
        },
        updateUnReadCount(data) {
            this.unReadCount = data
        },
        async getCurrentGroupInfoFromReq(groupID) {
            const contactStore = useContactStore()
            const sourceID = groupID ?? this.currentConversation.groupID
            const localGroup = contactStore.storeGroupList.find((group) => group.groupID === sourceID)
            if (localGroup) {
                this.currentGroupInfo = localGroup
                return
            }
            try {
                const { data } = await IMSDK.getSpecifiedGroupsInfo([sourceID])
                this.currentGroupInfo = data[0] ?? {}
            } catch (error) {
                console.error(error)
            }
        },
        async getCurrentMemberInGroupFromReq(groupID) {
            const userStore = useUserStore()
            try {
                const { data } = await IMSDK.getSpecifiedGroupMembersInfo({
                    groupID: groupID ?? this.currentConversation.groupID,
                    userIDList: [userStore.storeSelfInfo.userID]
                })
                this.currentMemberInGroup = data[0] ?? {}
                console.log("currentMemberInGroup:", this.currentMemberInGroup)
            } catch (error) {
                console.error(error)
            }
        },
        updateCurrentGroupInfo(item) {
            this.currentGroupInfo = { ...item }
        },
        updateCurrentConversation(item) {
            console.log("updateCurrentConversation:", item)
            // 使用 reactive 确保深层响应性
            this.currentConversation = reactive({ ...toRaw(item) })
        },
        updateConversationList(list) {
            // 会话列表更新时，过滤掉临时回话 ==》 因为现在的处理逻辑是不在我的会话这个tab，不更新列表，所以这个判断可以删除
            // if(this.currentConversation.isTemporaryJoin){
            //     let idx = list.findIndex((item) => item.groupID == this.currentConversation.groupID)
            //     if (idx > -1) {
            //         list.splice(idx, 1)
            //     }
            // }
            this.conversationList = [...list]
        },
        delConversationByCID(conversationID) {
            const idx = this.conversationList.findIndex((cve) => cve.conversationID === conversationID)
            if (idx !== -1) {
                this.conversationList.splice(idx, 1)
                if (this.conversationList.length == 0) {
                    this.updateCurrentConversation({})
                    this.currentGroupMemberList = []
                } else {
                    if (this.currentConversation.conversationID == conversationID) {
                        this.updateCurrentConversation(this.conversationList[0])
                    }
                }
            }
        },
        // 隐藏一个本地会话，不会删除会话内的消息，收到新消息时此会话会展现
        hideConversation({ conversationId, groupId, showToast = false }) {
            return new Promise((resolve) => {
                let data = null
                if (conversationId) {
                    data = this.conversationList.find((item) => item.conversationID == conversationId)
                } else if (groupId) {
                    data = this.conversationList.find((item) => item.groupID == groupId)
                }
                if (data) {
                    IMSDK.hideConversation(data.conversationID)
                        .then(() => {
                            this.afterHideOrDelConversationSuccess(data)
                        })
                        .catch((error) => {
                            if (showToast) {
                                feedbackToast({ error })
                            } else {
                                console.log("hideConversation error:", error)
                            }
                        })
                        .finally(() => {
                            resolve()
                        })
                } else {
                    resolve()
                }
            })
        },
        // 从本地和服务器删除指定会话及会话中的消息
        deleteConversation({ conversationId, groupId, showToast = false }) {
            return new Promise((resolve) => {
                let data = null
                if (conversationId) {
                    data = this.conversationList.find((item) => item.conversationID == conversationId)
                } else if (groupId) {
                    data = this.conversationList.find((item) => item.groupID == groupId)
                }
                if (data) {
                    IMSDK.deleteConversationAndDeleteAllMsg(data.conversationID)
                        .then(() => {
                            this.afterHideOrDelConversationSuccess(data)
                        })
                        .catch((error) => {
                            if (showToast) {
                                feedbackToast({ error })
                            } else {
                                console.log("deleteConversation error:", error)
                            }
                        })
                        .finally(() => {
                            resolve()
                        })
                } else {
                    resolve()
                }
            })
        },
        afterHideOrDelConversationSuccess(data) {
            this.delConversationByCID(data.conversationID)
            if (data && data.unreadCount > 0) {
                IMSDK.markConversationMessageAsRead(data.conversationID).catch((error) => {
                    // 只有当错误不是 10303(未读数已经为0) 时才记录
                    if (error.errCode !== 10303) {
                        console.error("Mark as read error:", error)
                    }
                })
                setTimeout(() => {
                    this.getUnReadCountFromReq()
                }, 1000)
            }
        },
        updateCurrentGroupMemberList(data) {
            this.currentGroupMemberList = data
        },
        // ⼈⼯池列表查询
        async getArtificialPoolListFromReq(isScroll) {
            let info = this.artificialConversationInfo
            let pageNo = info.pageNo
            let pageSize = info.pageSize
            if (isScroll) {
                if (info.isLastPage || info.loading) return
                info.loading = true
                pageNo = info.pageNo = info.pageNo + 1
            } else {
                pageSize = info.pageNo * info.pageSize
                pageNo = 1
            }
            let params = {
                groupType: "2",
                keyWord: "",
                pageNo: pageNo,
                pageSize: pageSize
            }
            let res = await getSessionList(params)
            if (res && res.success && res.body) {
                let data = res.body || {}
                let list = []
                data.list.forEach((item) => {
                    list.push(this.formatMessage(item, "2"))
                })
                if (isScroll) {
                    this.artificialConversationInfo.list = [...info.list, ...list]
                    info.loading = false
                } else {
                    this.artificialConversationInfo.list = list
                }
                info.isLastPage = data.isLastPage
                this.otherConversationTip.artificialNum = list.length
            }
        },
        // 自动化池列表查询
        async getAutomatedPoolListFromReq(isScroll) {
            let info = this.automatedConversationInfo
            let pageNo = info.pageNo
            let pageSize = info.pageSize
            if (isScroll) {
                if (info.isLastPage || info.loading) return
                info.loading = true
                pageNo = info.pageNo = info.pageNo + 1
            } else {
                pageSize = info.pageNo * info.pageSize
                pageNo = 1
            }
            let params = {
                groupType: "1",
                keyWord: "",
                pageNo: pageNo,
                pageSize: pageSize
            }
            let res = await getSessionList(params)
            if (res && res.success && res.body) {
                let data = res.body || {}
                let list = []
                data.list.forEach((item) => {
                    list.push(this.formatMessage(item, "1"))
                })
                if (isScroll) {
                    this.automatedConversationInfo.list = [...info.list, ...list]
                    info.loading = false
                } else {
                    this.automatedConversationInfo.list = list
                }
                info.isLastPage = data.isLastPage
                this.otherConversationTip.autoNum = data.total
            }
        },
        formatMessage(msg, groupType) {
            let sessionMsgLast = msg?.chatLogVm || {}
            let content = sessionMsgLast.content ? JSON.parse(sessionMsgLast.content) : {}
            let contentType = sessionMsgLast?.contentType
            let latestMsg = {
                sendTime: msg?.sendTime,
                sendID: sessionMsgLast?.sendID,
                senderNickname: sessionMsgLast?.senderNickname,
                groupID: msg.groupId,
                contentType: contentType,
                groupType: groupType
            }
            switch (contentType) {
                case MessageType.TextMessage:
                    latestMsg.textElem = content
                    break
                case MessageType.CustomMessage:
                    latestMsg.customElem = content
                    break
                case MessageType.AtTextMessage:
                    latestMsg.atTextElem = content
                    break
                case MessageType.PictureMessage:
                    latestMsg.pictureElem = content
                    break
                case MessageType.FileMessage:
                    latestMsg.fileElem = content
                    break
                case MessageType.LocationMessage:
                    latestMsg.locationElem = content
                    break
                case MessageType.VoiceMessage:
                    latestMsg.soundElem = content
                    break
            }
            if (TipTypes.includes(contentType)) {
                latestMsg.notificationElem = content
            }
            return {
                conversationID: msg.conversationId,
                conversationType: sessionMsgLast?.sessionType,
                groupID: msg.groupId,
                showName: msg.groupName,
                faceURL: sessionMsgLast?.groupFaceURL,
                unreadCount: msg.newMsg,
                latestMsg: JSON.stringify(latestMsg),
                latestMsgSendTime: msg?.sendTime,
                groupType: groupType
            }
        },
        // 获取⾃动⼈⼯池红点提示
        async getSessionTipFromReq() {
            let res = await getSessionTip()
            if (res && res.success && res.body) {
                let newMsgArtificial = res.body.newMsgArtificial
                let newMsgAuto = res.body.newMsgAuto
                this.otherConversationTip.newMsgArtificial = newMsgArtificial
                this.otherConversationTip.newMsgAuto = newMsgAuto
            }
        },
        // 获取全局配置
        async getGlobalConfig() {
            try {
                const userStore = useUserStore()
                let res = await getConfig()
                if (res && res.success && res.body) {
                    const configObject = {}
                    configObject.defaultRobotPhone = res.body?.defaultRobotPhone
                    configObject.defaultRobotName = res.body?.defaultRobotName
                    configObject.robotCodeList = res.body?.robotCodeList || []
                    // 工具栏
                    const toolData = res.body?.tool || []
                    for (const item of toolData) {
                        configObject[item.type] = item.flag
                    }
                    // 菜单
                    let menuData = res.body?.menu || []
                    let menuItem = menuData.find((item) => item.type === "showMenu")
                    if (menuItem) {
                        const children = menuItem.chilren || []
                        for (const item of children) {
                            configObject[item.type] = item.flag == "1"
                        }
                    }
                    // 小窗口透明度
                    if (userStore.storeOnlychat) {
                        configObject.diaphaneity = res.body?.diaphaneity || 90
                        setOpacity(configObject.diaphaneity)
                    }
                    this.globalConfig = configObject
                    // 语音按钮的配置voiceflag: 0为不使用 1为使用语音消息手工转文本 2为语音消息自动转文本展示
                    console.log("globalConfig:", this.globalConfig)
                } else {
                    console.log("Failed to fetch global configuration.")
                    this.globalConfig = {}
                }
                // 流式消息进度标签配置
                res = await getSysConfigValue({ key: "RUN_PROCESS" })
                if (res && res.success && res.body) {
                    let labels = res.body.split(",")
                    this.globalConfig.runProcessList = labels.map((item, index) => {
                        return {
                            label: item,
                            step: index + 1
                        }
                    })
                } else {
                    console.log("error getSysConfigValue")
                }
            } catch (error) {
                console.error("Error fetching global configuration:", error)
                // this.globalConfig = {}
            }
        },
        toSignalConversation(userId, isAllowedToOpen = false) {
            if (!isAllowedToOpen) {
                if (!isSignalConversationAccessible(userId)) return
            }
            const contactStore = useContactStore()
            contactStore.getUserCardData(userId).then((rs) => {
                if (rs) {
                    const { toSpecifiedConversation } = useConversationToggle()
                    toSpecifiedConversation({
                        sourceID: contactStore.storeUserCardData.baseInfo.userID,
                        sessionType: SessionType.Single
                    })
                }
            })
        },
        toGroupConversation(groupId) {
            const { toSpecifiedConversation } = useConversationToggle()
            toSpecifiedConversation({
                sourceID: groupId,
                sessionType: SessionType.Group
            })
        },
        resetToolInfo() {
            this.toolInfo = { showToolBar: false, showFloatTool: false, floatToolHeight: 0, data: {} }
        },
        resetAutomatedConversationInfo() {
            this.automatedConversationInfo = {
                list: [],
                loading: false,
                pageNo: 1,
                pageSize: 100,
                isLastPage: false
            }
        },
        resetArtificialConversationInfo() {
            this.artificialConversationInfo = {
                list: [],
                loading: false,
                pageNo: 1,
                pageSize: 100,
                isLastPage: false
            }
        },
        resetEmergencySchedulingInfo() {
            this.emergencySchedulingInfo = {}
        },
        clearConversationStore() {
            this.conversationList = []
            this.currentConversation = {}
            this.unReadCount = 0
            this.currentGroupInfo = {}
            this.currentMemberInGroup = {}
            this.currentConversationType = ConversationType.MyTalk
            this.currentGroupMemberList = []
            this.currentUserList = []
            this.resetAutomatedConversationInfo()
            this.resetArtificialConversationInfo()
            this.otherConversationTip = {}
            this.globalConfig = {}
            this.resetToolInfo()
            this.resetEmergencySchedulingInfo()
        }
    }
})

export default function useConversationStore() {
    return useStore(store)
}
