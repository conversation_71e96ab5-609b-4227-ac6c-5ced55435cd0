import { MessageType } from "open-im-sdk-wasm"
import { CustomMessageType } from "@/constants/enum"
import { feedbackToast } from "@/utils/common"
import { IMSDK, checkIsFeedbackMessage, convertResultToObject } from "@/utils/imCommon"
import { defineStore } from "pinia"
import store from "../index"
import useConversationStore from "@/store/modules/conversation"
import { getFeedbackList, getMsgFeedbackList, completeQueryList } from "@/api/chat"

const useStore = defineStore("message", {
    state: () => ({
        historyMessageList: [],
        hasMore: true,
        cardMessageSearchInfo: {
            data: [],
            show: false
        },
        selectedTextInfo: {
            text: "",
            showPopover: false,
            element: null,
            position: null
        },
        searchHistoryMessageInfo: {
            messageList: [],
            upHasMore: true,
            downHasMore: true,
            upLastMinSeq: 0,
            downLastMinSeq: 0
        },
        feedbackMessageListObj: {},
        currentPlayVoiceMessageId: "",
        cardFormMessageListObj: {},
        cardMessageTextDetailInfo: {
            data: {},
            show: false
        },
        streamingMessageListObj: {},
        voiceTextMessageListObj: {}
    }),
    getters: {
        storeHistoryMessageList: (state) => state.historyMessageList,
        storeHistoryMessageHasMore: (state) => state.hasMore,
        storeCardMessageSearchInfo: (state) => state.cardMessageSearchInfo,
        storeSelectedTextInfo: (state) => state.selectedTextInfo,
        storeSearchHistoryMessageInfo: (state) => state.searchHistoryMessageInfo,
        storeFeedbackMessageListObj: (state) => state.feedbackMessageListObj,
        storeCurrentPlayVoiceMessageId: (state) => state.currentPlayVoiceMessageId,
        storeCardFormMessageListObj: (state) => state.cardFormMessageListObj,
        storeCardMessageTextDetailInfo: (state) => state.cardMessageTextDetailInfo,
        storeStreamingMessageListObj: (state) => state.streamingMessageListObj,
        storeVoiceTextMessageListObj: (state) => state.voiceTextMessageListObj
    },
    actions: {
        async getHistoryMessageListFromReq(params) {
            const isFirstPage = params.startClientMsgID === "" || params.lastMinSeq === 0
            try {
                const { data: tmpData } = await IMSDK.getAdvancedHistoryMessageList(params)
                this.historyMessageList = [...tmpData.messageList, ...(isFirstPage ? [] : this.historyMessageList)]
                this.hasMore = !tmpData.isEnd && tmpData.messageList.length === params.count
                console.log("storeHistoryMessageList:", this.historyMessageList)
                this.getFeedbackMessageListObjFromReq(tmpData.messageList)
                this.getCompleteMessagesFromAPI(tmpData.messageList)
                return {
                    messageIDList: tmpData.messageList.map((message) => message.clientMsgID),
                    lastMinSeq: tmpData.lastMinSeq
                }
            } catch (error) {
                // feedbackToast({ message: "Get history message failed", error })
                console.log("Get history message failed")
                this.hasMore = false
                return {
                    messageIDList: [],
                    lastMinSeq: 0
                }
            }
        },
        // 历史消息查询，支持向上滚动分页查询和向下滚动分页查询
        getSearchHistoryMessageList({ clientMsgID, conversationID, isFirstPage, isDownSearch, isUpSearch }) {
            return new Promise(async (resolve) => {
                try {
                    let info = this.searchHistoryMessageInfo
                    let pageSize = isFirstPage ? 10 : 20
                    let params = {
                        conversationID: conversationID,
                        userID: "",
                        groupID: "",
                        count: pageSize
                    }
                    let upMessageList = []
                    if (isFirstPage || isUpSearch) {
                        params.lastMinSeq = info.upLastMinSeq
                        params.startClientMsgID = isFirstPage ? clientMsgID : info.messageList[0].clientMsgID
                        let upHistoryData = (await IMSDK.getAdvancedHistoryMessageList(params)).data
                        upMessageList = upHistoryData.messageList || []
                        info.upHasMore = !upHistoryData.isEnd && upMessageList.length === pageSize
                        info.upLastMinSeq = upHistoryData.lastMinSeq
                        this.getFeedbackMessageListObjFromReq(upMessageList)
                        this.getCompleteMessagesFromAPI(upMessageList)
                    }
                    let downMessageList = []
                    if (isFirstPage || isDownSearch) {
                        params.lastMinSeq = info.downLastMinSeq
                        params.startClientMsgID = isFirstPage ? upMessageList[upMessageList.length - 1].clientMsgID : info.messageList[info.messageList.length - 1].clientMsgID
                        let downHistoryData = (await IMSDK.getAdvancedHistoryMessageListReverse(params)).data
                        downMessageList = downHistoryData.messageList || []
                        info.downHasMore = !downHistoryData.isEnd && downMessageList.length === pageSize
                        info.downLastMinSeq = downHistoryData.lastMinSeq
                        this.getFeedbackMessageListObjFromReq(downMessageList)
                        this.getCompleteMessagesFromAPI(downMessageList)
                    }
                    info.messageList = [...upMessageList, ...info.messageList, ...downMessageList]
                    if (!info.downHasMore && !info.upHasMore) {
                        this.historyMessageList = JSON.parse(JSON.stringify(info.messageList))
                        this.hasMore = false
                        this.resetSearchHistoryMessageInfo()
                    }
                } catch (error) {
                    console.error(error)
                }
                resolve()
            })
        },
        // 从queryList接口查询完整的消息
        getCompleteMessagesFromAPI(list) {
            if (list && list.length > 0) {
                this.getCardFormMessageListObjFromReq([...list])
                this.getStreamingMessageListObjFromReq([...list])
                this.getVoiceTextMessageListObjFromReq([...list])
            }
        },
        // 聊天窗口滚动到指定的消息的位置
        async scrollToMessagePosition({ clientMsgID }) {
            if (!clientMsgID) return
            let idx = this.historyMessageList.findIndex((message) => message.clientMsgID == clientMsgID)
            if (idx == -1) {
                this.resetSearchHistoryMessageInfo()
                const conversationStore = useConversationStore()
                let conversationID = conversationStore.storeCurrentConversation.conversationID
                await this.getSearchHistoryMessageList({ clientMsgID: clientMsgID, conversationID, isFirstPage: true })
                await nextTick()
            }
            let parentNode = document.getElementById("messageListContainer")
            let el = parentNode?.querySelector(`[id="${clientMsgID}"]`)
            if (el) {
                el.scrollIntoView(true)
                el.style.backgroundColor = "#c3c3c34f"
                setTimeout(() => {
                    el.style.backgroundColor = "transparent"
                }, 1000)
            }
        },
        pushNewMessage(message) {
            // const imageUrls = filterPreviewImage([message]);
            // if (imageUrls.length > 0) {
            //   this.previewImgList = [...this.previewImgList, ...imageUrls];
            // }
            message.isNewMessage = true
            const conversationStore = useConversationStore()
            let groupMemberList = conversationStore.storeCurrentGroupMemberList || []
            let member = groupMemberList.find((item) => item.userID == message.sendID)
            message.senderFaceUrl = member?.faceURL
            if (this.historyMessageList.length < 10) {
                // 解决创建群后聊天窗口消息重复渲染的问题  (历史消息和新推送的提示语重复)
                const idx = this.historyMessageList.findIndex((msg) => msg.clientMsgID === message.clientMsgID)
                if (idx == -1) {
                    this.historyMessageList.push(message)
                    this.getFeedbackMessageListObjFromReq([message])
                }
            } else {
                this.historyMessageList.push(message)
                this.getFeedbackMessageListObjFromReq([message])
            }
            this.resetSearchHistoryMessageInfo()
        },
        updateOneMessage(message, isSuccessCallBack = false) {
            const idx = this.historyMessageList.findIndex((msg) => msg.clientMsgID === message.clientMsgID)
            if (idx !== -1) {
                this.historyMessageList[idx] = {
                    ...this.historyMessageList[idx],
                    ...message
                }
                //   if (isSuccessCallBack) {
                //     const imageUrls = filterPreviewImage([message]);
                //     if (imageUrls.length > 0) {
                //       this.previewImgList = [...this.previewImgList, ...imageUrls];
                //     }
                //   }
            }
        },
        updateCardMessageSearchInfo(data) {
            this.cardMessageSearchInfo = data
        },
        updateSelectedTextInfo(data) {
            this.selectedTextInfo = data
        },
        updateCurrentPlayVoiceMessageId(data) {
            this.currentPlayVoiceMessageId = data
        },
        resetHistoryMessageList() {
            this.historyMessageList = []
            this.hasMore = true
        },
        resetFeedbackMessageListObj() {
            this.feedbackMessageListObj = {}
        },
        resetCardMessageSearchInfo() {
            this.cardMessageSearchInfo = { data: [], show: false }
        },
        resetSelectedTextInfo() {
            this.selectedTextInfo = { text: "", showPopover: false, element: null, position: null }
        },
        resetSearchHistoryMessageInfo() {
            this.searchHistoryMessageInfo = { messageList: [], upHasMore: true, downHasMore: true, upLastMinSeq: 0, downLastMinSeq: 0 }
        },
        resetCardMessageTextDetailInfo() {
            this.cardMessageTextDetailInfo = { data: {}, show: false }
        },
        clearHistoryMessage() {
            this.resetHistoryMessageList()
            this.resetCardMessageSearchInfo()
            this.resetSelectedTextInfo()
            this.resetSearchHistoryMessageInfo()
            this.resetFeedbackMessageListObj()
            this.currentPlayVoiceMessageId = ""
            this.cardFormMessageListObj = {}
            this.resetCardMessageTextDetailInfo()
            this.streamingMessageListObj = {}
            this.voiceTextMessageListObj = {}
        },
        async getFeedbackMessageListObjFromReq(messagelist) {
            const conversationStore = useConversationStore()
            const fbMessageList = messagelist.filter((message) => checkIsFeedbackMessage(message))
            const cids = fbMessageList.map((item) => item.clientMsgID)
            if (!cids || cids.length <= 0) return

            const params = {
                groupId: conversationStore.storeCurrentConversation.groupID,
                clientMsgIds: cids
            }

            try {
                const resMsg = await getMsgFeedbackList(params)
                const resultMsg = resMsg && resMsg.success && resMsg.body ? resMsg.body : []
                const objMsg = convertResultToObject(resultMsg, "clientMsgId")

                const res = await getFeedbackList(params)
                const result = res && res.success && res.body ? res.body : []
                // let result = [
                //     {
                //         clientMsgId: "ea2e31332e71b45f66ffc267d73a7e52",
                //         upNum: 5,
                //         upFlag: false,
                //         downNum: 6,
                //         downFlag: false,
                //         commentNum: 7,
                //         commentFlag: false
                //     },
                //     {
                //         clientMsgId: "222",
                //         upNum: 0,
                //         upFlag: false,
                //         downNum: 0,
                //         downFlag: false,
                //         commentNum: 0,
                //         commentFlag: false
                //     }
                // ]
                const obj = convertResultToObject(result, "clientMsgId")

                this.feedbackMessageListObj = { ...this.feedbackMessageListObj, ...objMsg, ...obj }
            } catch (error) {
                console.error("Error fetching feedback message list:", error)
            }
        },
        // 批量查询消息
        async getBatchMessageListFromReq(messageList, messageType) {
            const cfMessageList = messageList.filter((message) => {
                if (message.contentType == MessageType.CustomMessage) {
                    let data = message.customElem ? message.customElem.data : {}
                    if (data) {
                        let obj = JSON.parse(data)
                        obj.type = obj.type ? parseInt(obj.type) : ""
                        return messageType == obj.type
                    }
                } else {
                    return messageType == message.contentType
                }
                return false
            })
            const cids = cfMessageList.map((item) => item.clientMsgID)
            if (!cids || cids.length <= 0) return
            const params = { clientMsgIds: cids }
            const res = await completeQueryList(params)
            const result = res && res.success && res.body ? res.body : []
            return new Promise((resolve) => resolve(result))
        },
        // 卡片式输入消息历史消息回填参数查询
        async getCardFormMessageListObjFromReq(messageList) {
            this.getBatchMessageListFromReq(messageList, CustomMessageType.CardFormMsg).then((result) => {
                let obj = {}
                result?.forEach((item) => {
                    let message = item.message ? JSON.parse(item.message) : null
                    obj[item.client_msg_id] = message
                })
                this.cardFormMessageListObj = { ...this.cardFormMessageListObj, ...obj }
            })
        },
        // 语音转文字消息历史消息查询
        async getVoiceTextMessageListObjFromReq(messageList) {
            const conversationStore = useConversationStore()
            if (conversationStore.storeGlobalConfig?.voiceFlag == "2") {
                this.getBatchMessageListFromReq(messageList, MessageType.VoiceMessage).then((result) => {
                    let obj = {}
                    result?.forEach((item) => {
                        let message = item.message ? JSON.parse(item.message) : null
                        obj[item.client_msg_id] = message
                    })
                    this.voiceTextMessageListObj = { ...this.voiceTextMessageListObj, ...obj }
                })
            }
        },
        // 动态的流式打印的历史消息查询
        async getStreamingMessageListObjFromReq(messageList) {
            this.getBatchMessageListFromReq(messageList, CustomMessageType.StreamingMsg).then((result) => {
                let obj = {}
                result?.forEach((item) => {
                    // item: {"client_msg_id": "2986210719646b577cf85efde9ee8405", "mt_message_complete_id": 573,"message": "11","status": "1","message_index": "7"}
                    obj[item.mt_message_complete_id] = {
                        ...item,
                        index: item.message_index,
                        completeId: item.mt_message_complete_id,
                        clientMsgID: item.client_msg_id,
                        isFromApi: true
                    }
                })
                this.updateDynamicStreamingMessage(obj)
            })
        },
        updateDynamicStreamingMessage(data) {
            this.streamingMessageListObj = { ...this.streamingMessageListObj, ...data }
        }
    }
})

export default function useMessageStore() {
    return useStore(store)
}
