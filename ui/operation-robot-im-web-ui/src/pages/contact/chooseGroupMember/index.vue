<template>
    <el-scrollbar class="choose-group-member-list">
        <div
            class="user-item"
            :id="'user_' + item.userID"
            :class="{ 'focus-item': index === currentFocusIndex }"
            v-for="(item, index) in groupMemberList"
            :key="index"
            @mousedown.prevent="onItem(item)"
            @mouseover="currentFocusIndex = index"
        >
            <Avatar class="user-item-icon" :src="item.faceURL" :name="item.nickname" />
            <div class="user-item-text">{{ item.nickname }}</div>
        </div>
    </el-scrollbar>
</template>

<script setup>
import Avatar from "@/components/avatar/index"
import useConversationStore from "@/store/modules/conversation"
import useUserStore from "@/store/modules/user"
import { sortByChinesePinyin } from "@/utils/common"
import { GroupMemberRole } from "open-im-sdk-wasm"
const $emits = defineEmits(["onMemberItem", "onClose"])
const $props = defineProps({
    keyword: {
        type: String,
        default: ""
    },
    visible: {
        type: Boolean,
        default: false
    }
})
const userStore = useUserStore()
const conversationStore = useConversationStore()
const groupMemberList = ref([])
let groupMemberListSource = []
let currentFocusIndex = ref(0)
const getGroupMemberList = () => {
    let list = JSON.parse(JSON.stringify(conversationStore.storeCurrentGroupMemberList))
    list = list.filter((item) => !item.hide && userStore.selfInfo.userID != item.userID)
    // 根据汉字拼音的首字母排序
    sortByChinesePinyin(list, "nickname")
    // 前两个字符是福建的排后面
    let temp = []
    let fj = []
    let memberCount = 0
    for (let i = 0; i < list.length; i++) {
        let nickname = list[i].nickname
        if (nickname && nickname.length > 1 && nickname.slice(0, 2) === "福建") {
            fj.push(list[i])
        } else {
            temp.push(list[i])
        }
        if (list[i].userType != "2") {
            memberCount++
        }
    }
    // 如果我是群主或管理员，并且除机器人之外的人员超过1个，那么可以@全体成员
    if (memberCount > 1) {
        let isAdmin = conversationStore.storeCurrentMemberInGroup.roleLevel == GroupMemberRole.Admin
        let isOwner = conversationStore.storeCurrentMemberInGroup.roleLevel == GroupMemberRole.Owner
        if (isAdmin || isOwner) {
            temp.unshift({
                userID: "AtAllTag",
                nickname: "全体成员",
                faceURL: ""
            })
        }
    }
    let datas = [...temp, ...fj]
    groupMemberListSource = JSON.parse(JSON.stringify(datas))
    groupMemberList.value = datas
}
watch(
    () => conversationStore.storeCurrentGroupMemberList,
    () => {
        currentFocusIndex.value = 0
        getGroupMemberList()
    },
    { deep: true, immediate: true }
)
watch(
    () => $props.keyword,
    (value) => {
        currentFocusIndex.value = 0
        if (value) {
            let temp = groupMemberListSource.filter((item) => item.nickname?.indexOf(value) > -1)
            if (temp.length > 0) {
                groupMemberList.value = temp
            } else {
                $emits("onClose")
            }
        } else {
            groupMemberList.value = groupMemberListSource
        }
    }
)
const onItem = (item) => {
    $emits("onMemberItem", item)
}

// 处理键盘的上下箭头事件
const handleArrowKey = (event) => {
    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        let length = groupMemberList.value.length
        if (length === 0) return
        switch (event.key) {
            case "ArrowUp":
                currentFocusIndex.value = (currentFocusIndex.value - 1 + length) % length // 循环向前
                break
            case "ArrowDown":
                currentFocusIndex.value = (currentFocusIndex.value + 1) % length // 循环向后
                break
            default:
                return
        }
        let id = "user_" + groupMemberList.value[currentFocusIndex.value].userID
        let node = document.getElementById(id)
        if (node) {
            node.scrollIntoView({ behavior: "smooth" })
        }
    }
}
// 处理键盘的回车事件
const handleEnterKey = (event) => {
    if (event.key === "Enter") {
        onItem(groupMemberList.value[currentFocusIndex.value])
    }
}
// 处理键盘事件
const handleKeydown = (event) => {
    if (!$props.visible) return
    handleArrowKey(event)
    handleEnterKey(event)
}
// 绑定键盘事件
onMounted(() => {
    document.addEventListener("keydown", handleKeydown)
    // 清理事件监听器
    return () => {
        document.removeEventListener("keydown", handleKeydown)
    }
})
</script>

<style scoped lang="less">
.choose-group-member-list {
    width: 100%;
    max-height: 420px;
    padding: 0px;
    display: flex;
    flex-direction: column;
    .user-item {
        width: 100%;
        margin: 2px 0px;
        padding: 4px 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        cursor: pointer;
        .user-item-icon {
            width: 32px;
            height: 32px;
        }
        .user-item-text {
            font-size: 14px;
            color: #6a6a6a;
            margin-left: 10px;
        }
    }
    .focus-item {
        background: #c3c3c347;
        border-radius: 4px;
    }
}
</style>
