<template>
    <InstructionScroll v-if="!isSingle" @height="(v) => (instructionScrollHeight = v)" />
    <el-scrollbar class="chat-content" :style="{ height: contentHeight }" ref="scrollbarRef" id="messageListContainer" @scroll="handelScroll">
        <ToolGroupPopover />
        <AddFriends :visible="addFriendsVisible" :pData="newFriendItem"></AddFriends>
        <div class="more-loading" v-show="loading">
            <span class="loading-icon" v-loading="loading" element-loading-svg-view-box="0, 0, 50, 50" :element-loading-svg="loadingSvg" element-loading-background="transparent"></span>
            <span>加载中...</span>
        </div>
        <!-- <div class="more-loading" v-show="!messageStore.storeHistoryMessageHasMore">
            <span>没有更多了</span>
        </div> -->
        <template v-if="isDismissGroup">
            <MessageItem v-for="(item, i) in historyMessageResult.list" :key="i" :message="item.message" @onAvatarClick="onAvatarClick(item)" />
        </template>
        <template v-else-if="isShowSearchHistory">
            <MessageItem v-for="(item, i) in messageStore.storeSearchHistoryMessageInfo?.messageList" :key="i" :message="item" @onAvatarClick="onAvatarClick(item)" />
        </template>
        <template v-else>
            <MessageItem v-for="(item, i) in historyMessageList" :key="i" :message="item" @onAvatarClick="onAvatarClick(item)" />
        </template>
    </el-scrollbar>
</template>

<script setup>
import { isScrollAtBottom } from "@/utils/common"
import emitter from "@/utils/events"
import MessageItem from "./messageItem.vue"
import ToolGroupPopover from "./toolMenuPopover/toolGroupPopover"
import InstructionScroll from "@/pages/tabsView/emergencyScheduling/instructionScroll.vue"
import AddFriends from "@/pages/contact/addFriends"
import useConversationStore from "@/store/modules/conversation"
import useMessageStore from "@/store/modules/message"
import useUserStore from "@/store/modules/user"
import useContactStore from "@/store/modules/contact"
import { isSignalConversationAccessible, checkIsSingle } from "@/utils/imCommon"
import useIframeMessageListener from "@/pages/chat/components/chatContent/message/customIframePage/useIframeMessageListener.js"
import useSearch from "@/pages/search/js/useSearch.js"
const { searchMessageAllByConversationId } = useSearch()
useIframeMessageListener()
const userStore = useUserStore()
const loadingSvg = `<circle class="path" cx="25" cy="25" r="10" fill="none" />`
const conversationStore = useConversationStore()
const messageStore = useMessageStore()
const contactStore = useContactStore()
const addFriendsVisible = ref(false)
const newFriendItem = ref({})
const isSingle = computed(() => checkIsSingle())
const historyMessageList = computed(() => {
    return messageStore.storeHistoryMessageList
})
// 是否显示历史消息
const isShowSearchHistory = computed(() => {
    return messageStore.storeSearchHistoryMessageInfo.messageList.length > 0
})
// 是否是已解散的群组
const isDismissGroup = ref(false)
// 应急指挥调度指令滚动高度
const instructionScrollHeight = ref(0)
// 消息内容滚动区域的高度，浮动窗口切换时需要动态计算
const contentHeight = computed(() => {
    scrollToCurrentPosition()
    if (userStore.storeOnlychat) {
        return `calc(100% - 140px - 50px - (${conversationStore.storeToolInfo.floatToolHeight}px) - (${isSingle.value ? 0 : instructionScrollHeight.value}px)) `
    } else {
        return `calc(100% - 200px - 70px - (${conversationStore.storeToolInfo.floatToolHeight}px) - (${isSingle.value ? 0 : instructionScrollHeight.value}px)) `
    }
})
// 消息内容可视区域的高度变化时，需要重新计算可视区域内最后一条消息的位置，重新计算scrollTop
const scrollbarRef = ref()
let scrollBottom = 0
const scrollToCurrentPosition = async () => {
    await nextTick()
    if (!scrollbarRef.value) return
    let scrollTop = scrollbarRef.value.wrapRef.scrollHeight - scrollbarRef.value.wrapRef.offsetHeight - scrollBottom
    scrollbarRef.value.setScrollTop(scrollTop)
}

let loading = ref(false)
let isFirstPage = true
let lastMinSeq = 0

const onLoad = () => {
    if (messageStore.storeHistoryMessageHasMore && !loading.value) {
        getMessageData()
        setTimeout(() => {
            scrollbarRef.value.setScrollTop(40)
        }, 100)
    }
}

const getMessageData = async () => {
    if (conversationStore.storeCurrentConversation.latestMsg) {
        isDismissGroup.value = false
        loading.value = true
        const data = await messageStore
            .getHistoryMessageListFromReq({
                conversationID: conversationStore.storeCurrentConversation.conversationID,
                userID: "",
                groupID: "",
                count: 20,
                startClientMsgID: messageStore.storeHistoryMessageList[0]?.clientMsgID ?? "",
                lastMinSeq: isFirstPage ? 0 : lastMinSeq
            })
            .catch(() => {
                console.error("getHistoryMessageListFromReq error")
            })
        if (data.lastMinSeq) {
            lastMinSeq = data.lastMinSeq
        }
        await nextTick()
        isFirstPage = false
        loading.value = false
        return data
    } else {
        isDismissGroup.value = true
        searchHistoryMessageByApi()
    }
}

const historyMessageResult = reactive({ isLastPage: false, pageNo: 1, list: [] })
// 已解散的群的历史消息查询
const searchHistoryMessageByApi = (isScroll) => {
    if ((isScroll && historyMessageResult.isLastPage) || loading.value) return
    loading.value = true
    if (isScroll) {
        historyMessageResult.pageNo++
    } else {
        historyMessageResult.list = []
        historyMessageResult.isLastPage = false
        historyMessageResult.pageNo = 1
    }
    let conversationId = conversationStore.storeCurrentConversation.conversationID
    if (!conversationId) {
        loading.value = false
        return
    }
    searchMessageAllByConversationId({ keyWord: "", conversationId, staTime: "", endTime: "", pageNo: historyMessageResult.pageNo }).then((result) => {
        historyMessageResult.list = [...result.list.reverse(), ...historyMessageResult.list]
        historyMessageResult.isLastPage = result.isLastPage
        loading.value = false
        if (!result.isLastPage) {
            scrollbarRef.value.setScrollTop(40)
        }
    })
}

watch(
    () => conversationStore.storeCurrentConversation.conversationID,
    async () => {
        if (conversationStore.storeCurrentConversation.conversationID) {
            isFirstPage = true
            messageStore.clearHistoryMessage()
            await getMessageData()
            setChatMainScrollToBottom()
        } else if (conversationStore.storeCurrentConversation.conversationID == undefined) {
            messageStore.clearHistoryMessage()
        }
    },
    {
        immediate: true
    }
)

const setChatMainScrollToBottom = async () => {
    await nextTick()
    setTimeout(() => {
        const messageListContainer = document.getElementById("messageListContainer")
        messageListContainer?.lastElementChild?.scrollIntoView(true)
    }, 50)
}

// 滚动事件
const handelScroll = (data) => {
    if (isDismissGroup.value) {
        if (data.scrollTop === 0) {
            searchHistoryMessageByApi(true)
        }
    } else if (isShowSearchHistory.value) {
        // 搜索的历史消息
        if (data.scrollTop === 0) {
            // 已滚动到最顶部
            if (messageStore.storeSearchHistoryMessageInfo.upHasMore) {
                let conversationID = conversationStore.storeCurrentConversation.conversationID
                messageStore.getSearchHistoryMessageList({ isUpSearch: true, conversationID: conversationID }).then(() => {
                    if (messageStore.storeSearchHistoryMessageInfo.upHasMore) {
                        scrollbarRef.value.setScrollTop(40)
                    }
                })
            }
        } else if (isScrollAtBottom(scrollbarRef.value)) {
            // 已滚动到最底部
            if (messageStore.storeSearchHistoryMessageInfo.downHasMore) {
                let conversationID = conversationStore.storeCurrentConversation.conversationID
                messageStore.getSearchHistoryMessageList({ isDownSearch: true, conversationID: conversationID })
            }
        }
    } else {
        if (data.scrollTop === 0) {
            onLoad()
        }
    }
    // 最后一条消息与可视区域底部的距离
    let wrapRef = scrollbarRef.value.wrapRef
    scrollBottom = wrapRef.scrollHeight - wrapRef.offsetHeight - wrapRef.scrollTop
}
// 点击头像，跳转到单聊对话框
const onAvatarClick = (item) => {
    if (isSignalConversationAccessible(item.sendID)) {
        const friendList = contactStore.storeFriendList
        if (userStore.selfInfo.userID !== item.sendID && friendList?.every((vItem) => vItem.userID !== item.sendID)) {
            newFriendItem.value = {
                imUserId: item.sendID,
                label: item.senderNickname
            }
            addFriendsVisible.value = !addFriendsVisible.value
        } else {
            conversationStore.toSignalConversation(item.sendID)
        }
    }
}

onMounted(() => {
    emitter.on("CHAT_MAIN_SCROLL_TO_BOTTOM", setChatMainScrollToBottom)
    emitter.on("SCROLL_TO_CURRENT_POSITION", scrollToCurrentPosition)
})
onUnmounted(() => {
    emitter.off("CHAT_MAIN_SCROLL_TO_BOTTOM", setChatMainScrollToBottom)
    emitter.off("SCROLL_TO_CURRENT_POSITION", scrollToCurrentPosition)
})
</script>

<style scoped lang="less">
.chat-content {
    // height: calc(100% - 200px - 70px);
    width: 100%;
    .more-loading {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        .loading-icon {
            width: 30px;
            height: 30px;
            margin-right: 2px;
        }
        span {
            margin-left: 5px;
            color: #b2b2b2;
            font-weight: 500;
            font-size: 12px;
            letter-spacing: 1px;
        }
    }
}
</style>
