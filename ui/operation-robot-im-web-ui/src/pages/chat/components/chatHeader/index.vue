<template>
    <div class="chat-header">
        <div class="title">
            <div class="label" :style="{ maxWidth: `calc(${parentWidth} - 180px)` }">{{ conversationStore.storeCurrentConversation.showName || "" }}</div>
            <div class="count">{{ memberCount }}</div>
        </div>
        <div class="right">
            <img class="icon-help" :title="helpTitle" src="@/assets/images/icon_help.png" @click="showHelpDrawer = !showHelpDrawer" />
            <el-dropdown ref="dropdown1" trigger="click" placement="bottom-end" :teleported="false">
                <img class="icon-more" src="@/assets/images/icon_more.png" />
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item v-if="showGroupMemberBtn">
                            <div class="dropdown-item" @click="showGroupMemberList = !showGroupMemberList">
                                <img src="@/assets/images/icon_user.png" />
                                <div class="label">群成员</div>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item v-if="showSetBtn">
                            <div class="dropdown-item" @click="showSetDrawer = !showSetDrawer">
                                <img src="@/assets/images/icon_set.png" />
                                <div class="label">设置</div>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item v-if="showGroupSetBtn">
                            <div class="dropdown-item" @click="showGroupSetDrawer = !showGroupSetDrawer">
                                <img src="@/assets/images/icon_set.png" />
                                <div class="label">群聊设置</div>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item v-if="showProblemBtn">
                            <div class="dropdown-item" @click="onClickProblem">
                                <img src="@/assets/images/icon_set.png" />
                                <div class="label">问题反馈</div>
                            </div>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
    <el-drawer modal-class="group-member-list-modal" v-model="showGroupMemberList" direction="rtl" size="100%" :with-header="false">
        <GroupMemberList v-if="showGroupMemberList" @close="showGroupMemberList = false" />
    </el-drawer>
    <ChatHelp v-model:visible="showHelpDrawer" :title="helpTitle" :content="helpContent" />
    <ChatSet v-model:visible="showSetDrawer" />
    <problemFeedback v-model:visible="showProblem" />
    <problemFeedbackS v-model:visible="showProblemS" />
</template>

<script setup>
import { checkIsSingle } from "@/utils/imCommon"
import GroupMemberList from "@/pages/contact/groupMemberList/index.vue"
import ChatHelp from "@/pages/chat/components/chatHelp/index.vue"
import ChatSet from "@/pages/chat/components/chatSet/index.vue"
import GroupSetting from "@/pages/groupSetting/index.vue"
import useConversationStore from "@/store/modules/conversation"
import useUserStore from "@/store/modules/user"
import useHelp from "@/pages/chat/components/chatHelp/useHelp.js"
import problemFeedback from "@/pages/problemFeedback/index.vue"
import problemFeedbackS from "@/pages/problemFeedback/index-s.vue"
import { ref } from "vue"
const { showHelpBtn, helpTitle, helpContent } = useHelp()
const $props = defineProps({
    parentWidth: {
        type: String,
        default: "100%"
    }
})
const conversationStore = useConversationStore()
const userStore = useUserStore()
const isSingle = computed(() => checkIsSingle())
const memberCount = computed(() => {
    let showName = conversationStore.storeCurrentConversation.showName || ""
    // let memberCount = showName && conversationStore.storeCurrentGroupInfo.memberCount ? `(${conversationStore.storeCurrentGroupInfo.memberCount})` : ""
    let list = conversationStore.storeCurrentGroupMemberList?.filter((item) => !item.hide)
    let memberCount = !isSingle.value && showName && list?.length > 0 ? `(${list.length})` : ""
    return `${memberCount}`
})
const showGroupMemberList = ref(false)
const showHelpDrawer = ref(false)
const showSetDrawer = ref(false)
const showGroupSetDrawer = ref(false)
// 是否显示设置按钮
const showSetBtn = computed(() => userStore.storeOnlychat)
// 是否显示群成员按钮
const showGroupMemberBtn = computed(() => !isSingle.value && conversationStore.storeGlobalConfig.groupMembers)
// 是否显示右侧下拉菜单按钮
const showRightMenu = computed(() => showGroupMemberBtn.value || showSetBtn.value)
// 是否显示群设置
const showGroupSetBtn = computed(() => !isSingle.value)
// 是否显示问题反馈按钮
const showProblemBtn = computed(() => !isSingle.value && conversationStore.storeGlobalConfig.question)
// const showProblemBtn = ref(true)
const showProblem = ref(false)
const showProblemS = ref(false)

const onClickProblem = () => {
    if (userStore.storeOnlychat) {
        showProblemS.value = !showProblemS.value
    } else {
        showProblem.value = !showProblem.value
    }
}
</script>

<style scoped lang="less">
.chat-header {
    width: 100%;
    height: 70px;
    background: #f5f5f5;
    border-bottom: 1px solid #eeeeee;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
        font-family:
            Microsoft YaHei,
            Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #2d3040;
        padding-left: 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        .label {
            // max-width: 340px;
            white-space: nowrap; /* 不换行 */
            overflow: hidden; /* 隐藏超出部分 */
            text-overflow: ellipsis;
        }
        .count {
            margin-left: 5px;
        }
    }
    .right {
        padding: 0px 16px;
        display: flex;
        align-items: center;
        img {
            cursor: pointer;
            &:active {
                opacity: 0.5;
            }
        }
        .icon-more {
            width: 36px;
            height: 34px;
            margin-left: 10px;
        }
        .icon-help {
            width: 22px;
            height: auto;
        }
        /deep/ .dropdown-item {
            width: 100%;
            display: flex;
            align-items: center;
            img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
            }
            .label {
                font-size: 14px;
                color: #575b66;
            }
        }
    }
}
</style>
<style lang="less">
.group-member-list-modal {
    height: 100%;
    z-index: 999 !important;
    background-color: #00000000 !important;
    .el-drawer {
        height: calc(100% - 20px) !important;
        width: 300px !important;
        top: 10px !important;
        left: calc(100% - 300px - 10px) !important;
        bottom: 10px !important;
    }
    .el-drawer__body {
        height: 100%;
        padding: 0px !important;
        background-color: #fff;
    }
}
</style>
