import { SourceType } from "@/constants/enum"
import { LoginStatus } from "open-im-sdk-wasm"
import { getIMToken, getIMUserID, getRoleCode } from "@/utils/storage"
import { IMSDK, goLoginPage } from "@/utils/imCommon"
import { getCookie } from "@/utils/common"
import { ElLoading } from "element-plus"
import { loginByCode, loginCndids } from "@/api/login"
import useUserStore from "@/store/modules/user"

export default function useLogin() {
    const userStore = useUserStore()
    const route = useRoute()
    const query = route.query || {}
    const type = query.type || ""
    const code = query.code
    const unifrom = query.unifrom
    const isNoScene = type == SourceType.NoScene // 是否是无场景的
    const loading = ref(code || unifrom ? true : false)
    const loginState = ref("")
    const onlychat = ref(query.onlychat == "true")
    userStore.updateOnlychat(onlychat.value)

    onMounted(() => {
        if (process.env.NODE_ENV === "production") {
            if (onlychat.value && (!type || isNoScene)) {
                // 仅聊天 配置错误（配置了无场景的小窗口）
                goLoginPage("配置错误")
                loading.value = false
                return
            }
        }
        if (code || unifrom == "cndids") {
            loginFromOS()
        } else {
            loginCheck()
        }
    })
    const loginFromOS = async () => {
        try {
            loginState.value = ""
            let res = {}
            let params = {}
            if (code) {
                userStore.updateUrlCode(code)
                // code与上一次的一样时，不需要重复单点登录
                let codeStorage = localStorage.getItem("IM_CODE")
                if (codeStorage == code && query.system != "zstp") {
                    loading.value = false
                    loginCheck()
                    return
                }
                params = {
                    code: code,
                    otherRoleCode: query.rolecode || "",
                    clientType: "WEB",
                    state: query.state ? query.state.toString() : ""
                }
                res = await loginByCode(params)
            } else if (unifrom == "cndids") {
                userStore.updateUrlCode(true)
                params = {
                    token: getCookie("portal-oss-token")
                }
                res = await loginCndids(params)
            }
            loading.value = false
            if (res && res.success && res.body) {
                if (code) localStorage.setItem("IM_CODE", code)
                userStore.afterLoginSuccess(res)
                loginCheck()
            } else {
                let message = res.head && res.head.respMsg ? res.head.respMsg : "登录失败"
                goLoginPage(message)
            }
        } catch (error) {
            loading.value = false
            goLoginPage("登录失败")
        }
    }

    const loginCheck = async () => {
        loginState.value = ""
        const IMToken = getIMToken()
        const IMUserID = getIMUserID()
        if (!IMToken || !IMUserID) {
            goLoginPage()
            return
        }
        try {
            const { data } = await IMSDK.getLoginStatus()
            if (data === LoginStatus.Logged) {
                await userStore.userLogout(false, false)
            } else {
                await userStore.userLogout(true, false)
            }
        } catch (error) {}
        tryLogin()
    }

    const tryLogin = async (retryCount = 0) => {
        loading.value = false
        let isLoginError = false
        const IMToken = getIMToken()
        const IMUserID = getIMUserID()
        // let loginLoading = ElLoading.service({
        //     lock: true,
        //     text: "登录中",
        //     background: "rgba(0, 0, 0, 0.7)"
        // })
        console.log("tryLogin：登录中")
        try {
            let apiHost = ""
            let wsHost = ""
            if (process.env.NODE_ENV === "production") {
                // 生产环境动态获取接口地址的域名，斗门环境build时需要注释
                let host = window.location.host
                apiHost = window.location.protocol + "//" + host
                wsHost = (window.location.protocol == "https:" ? "wss://" : "ws://") + host
            }

            await IMSDK.login({
                userID: IMUserID,
                token: IMToken,
                apiAddr: apiHost + process.env.API_URL,
                wsAddr: wsHost + process.env.WS_URL,
                platformID: 5
            })
            const roleCode = getRoleCode()
            userStore.updateRoleCode(roleCode)
            loginState.value = "success"
        } catch (error) {
            isLoginError = true
        }
        if (isLoginError) {
            if (retryCount < 3) {
                // 设置最大重试次数
                console.log("登录失败，正在重试...", retryCount + 1)
                setTimeout(() => {
                    tryLogin(retryCount + 1) // 重新登录
                }, 3000)
            } else {
                console.log("tryLogin：登录结束")
                // loginLoading?.close()
                goLoginPage("登录失败")
            }
        } else {
            console.log("tryLogin：登录结束")
            // loginLoading?.close()
        }
    }
    return {
        type,
        loading,
        loginState,
        onlychat,
        isNoScene
    }
}
