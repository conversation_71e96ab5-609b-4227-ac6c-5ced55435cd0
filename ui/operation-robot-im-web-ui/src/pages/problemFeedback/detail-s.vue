<template>
    <el-dialog v-model="visible" :title="!isView ? '新增' : '查看'" fullscreen :close-on-click-modal="false" draggable>
        <div class="detail-box">
            <el-form ref="dataFormRef" :model="dataForm" :rules="dataRules" label-width="90px" @keyup.enter="submitHandle()">
                <el-form-item prop="questionDesc" label="问题描述" required>
                    <el-input :disabled="isView" v-model="dataForm.questionDesc" type="textarea" rows="5" placeholder="输入问题描述内容..."></el-input>
                </el-form-item>
                <el-form-item v-if="isView">
                    <div class="file-box">
                        <div class="image-box" v-for="item in imageList" :key="item.mtQuestionResourceId">
                            <el-image
                                class="image-item"
                                :src="'/portal-gateway/cn-dids-web/api/dfs/downloadFile?fileName=' + item.resourceUrl"
                                fit="cover"
                                :preview-src-list="['/portal-gateway/cn-dids-web/api/dfs/downloadFile?fileName=' + item.resourceUrl]"
                            />
                        </div>
                        <div class="txt-box" v-for="item in txtList" :key="item.mtQuestionResourceId" @click="onDownloadFile(item)">
                            <div class="txt-item">{{ item.resourceName }}</div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item prop="files" label="" v-if="!isView">
                    <div class="">上传"有效截图/视频",可以让问题<span style="color: red">更快被解决</span>哦,最多可以上传5个图片或文件</div>
                    <el-upload
                        class="upload-demo"
                        :file-list="fileList"
                        :http-request="customUpload"
                        :on-remove="handleRemove"
                        :on-preview="handlePreview"
                        multiple
                        drag
                        limit="5"
                        :show-file-list="true"
                        list-type="text"
                        accept=".jpg,.jpeg,.png,.apng,.gif,.bmp,.doc,.docx,.xls,.xlsx,.zip,.rar,.mp4,.avi,.mov"
                        @paste="handlePaste"
                    >
                        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                        <div class="el-upload__text">可在此处粘贴、拖拽,上传多个图片</div>
                        <!-- <template #tip>
                            <div class="el-upload__tip">支持类型: JPG、JPEG、PNG、APNG、GIF、BMP</div>
                        </template> -->
                    </el-upload>
                </el-form-item>
                <el-form-item prop="questionBelong" label="问题归属" required>
                    <el-select v-model="dataForm.questionBelong" :disabled="isView" placeholder="请选择问题归属" style="width: 100%">
                        <el-option v-for="item in questionBelongOptions" :key="item.codeValue" :label="item.codeName" :value="item.codeValue" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="questionType" label="问题类型" required>
                    <el-select v-model="dataForm.questionType" :disabled="isView" placeholder="请选择问题类型" style="width: 100%">
                        <el-option v-for="item in questionTypeOptions" :key="item.codeValue" :label="item.codeName" :value="item.codeValue" />
                    </el-select>
                </el-form-item>
                <template v-if="isView">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="label">反馈人：</span>
                            <span class="value">{{ dataForm.feedbackUserName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">反馈人电话：</span>
                            <span class="value">{{ dataForm.feedbackUserPhone }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="label">反馈时间：</span>
                            <span class="value">{{ dataForm.feedbackTime }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">处理状态：</span>
                            <span class="value">{{ processStatusMapper[dataForm.processStatus] }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="label">处理人：</span>
                            <span class="value">{{ dataForm.processUserName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">处理时间：</span>
                            <span class="value">{{ dataForm.processTime }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item full-width">
                            <span class="label">处理意见：</span>
                            <span class="value">{{ dataForm.processOpinion }}</span>
                        </div>
                    </div>
                </template>
            </el-form>
        </div>
        <template #footer>
            <div class="footer-buttons-fixed">
                <el-button @click="visible = false" v-if="!isView">取消</el-button>
                <el-button @click="visible = false" v-if="isView">关闭</el-button>
                <el-button type="primary" v-if="!isView" @click="submitHandle()">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { reactive, ref } from "vue"
import { ElMessage, ElLoading } from "element-plus/es"
import { Plus, Document, VideoCamera, Files, Picture } from "@element-plus/icons-vue"
import { getDetail, add, uploadFile, downloadFile } from "@/api/problem"
import { getAllDictionary } from "@/api/common"
import { feedbackToast } from "@/utils/common"
const emit = defineEmits(["refreshDataList"])

const visible = ref(false)
const dataFormRef = ref()
const fileList = ref([])
const isView = ref(false)
let imageList = ref([])
let txtList = ref([])
let questionBelongOptions = ref([])
let questionTypeOptions = ref([])
let processStatusMapper = ref({})
let questionBelongMapper = ref({})
let questionTypeMapper = ref({})

const dataForm = reactive({
    createdTime: "",
    createdUserName: "",
    feedbackTime: "",
    feedbackUserName: "",
    feedbackUserPhone: "",
    mtQuestionId: 0,
    processOpinion: "",
    processStatus: "",
    processTime: "",
    processUserName: "",
    questionBelong: "",
    questionDesc: "",
    questionType: "",
    resourceList: [],
    updatedTime: "",
    updatedUserName: ""
})

const init = (data, isAdd) => {
    visible.value = true
    isView.value = !isAdd

    // 重置 dataForm 为初始状态
    Object.keys(dataForm).forEach((key) => {
        if (key == "resourceList") {
            dataForm[key] = []
        } else {
            dataForm[key] = ""
        }
    })
    // 重置表单数据
    if (dataFormRef.value) {
        dataFormRef.value.resetFields()
    }
    fileList.value = []
    queryDict()
    if (data) {
        getDetail(data)
            .then((res) => {
                if (res && res.success) {
                    Object.assign(dataForm, res.body)

                    imageList.value = []
                    txtList.value = []
                    // 分类图片和文件
                    dataForm.resourceList.forEach((item) => {
                        if (isUrlImage(item.resourceUrl)) {
                            imageList.value.push(item)
                        } else {
                            txtList.value.push(item)
                        }
                    })
                } else {
                    feedbackToast({ message: "获取数据异常", error })
                }
            })
            .catch((error) => {
                feedbackToast({ message: "获取数据异常", error })
            })
    }
}

// 查询字典值 question_belong 问题归属  question_type  问题类型 process_status  处理状态
const queryDict = () => {
    getAllDictionary(["process_status", "question_belong", "question_type"])
        .then((res) => {
            console.log("getAllDictionary res===", res)
            if (res && res.success) {
                questionTypeOptions.value = res.body?.question_type || []
                questionBelongOptions.value = res.body?.question_belong || []
                for (let item of res.body?.process_status) {
                    processStatusMapper.value[item.codeValue] = item.codeName
                }
                for (let item of res.body?.question_belong) {
                    questionBelongMapper.value[item.codeValue] = item.codeName
                }
                for (let item of res.body?.question_type) {
                    questionTypeMapper.value[item.codeValue] = item.codeName
                }
            } else {
                feedbackToast({ message: "获取数据异常", error })
            }
        })
        .catch((error) => {
            feedbackToast({ message: "获取数据异常", error })
        })
}

const dataRules = ref({
    questionDesc: [{ required: true, message: "问题描述不能为空", trigger: "blur" }],
    questionBelong: [{ required: true, message: "请选择问题归属", trigger: "change" }],
    questionType: [{ required: true, message: "请选择问题类型", trigger: "change" }]
    // files: [{ required: true, message: "请上传附件", trigger: "change" }]
})

// 表单提交
const submitHandle = () => {
    dataFormRef.value.validate((valid) => {
        if (!valid) {
            return false
        }

        // 显示加载框
        const loading = ElLoading.service({
            lock: true,
            text: "保存中...",
            background: "rgba(0, 0, 0, 0.7)"
        })

        add(dataForm)
            .then(() => {
                // 关闭加载框
                loading.close()

                ElMessage.success({
                    message: "操作成功"
                })
                visible.value = false
                emit("refreshDataList")
            })
            .catch((error) => {
                // 关闭加载框
                loading.close()

                ElMessage.error({
                    message: "操作失败"
                })
            })
    })
}

const isUrlImage = (url) => {
    // 常见图片后缀判断
    return /\.(jpg|jpeg|png|gif|bmp|webp|svg|apng)$/i.test(url)
}

// 文件类型判断
const isImage = (file) => {
    return file.raw.type && file.raw.type.startsWith("image")
}

// 获取文件后缀
const getFileExt = (name) => {
    return name.substring(name.lastIndexOf(".") + 1)
}

// 自定义上传方法
const customUpload = async (option) => {
    // 这里需要你实现上传逻辑，比如调用后端API
    // 假设上传成功后返回url
    const file = option.file
    // 创建FormData对象
    const formData = new FormData()
    formData.append("file", file)
    formData.append("path", "question")

    // 上传文件
    const uploadRes = await uploadFile(formData)
    if (uploadRes && uploadRes.success) {
        // 更新文件信息
        const fileInfo = uploadRes.body || {}
        let item = {
            resourceName: fileInfo.docName,
            resourceUrl: fileInfo.docUrl
        }

        dataForm.resourceList.push(item)
        if (isUrlImage(item.resourceUrl)) {
            imageList.value.push(item)
        } else {
            txtList.value.push(item)
        }
        console.log()
        option.onSuccess()
    } else {
        option.onError()
        ElMessage.success({
            message: "文件上传失败"
        })
    }
}

// 删除文件
const handleRemove = (file) => {
    dataForm.resourceList = dataForm.resourceList.filter((f) => f.resourceName !== file.name)
}

// 预览
const handlePreview = (file) => {
    if (isImage(file)) {
        window.open(file.url || file.preview)
    } else {
        window.open(file.url)
    }
}

// 获取文件图标
const getFileIcon = (file) => {
    const ext = getFileExt(file.name).toLowerCase()
    if (["doc", "docx"].includes(ext)) {
        return Document
    } else if (["xls", "xlsx"].includes(ext)) {
        return Files
    } else if (["mp4", "avi", "mov"].includes(ext)) {
        return VideoCamera
    } else if (["zip", "rar"].includes(ext)) {
        return Files
    } else {
        return Document
    }
}

// 获取文件图标颜色
const getFileIconColor = (file) => {
    const ext = getFileExt(file.name).toLowerCase()
    if (["doc", "docx"].includes(ext)) {
        return "#409EFF"
    } else if (["xls", "xlsx"].includes(ext)) {
        return "#67C23A"
    } else if (["mp4", "avi", "mov"].includes(ext)) {
        return "#E6A23C"
    } else if (["zip", "rar"].includes(ext)) {
        return "#F56C6C"
    } else {
        return "#909399"
    }
}

// 处理粘贴事件
const handlePaste = async (event) => {
    const items = event.clipboardData?.items
    if (!items) return

    for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.kind === "file") {
            const file = item.getAsFile()
            if (file) {
                // 检查文件类型
                const fileType = file.type
                const isImage = fileType.startsWith("image/")
                const isVideo = fileType.startsWith("video/")
                const isDocument = fileType.includes("word") || fileType.includes("excel") || fileType.includes("pdf")
                const isZip = fileType.includes("zip") || fileType.includes("rar")

                if (isImage || isVideo || isDocument || isZip) {
                    // 创建文件对象
                    const uploadFile = {
                        uid: Date.now(),
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        raw: file
                    }

                    // 创建FormData对象
                    const formData = new FormData()
                    formData.append("file", uploadFile)
                    formData.append("path", "question")
                    // 上传文件
                    const uploadRes = await uploadFile(formData)
                    if (uploadRes && uploadRes.success) {
                        // 更新文件信息
                        const fileInfo = uploadRes.body || {}
                        let item = {
                            resourceName: fileInfo.docUrl,
                            resourceUrl: fileInfo.docName
                        }
                        if (isUrlImage(item.resourceUrl)) {
                            imageList.value.push(item)
                        } else {
                            txtList.value.push(item)
                        }
                    } else {
                        ElMessage.warning({
                            message: "文件上传失败"
                        })
                    }
                } else {
                    ElMessage.warning("不支持的文件类型")
                }
            }
        }
    }
}

const onDownloadFile = (item) => {
    downloadFile(item)
}

defineExpose({
    init
})
</script>

<style scoped lang="less">
.detail-box {
    background: #e9ebf4;
    padding: 10px;
}

.upload-demo {
    width: 100%;
}
.upload-demo :deep(.el-upload-dragger) {
    width: 100%;
    height: 200px;
}

.file-box {
    display: flex;
    flex-direction: column;
    .image-box {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .image-item {
            width: 100px;
            height: 80px;
        }
    }
    .txt-box {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        .txt-item {
            color: #409eff;
            text-decoration: underline;
            cursor: pointer;
        }
    }
}

.info-row {
    display: flex;
    margin-bottom: 16px;
    padding: 0px 10px;
    .info-item {
        flex: 1;
        padding: 0 12px;
        display: flex;
        align-items: center;
        .label {
            flex-shrink: 0;
            color: #606266;
            margin-right: 8px;
            white-space: nowrap;
        }
        .value {
            color: #303133;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        &.full-width {
            flex: 2;
        }
    }
}

.footer-buttons-fixed {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    background: #fff;
    z-index: 10;
    box-shadow: 0 -2px 8px #f0f1f2;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
}

// 固定头部样式
:deep(.el-dialog__header) {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    background: #fff !important;
    z-index: 10 !important;
    box-shadow: 0 2px 8px #f0f1f2 !important;
    padding: 20px !important;
    margin: 0 !important;
}
</style>
