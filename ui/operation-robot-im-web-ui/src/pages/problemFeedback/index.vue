<template>
    <el-dialog
        v-model="props.visible"
        title="问题反馈"
        width="90%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="true"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        :append-to-body="true"
        :lock-scroll="false"
        :custom-class="'problem-feedback-dialog'"
        :style="{ height: '700px' }"
        @opened="onDialogOpened"
        @close="closeDialog"
    >
        <div class="problem-feedback-dialog-content">
            <div class="problem-feedback" ref="monitorManageContainer">
                <header ref="headerContainer">
                    <div class="search-input">
                        <el-input suffix-icon="el-icon-search" @keyup.enter="keywordSearch" style="width: 280px" placeholder="请输入关键字并按回车查询" v-model="form.questionDesc"> </el-input>
                        <el-button class="ml5" type="primary" @click="toggleSearchCondition">
                            高级搜索
                            <el-icon class="ml5">
                                <ArrowUp v-if="expandSearch" />
                                <ArrowDown v-else />
                            </el-icon>
                        </el-button>
                    </div>
                    <div v-show="expandSearch" class="search-content">
                        <el-form :inline="true" :model="form">
                            <el-form-item label="问题描述">
                                <el-input v-model="form.questionDesc" placeholder="输入要查询的问题描述" clearable />
                            </el-form-item>
                            <el-form-item label="反馈时间">
                                <el-date-picker v-model="form.feedbackDate" type="datetimerange" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" />
                            </el-form-item>
                            <el-form-item label="处理状态">
                                <el-select v-model="form.processStatus" @change="onQuery" clearable placeholder="请选择处理状态">
                                    <el-option v-for="item in processStatusOptions" :key="item.codeValue" :label="item.codeName" :value="item.codeValue" />
                                </el-select>
                            </el-form-item>
                            <div class="button-group">
                                <el-button type="primary" @click="onQuery">查询</el-button>
                                <el-button @click="onReset">重置</el-button>
                            </div>
                        </el-form>
                    </div>
                </header>
                <el-container class="main-container">
                    <div class="header-content">
                        <el-button type="primary" @click="add()">新增</el-button>
                        <el-button :disabled="multipleSelection.length == 0" @click="onDelete(multipleSelection, true)">批量删除</el-button>
                    </div>
                    <el-table :data="tableData" style="width: 100%" :height="tableHeight" tooltip-effect="dark" @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" align="center" :selectable="(row) => row.manageStatus == '0'"> </el-table-column>
                        <el-table-column label="问题归属" prop="questionBelong" align="center" width="140">
                            <template #default="scope">
                                {{ questionBelongMapper[scope.row.questionBelong] }}
                            </template>
                        </el-table-column>
                        <el-table-column label="问题类型" prop="questionType" width="100" align="center">
                            <template #default="scope">
                                {{ questionTypeMapper[scope.row.questionType] }}
                            </template>
                        </el-table-column>
                        <el-table-column label="问题描述" prop="questionDesc" min-width="200" show-overflow-tooltip align="center"></el-table-column>
                        <el-table-column label="反馈时间" prop="feedbackTime" width="180" align="center" />
                        <el-table-column label="处理状态" prop="processStatus" width="100" align="center">
                            <template #default="scope">
                                {{ processStatusMapper[scope.row.processStatus] }}
                            </template>
                        </el-table-column>
                        <el-table-column label="处理人" prop="processUserName" width="100" align="center"></el-table-column>
                        <el-table-column label="处理时间" prop="processTime" width="180" show-overflow-tooltip align="center" />
                        <el-table-column label="处理意见" prop="processOpinion" min-width="140" show-overflow-tooltip align="center" />
                        <el-table-column label="操作" width="100" align="center" fixed="right">
                            <template #default="scope">
                                <span class="active-col" @click="openDetail(scope.row)"> 查看 </span>
                                <span v-if="scope.row.processStatus == '0'" class="active-col" @click="onDelete(scope.row, false)"> 删除 </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-container>
                <el-pagination
                    class="pagination-container"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="form.pageNo"
                    :page-size="form.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalCount"
                >
                </el-pagination>
            </div>
        </div>
    </el-dialog>
    <Detail ref="detailRef" :data="currentConversation" @refreshDataList="queryList" @close="detailsDialogVisible = false" />
</template>

<script setup>
import { ElLoading, ElMessageBox } from "element-plus"
import { feedbackToast } from "@/utils/common"
import Detail from "./detail.vue"
import { getContentList, del } from "@/api/problem"
import { getAllDictionary } from "@/api/common"
import useUserStore from "@/store/modules/user"
import { getCookie } from "@/utils/common"
import { loginCndids } from "@/api/login"
import { archiveSession } from "@/api/conversation"
import useConversationStore from "@/store/modules/conversation"
import { ref } from "vue"
const props = defineProps({
    visible: Boolean
})
const emit = defineEmits(["update:visible"])
const conversationStore = useConversationStore()
const userStore = useUserStore()
const route = useRoute()
const query = route.query || {}
const unifrom = query.unifrom
const currentConversation = ref({})
const detailsDialogVisible = ref(false)

const closeDialog = () => {
    emit("update:visible", false)
}

const formatDate = (date) => {
    const y = date.getFullYear()
    const m = (date.getMonth() + 1).toString().padStart(2, "0")
    const d = date.getDate().toString().padStart(2, "0")
    const h = date.getHours().toString().padStart(2, "0")
    const min = date.getMinutes().toString().padStart(2, "0")
    const s = date.getSeconds().toString().padStart(2, "0")
    return `${y}-${m}-${d} ${h}:${min}:${s}`
}
// 获取当前日期和一个月前的日期
const getDefaultDateRange = () => {
    const end = new Date()
    const start = new Date()
    start.setMonth(start.getMonth() - 1)
    // 设置开始时间为00:00:00
    start.setHours(0, 0, 0, 0)
    // 设置结束时间为23:59:59
    end.setHours(23, 59, 59, 999)
    // 返回 [开始时间, 结束时间]
    return [formatDate(start), formatDate(end)]
}
const form = reactive({
    feedbackDate: getDefaultDateRange(),
    feedbackEndTime: getDefaultDateRange()[1],
    feedbackStartTime: getDefaultDateRange()[0],
    feedbackUserPhone: "",
    pageNo: 1,
    pageSize: 10,
    processStatus: "",
    questionDesc: ""
})

const detailRef = ref()
const loginState = ref(false)
let monitorManageContainer = ref(null)
let headerContainer = ref(null)
let tableHeight = ref()
let keyword = ref()
let expandSearch = ref(false)
let pageNo = ref(1)
let pageSize = ref(20)
let totalCount = ref(0)
let tableData = ref([])
let sceneTypeOptions = ref([])
let processStatusOptions = ref([])
let processStatusMapper = ref({})
let questionBelongMapper = ref({})
let questionTypeMapper = ref({})
let loading = ref(false)
let multipleSelection = ref([])

const onDialogOpened = () => {
    nextTick(() => {
        setTableHeight()
    })
}

// 获取表格数据
const queryList = () => {
    getContentList(form)
        .then((res) => {
            console.log("getContentList res===", res)
            if (res && res.success) {
                totalCount.value = res.body.total
                tableData.value = res.body?.list || []
            } else {
                feedbackToast({ message: "获取数据异常", error })
            }
        })
        .catch((error) => {
            feedbackToast({ message: "获取数据异常", error })
        })
}
// 查询字典值 question_belong 问题归属  question_type  问题类型 process_status  处理状态
const queryProcessStatusDict = () => {
    getAllDictionary(["process_status", "question_belong", "question_type"])
        .then((res) => {
            console.log("getAllDictionary res===", res)
            if (res && res.success) {
                processStatusOptions.value = res.body?.process_status || []
                for (let item of res.body?.process_status) {
                    processStatusMapper.value[item.codeValue] = item.codeName
                }
                for (let item of res.body?.question_belong) {
                    questionBelongMapper.value[item.codeValue] = item.codeName
                }
                for (let item of res.body?.question_type) {
                    questionTypeMapper.value[item.codeValue] = item.codeName
                }
            } else {
                feedbackToast({ message: "获取数据异常", error })
            }
        })
        .catch((error) => {
            feedbackToast({ message: "获取数据异常", error })
        })
}
// 查询
const onQuery = () => {
    console.log("查询")
    queryList()
}
// 重置
const onReset = () => {
    keyword.value = ""
    form.feedbackEndTime = ""
    form.feedbackStartTime = ""
    form.feedbackUserPhone = ""
    form.processStatus = ""
    form.questionDesc = ""
    form.runStatus = ""
    form.feedbackDate = []
    pageNo.value = 1
    queryList()
}
// 搜索框回车后查询
const keywordSearch = () => {
    pageNo.value = 1
    queryList()
}
// pagesize触发查询
const handleSizeChange = (val) => {
    pageSize.value = val
    pageNo.value = 1
    queryList()
}
// 分页查询
const handleCurrentChange = (val) => {
    pageNo.value = val
    queryList()
}
// 打开详情页面
const openDetail = (row) => {
    detailRef.value.init(row, false)
}
// 高级搜索展开/收起
const toggleSearchCondition = () => {
    expandSearch.value = !expandSearch.value
    nextTick(() => {
        setTableHeight()
    })
}
// 设置表格高度
const setTableHeight = () => {
    tableHeight.value = 700 - 60 - headerContainer.value?.offsetHeight - 40 - 42 - 70
}
// 多选操作
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}
// 新增
const add = () => {
    detailRef.value.init(null, true)
}
// 删除、批量删除
const onDelete = (data, isMul) => {
    let message = isMul ? `已选${multipleSelection.value.length}条，确认批量删除?` : `确认删除?`
    ElMessageBox.confirm(message, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
    })
        .then(() => {
            loading.value = true
            let mtQuestionIdList = []
            if (isMul) {
                multipleSelection.value.forEach((item) => {
                    mtQuestionIdList.push(item.mtQuestionId)
                })
            } else {
                mtQuestionIdList = [data.mtQuestionId]
            }
            let params = { mtQuestionIdList: mtQuestionIdList }
            del(params)
                .then((res) => {
                    if (!res || !res.success) {
                        const errMsg = res?.head?.respMsg || "删除失败"
                        feedbackToast({ message: errMsg, error: true })
                        return
                    }

                    feedbackToast({ message: "删除成功" })
                    queryList()
                })
                .catch((error) => feedbackToast(error))
                .finally(() => {
                    loading.value = false
                })
        })
        .catch(() => {})
}

const autoLogin = async () => {
    // let loginLoading = ElLoading.service({
    //     lock: true,
    //     text: "登录中",
    //     background: "rgba(0, 0, 0, 0.7)"
    // })
    console.log("autoLogin：登录中")
    try {
        let token = getCookie("portal-oss-token")
        if (unifrom == "cndids" && token) {
            userStore.updateUrlCode(true)
            let params = {
                token: token
            }
            let res = await loginCndids(params)
            if (res && res.success && res.body) {
                userStore.afterLoginSuccess(res)
                loginState.value = "success"
            } else {
                feedbackToast({ message: "登录失败", error })
            }
        } else {
            loginState.value = "success"
        }
    } catch (error) {
        feedbackToast({ message: "登录失败", error })
    }
    console.log("autoLogin：登录结束")
    // loginLoading?.close()
}

onMounted(() => {
    autoLogin()
    window.addEventListener("resize", () => {
        setTableHeight()
    })
})
watch(
    () => loginState.value,
    () => {
        if (loginState.value == "success") {
            queryList()
            queryProcessStatusDict()
            conversationStore.getGlobalConfig()
        }
    }
)
</script>

<style scoped lang="less">
.problem-feedback {
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    background: #e9ebf4;
    header {
        width: 100%;
        padding: 10px 10px 5px;
        box-sizing: border-box;
        background: #fff;
        .search-input {
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
        .search-content {
            width: 100%;
            margin-top: 10px;
            background: #fff;
            /deep/ .el-form-item {
                width: 33.33%;
                padding: 5px 20px !important;
                box-sizing: border-box;
                margin: 0px !important;
                .el-form-item__label {
                    color: #2d3040 !important;
                }
            }
            .button-group {
                width: 100% !important;
                display: inline-flex;
                justify-content: flex-end;
                padding: 5px 20px;
                box-sizing: border-box;
            }
        }
    }
    .main-container {
        width: 100%;
        margin-top: 10px;
        padding: 10px 10px 0px;
        box-sizing: border-box;
        background: #fff;
        display: flex;
        flex-wrap: wrap;
        .header-content {
            width: 100%;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            .tip {
                margin-left: 10px;
                color: #2d304099;
                font-size: 13px;
                display: flex;
                align-items: center;
                /deep/ .el-icon {
                    margin-right: 2px;
                }
            }
        }
        /deep/ th {
            background: #eff3f8 !important;
            font-weight: normal !important;
            color: #2d3040 !important;
            border-right: 1px solid #fff !important;
        }
        /deep/ tr {
            color: #2d3040 !important;
        }
        .active-col {
            padding: 0px 2px;
            box-sizing: border-box;
            color: #47e;
            cursor: pointer;
        }
        .text-underline {
            text-decoration: underline;
        }
    }
    .pagination-container {
        width: 100%;
        height: 60px;
        padding: 0px 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        background: #fff;
    }
}
.problem-feedback-dialog {
    min-width: 900px;
    width: 70% !important;
    max-width: 100vw;
    height: 700px !important;
    .el-dialog__body {
        height: 630px;
        overflow-y: auto;
        padding: 0;
    }
}
.problem-feedback-dialog-content {
    height: 100%;
    overflow-y: auto;
}
</style>
<style lang="less">
.monitor-details-dialog {
    padding: 0px !important;
    min-width: 1380px;
    .el-dialog__header {
        display: none !important;
    }
    .el-dialog__body {
        width: 100% !important;
        height: 100% !important;
        overflow: hidden !important;
        background: #e9ebf4 !important;
    }
}
.monitor-notification {
    width: fit-content !important;
}
</style>
