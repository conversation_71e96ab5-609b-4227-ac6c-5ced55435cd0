<template>
    <div class="monitor-manage" ref="monitorManageContainer">
        <header ref="headerContainer">
            <div class="search-input">
                <el-input suffix-icon="el-icon-search" @keyup.enter="keywordSearch" style="width: 280px" placeholder="请输入关键字并按回车查询" v-model="keyword"> </el-input>
                <el-button class="ml5" type="primary" @click="toggleSearchCondition">
                    高级搜索
                    <el-icon class="ml5">
                        <ArrowUp v-if="expandSearch" />
                        <ArrowDown v-else />
                    </el-icon>
                </el-button>
            </div>
            <div v-show="expandSearch" class="search-content">
                <el-form :inline="true" :model="form">
                    <el-form-item label="场景名称">
                        <el-input v-model="form.sceneName" placeholder="输入要查询的会话场景名称" clearable />
                    </el-form-item>
                    <el-form-item label="场景类型">
                        <el-select v-model="form.sceneType" @change="onQuery" clearable placeholder="输入要查询的会话场景类型">
                            <el-option v-for="item in sceneTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="会话名称">
                        <el-input v-model="form.conversatioName" placeholder="输入要查询的会话名称" clearable />
                    </el-form-item>
                    <el-form-item label="参与人员电话">
                        <el-input v-model="form.phone" placeholder="按参与会话的人员（电话号码）查询" clearable />
                    </el-form-item>
                    <el-form-item label="参与人员姓名">
                        <el-input v-model="form.userName" placeholder="按参与会话的人员（姓名）查询" clearable />
                    </el-form-item>
                    <el-form-item label="运行状态">
                        <el-select v-model="form.runStatus" @change="onQuery" clearable placeholder="输入运行状态">
                            <el-option v-for="item in runStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <div class="button-group">
                        <el-button type="primary" @click="onQuery">查询</el-button>
                        <el-button @click="onReset">重置</el-button>
                    </div>
                </el-form>
            </div>
        </header>
        <el-container class="main-container">
            <div class="header-content">
                <el-button :disabled="multipleSelection.length == 0" :loading="archiveLoading" @click="archiveConversation(null)">批量归档</el-button>
                <div class="tip">
                    <el-icon size="14"><Warning /></el-icon>建议每次最多批量归档10条会话
                </div>
            </div>
            <el-table :data="tableData" style="width: 100%" :height="tableHeight" tooltip-effect="dark" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" :selectable="(row) => row.manageStatus == '0'"> </el-table-column>
                <el-table-column prop="sceneTypeName" label="场景类型" align="center"></el-table-column>
                <el-table-column prop="sceneName" label="会话场景名称" show-overflow-tooltip align="center" />
                <el-table-column label="会话名称" show-overflow-tooltip align="center">
                    <template #default="scope">
                        <span class="active-col text-underline" @click="openDetail(scope.row)"> {{ scope.row.imGroupName }} </span>
                    </template>
                </el-table-column>
                <el-table-column prop="createdTime" label="创建时间" align="center" />
                <el-table-column label="管理状态" width="100" align="center">
                    <template #default="scope">
                        {{ scope.row.manageStatus == "0" ? "活动" : scope.row.manageStatus == "1" ? "归档" : "" }}
                    </template>
                </el-table-column>
                <el-table-column label="运行状态" width="100" align="center">
                    <template #default="scope">
                        {{ scope.row.runStatus == "0" ? "正常" : scope.row.runStatus == "1" ? "异常" : "" }}
                    </template>
                </el-table-column>
                <el-table-column prop="userNameStr" label="成员" show-overflow-tooltip align="center" />
                <el-table-column label="操作" width="100" align="center">
                    <template #default="scope">
                        <span class="active-col" @click="openDetail(scope.row)"> 查看 </span>
                        <span v-if="scope.row.manageStatus == '0'" class="active-col" @click="archiveConversation(scope.row)"> 归档 </span>
                    </template>
                </el-table-column>
            </el-table>
        </el-container>
        <el-pagination
            class="pagination-container"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNo"
            :page-size="pageSize"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
        >
        </el-pagination>
        <el-dialog class="monitor-details-dialog" v-model="detailsDialogVisible" :modal="false" fullscreen destroy-on-close>
            <MonitorDetails :data="currentConversation" @close="detailsDialogVisible = false" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ElLoading, ElMessageBox } from "element-plus"
import { feedbackToast } from "@/utils/common"
import MonitorDetails from "../monitorDetails/index.vue"
import { getContentList, getSceneTypeList } from "@/api/monitor"
// import { getAllDictionary } from "@/api/common"
import useUserStore from "@/store/modules/user"
import { getCookie } from "@/utils/common"
import { loginCndids } from "@/api/login"
import { archiveSession } from "@/api/conversation"
import useConversationStore from "@/store/modules/conversation"
const conversationStore = useConversationStore()
const userStore = useUserStore()
const route = useRoute()
const query = route.query || {}
const unifrom = query.unifrom
const currentConversation = ref({})
const detailsDialogVisible = ref(false)
const form = reactive({
    sceneName: "",
    sceneType: "",
    conversatioName: "",
    phone: "",
    userName: "",
    runStatus: ""
})
const loginState = ref(false)
let monitorManageContainer = ref(null)
let headerContainer = ref(null)
let tableHeight = ref()
let keyword = ref()
let expandSearch = ref(false)
let pageNo = ref(1)
let pageSize = ref(20)
let totalCount = ref(0)
let tableData = ref([])
let sceneTypeOptions = ref([])
let runStatusOptions = ref([
    { value: "0", label: "正常" },
    { value: "1", label: "异常" }
])
let archiveLoading = ref(false)
let multipleSelection = ref([])

// 获取表格数据
const queryList = () => {
    let params = {
        imGroupName: form.conversatioName,
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        phone: form.phone,
        sceneName: form.sceneName,
        diBehaviorSceneTypeId: form.sceneType,
        userName: form.userName,
        runStatus: form.runStatus,
        keyWord: keyword.value
    }
    getContentList(params)
        .then((res) => {
            console.log("getContentList res===", res)
            if (res && res.success) {
                totalCount.value = res.body.total
                tableData.value = res.body?.list || []
            } else {
                feedbackToast({ message: "获取数据异常", error })
            }
        })
        .catch((error) => {
            feedbackToast({ message: "获取数据异常", error })
        })
}
// 查询所有会话场景类型
const querySceneTypeDict = () => {
    getSceneTypeList()
        .then((res) => {
            if (res && res.success) {
                let list = res.body || []
                let temp = []
                list.forEach((item) => {
                    temp.push({ value: item.diBehaviorSceneTypeId, label: item.sceneTypeName })
                })
                sceneTypeOptions.value = temp
            } else {
                feedbackToast({ message: "获取数据异常", error })
            }
        })
        .catch((error) => {
            feedbackToast({ message: "获取数据异常", error })
        })
    // getAllDictionary(["behaviorSceneType"])
    //     .then((res) => {
    //         console.log("getAllDictionary res===", res)
    //         if (res && res.success) {
    //             let list = res.body?.behaviorSceneType || []
    //             let temp = []
    //             list.forEach((item) => {
    //                 temp.push({ value: item.codeValue, label: item.codeName })
    //             })
    //             sceneTypeOptions.value = temp
    //         } else {
    //             feedbackToast({ message: "获取数据异常", error })
    //         }
    //     })
    //     .catch((error) => {
    //         feedbackToast({ message: "获取数据异常", error })
    //     })
}
// 获取字典名称
const getSceneTypeName = (value) => {
    if (!value) return
    if (sceneTypeOptions.value.length > 0) {
        let option = sceneTypeOptions.value.find((item) => item.value == value)
        return option?.label
    } else {
        return value
    }
}
// 查询
const onQuery = () => {
    console.log("查询")
    queryList()
}
// 重置
const onReset = () => {
    keyword.value = ""
    form.sceneName = ""
    form.sceneType = ""
    form.conversatioName = ""
    form.phone = ""
    form.userName = ""
    form.runStatus = ""
    pageNo.value = 1
    queryList()
}
// 搜索框回车后查询
const keywordSearch = () => {
    pageNo.value = 1
    queryList()
}
// pagesize触发查询
const handleSizeChange = (val) => {
    pageSize.value = val
    pageNo.value = 1
    queryList()
}
// 分页查询
const handleCurrentChange = (val) => {
    pageNo.value = val
    queryList()
}
// 打开详情页面
const openDetail = (row) => {
    currentConversation.value = row
    detailsDialogVisible.value = true
}
// 高级搜索展开/收起
const toggleSearchCondition = () => {
    expandSearch.value = !expandSearch.value
    nextTick(() => {
        setTableHeight()
    })
}
// 设置表格高度
const setTableHeight = () => {
    tableHeight.value = monitorManageContainer.value?.clientHeight - 60 - headerContainer.value?.offsetHeight - 40 - 42
}
// 多选操作
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}
// 归档会话
const archiveConversation = (data) => {
    // 批量归档时data=null
    let message = data ? `确认归档此会话?` : `已选${multipleSelection.value.length}条会话，确认批量归档会话?`
    ElMessageBox.confirm(message, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
    })
        .then(() => {
            let groupIds = []
            if (data) {
                groupIds = [data.imGroupId]
            } else {
                archiveLoading.value = true
                multipleSelection.value.forEach((item) => {
                    groupIds.push(item.imGroupId)
                })
            }
            let params = { groupIds: groupIds }
            archiveSession(params)
                .then((res) => {
                    if (!res || !res.success) {
                        const errMsg = res?.head?.respMsg || "归档失败"
                        feedbackToast({ message: errMsg, error: true })
                        return
                    }

                    let result = res.body || []
                    const failList = result.filter((item) => !item.success)
                    if (failList.length === 0) {
                        feedbackToast({ message: "归档成功" })
                    } else {
                        let message = ""
                        if (data) {
                            message = failList[0].errMsg ? failList[0].errMsg : `归档失败`
                        } else {
                            message = `<div>${failList.length}条会话归档失败，失败原因：</div>`
                            failList.forEach((item) => {
                                message += `<div style="margin-top:10px">${item.groupName}，${item.errMsg}</div>`
                            })
                        }
                        let duration = 3000 + (failList.length - 1) * 1000
                        feedbackToast({ message: message, error: true, duration: duration, dangerouslyUseHTMLString: true })
                    }
                    if (failList.length !== result.length) {
                        queryList()
                    }
                })
                .catch((error) => feedbackToast(error))
                .finally(() => {
                    archiveLoading.value = false
                })
        })
        .catch(() => {})
}

const autoLogin = async () => {
    // let loginLoading = ElLoading.service({
    //     lock: true,
    //     text: "登录中",
    //     background: "rgba(0, 0, 0, 0.7)"
    // })
    console.log("autoLogin：登录中")
    try {
        let token = getCookie("portal-oss-token")
        if (unifrom == "cndids" && token) {
            userStore.updateUrlCode(true)
            let params = {
                token: token
            }
            let res = await loginCndids(params)
            if (res && res.success && res.body) {
                userStore.afterLoginSuccess(res)
                loginState.value = "success"
            } else {
                feedbackToast({ message: "登录失败", error })
            }
        } else {
            loginState.value = "success"
        }
    } catch (error) {
        feedbackToast({ message: "登录失败", error })
    }
    console.log("autoLogin：登录结束")
    // loginLoading?.close()
}

onMounted(() => {
    autoLogin()
    setTableHeight()
    window.addEventListener("resize", () => {
        setTableHeight()
    })
})
watch(
    () => loginState.value,
    () => {
        if (loginState.value == "success") {
            queryList()
            querySceneTypeDict()
            conversationStore.getGlobalConfig()
        }
    }
)
</script>

<style scoped lang="less">
.monitor-manage {
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    background: #e9ebf4;
    header {
        width: 100%;
        padding: 10px 10px 5px;
        box-sizing: border-box;
        background: #fff;
        .search-input {
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
        .search-content {
            width: 100%;
            margin-top: 10px;
            background: #fff;
            /deep/ .el-form-item {
                width: 33.33%;
                padding: 5px 20px !important;
                box-sizing: border-box;
                margin: 0px !important;
                .el-form-item__label {
                    color: #2d3040 !important;
                }
            }
            .button-group {
                width: 100% !important;
                display: inline-flex;
                justify-content: flex-end;
                padding: 5px 20px;
                box-sizing: border-box;
            }
        }
    }
    .main-container {
        width: 100%;
        margin-top: 10px;
        padding: 10px 10px 0px;
        box-sizing: border-box;
        background: #fff;
        display: flex;
        flex-wrap: wrap;
        .header-content {
            width: 100%;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            .tip {
                margin-left: 10px;
                color: #2d304099;
                font-size: 13px;
                display: flex;
                align-items: center;
                /deep/ .el-icon {
                    margin-right: 2px;
                }
            }
        }
        /deep/ th {
            background: #eff3f8 !important;
            font-weight: normal !important;
            color: #2d3040 !important;
            border-right: 1px solid #fff !important;
        }
        /deep/ tr {
            color: #2d3040 !important;
        }
        .active-col {
            padding: 0px 2px;
            box-sizing: border-box;
            color: #47e;
            cursor: pointer;
        }
        .text-underline {
            text-decoration: underline;
        }
    }
    .pagination-container {
        width: 100%;
        height: 60px;
        padding: 0px 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        background: #fff;
    }
}
</style>
<style lang="less">
.monitor-details-dialog {
    padding: 0px !important;
    min-width: 1380px;
    .el-dialog__header {
        display: none !important;
    }
    .el-dialog__body {
        width: 100% !important;
        height: 100% !important;
        overflow: hidden !important;
        background: #e9ebf4 !important;
    }
}
.monitor-notification {
    width: fit-content !important;
}
</style>
