<template>
    <div class="process">
        <div class="select-wrapper">
            <img src="@/assets/images/icons/icon_role.png" />
            <el-select class="role-select" v-model="roleValue">
                <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>
        <div class="btn-wrapper">
            <SummaryButton label="全部" :count="statusCounts.all" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'all'" @click="onStatusFilter('all')" />
            <SummaryButton label="已完成" :count="statusCounts.completed" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'completed'" @click="onStatusFilter('completed')" />
            <SummaryButton label="待执行" :count="statusCounts.pending" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'pending'" @click="onStatusFilter('pending')" />
            <SummaryButton label="执行中" :count="statusCounts.executing" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'executing'" @click="onStatusFilter('executing')" />
            <SummaryButton
                label="超时"
                :count="statusCounts.timeout"
                direction="vertical"
                width="calc(20% - 6px)"
                :isSelected="statusValue == 'timeout'"
                @click="onStatusFilter('timeout')"
                type="danger"
            />
        </div>
        <div class="list-wrapper">
            <el-scrollbar :class="[showRecoveryBtn ? 'list-scrollbar' : 'list-scrollbar-100']">
                <template v-for="(item, i) in filteredTimelineData" :key="i">
                    <StepCard class="process-step-card" :hideLine="i == filteredTimelineData.length - 1 && !item.expanded" :expanded="item.expanded" :type="item.isTimeout ? 'danger' : ''">
                        <template #icon>
                            <ExecutingIcon v-if="item.status === 'executing'" :type="item.isTimeout ? 'danger' : 'primary'" />
                            <CompletedIcon v-if="item.status === 'completed'" :type="item.isTimeout ? 'danger' : 'primary'" />
                            <PendingIcon v-if="item.status === 'pending'" />
                        </template>
                        <template #content>
                            <div class="timepoint">
                                <img v-if="item.isKeyPoint" src="@/assets/images/icons/icon_flag.png" />
                                {{ item.timepoint }}
                            </div>
                            <div class="title">
                                <el-tooltip popper-class="process-tooltip" placement="top-start" trigger="click" effect="light">
                                    <template #content>
                                        <div class="desc-tooltip">
                                            <div>标准动作：{{ item.standardAction }}</div>
                                            <div><span>工具：</span>{{ item.tool }}</div>
                                        </div>
                                    </template>
                                    <img v-if="item.isTimeout" class="tooltip-icon" src="@/assets/images/icons/icon_info_red.png" />
                                    <img v-else class="tooltip-icon" src="@/assets/images/icons/icon_info_blue.png" />
                                </el-tooltip>
                                <span class="title-text"> {{ item.activityName }}</span>
                                <img
                                    v-if="item.child && item.child.length > 0"
                                    class="expand-icon"
                                    :class="{ rotated: item.expanded }"
                                    src="@/assets/images/icons/icon_arrow_down.png"
                                    @click.stop="toggleExpand(item)"
                                />
                            </div>
                        </template>
                        <template #rightTop v-if="item.standardTime != 'T0'">
                            <img class="role-img" v-if="item.isTimeout" src="@/assets/images/icons/icon_role_red.png" />
                            <img class="role-img" v-else src="@/assets/images/icons/icon_role_blue.png" />
                            {{ item.roleName }}
                        </template>
                        <template #rightBottom v-if="item.standardTime != 'T0'">
                            <ActionButton
                                :label="item.status === 'completed' ? '查看' : '完成'"
                                :type="item.isTimeout ? 'danger' : 'primary'"
                                showTooltip
                                :tooltipContent="{
                                    title: item.activityName,
                                    time: item.completionTime,
                                    name: item.executeUserName,
                                    artificialTaskResult: item.artificialTaskResult || item.taskResult,
                                    taskResult: item.taskResult
                                }"
                                @onTooltipBtnClick="
                                    (taskResult) => {
                                        onComplete(item, taskResult)
                                    }
                                "
                            ></ActionButton>
                            <ActionButton :type="item.isTimeout ? 'danger' : 'primary'" label="启动" v-if="item.status === 'pending'" @click="onReady(item)"> </ActionButton>
                            <ActionButton v-if="item.status === 'executing' && !item.isMyTask && item.imUserIds" label="催单" :type="item.isTimeout ? 'danger' : 'primary'" @click="onRemind(item)">
                            </ActionButton>
                        </template>
                    </StepCard>
                    <template v-if="item.child && item.expanded">
                        <StepCard
                            class="process-step-card"
                            isChild
                            :isLast="j == item.child.length - 1"
                            :hideLine="j == item.child.length - 1 && i == filteredTimelineData.length - 1"
                            v-for="(child, j) in item.child"
                            :key="i + '_' + j"
                        >
                            <template #icon>
                                <StatusDot :type="child.isTimeout ? 'danger' : 'primary'" />
                            </template>
                            <template #content>
                                <div class="timepoint"><img v-if="child.isKeyPoint" src="@/assets/images/icons/icon_flag.png" /> {{ child.timepoint }}</div>
                                <div class="title"></div>
                            </template>
                            <template #rightTop>
                                <img class="role-img" v-if="child.isTimeout" src="@/assets/images/icons/icon_role_red.png" />
                                <img class="role-img" v-else src="@/assets/images/icons/icon_role_blue.png" />
                                {{ child.roleName }}
                            </template>
                            <template #rightBottom>
                                <ActionButton
                                    :label="child.status === 'completed' ? '查看' : '完成'"
                                    :type="child.isTimeout ? 'danger' : 'primary'"
                                    showTooltip
                                    :tooltipContent="{
                                        title: child.activityName,
                                        time: child.completionTime,
                                        name: child.executeUserName,
                                        artificialTaskResult: child.artificialTaskResult || child.taskResult,
                                        taskResult: child.taskResult
                                    }"
                                    @onTooltipBtnClick="
                                        (taskResult) => {
                                            onComplete(child, taskResult)
                                        }
                                    "
                                ></ActionButton>
                                <ActionButton label="启动" v-if="child.status === 'pending'" :type="child.isTimeout ? 'danger' : 'primary'" @click="onReady(child)"> </ActionButton>
                                <ActionButton
                                    v-if="child.status === 'executing' && !child.isMyTask && child.imUserIds"
                                    label="催单"
                                    :type="child.isTimeout ? 'danger' : 'primary'"
                                    @click="onRemind(child)"
                                >
                                </ActionButton>
                            </template>
                        </StepCard>
                    </template>
                </template>
                <el-empty v-if="filteredTimelineData.length == 0" description="暂无数据" />
            </el-scrollbar>
            <div class="process-footer" v-if="showRecoveryBtn">
                <SummaryButton width="100%" @click="onFailBack">
                    <el-icon color="#1279fd" size="17"><Promotion /></el-icon>
                    故障恢复
                </SummaryButton>
            </div>
        </div>
    </div>
</template>

<script setup>
import SummaryButton from "./components/customButtonGroup/summaryButton.vue"
import StepCard from "./components/stepCard"
import StatusDot from "./components/statusIconGroup/statusDot"
import ExecutingIcon from "./components/statusIconGroup/executingIcon"
import CompletedIcon from "./components/statusIconGroup/completedIcon"
import PendingIcon from "./components/statusIconGroup/pendingIcon"
import ActionButton from "./components/customButtonGroup/actionButton.vue"
import useSendMessage from "@/hooks/useSendMessage"
import useConversationStore from "@/store/modules/conversation"
import useUserStore from "@/store/modules/user"
import useEmergencyOperate from "./useEmergencyOperate.js"
const userStore = useUserStore()
const conversationStore = useConversationStore()
const { onReady, onComplete, onRemind, onFailBack } = useEmergencyOperate()
let roleValue = ref("-1")
let roleOptions = computed(() => {
    const emerRoleOptions = conversationStore.storeEmergencySchedulingInfo?.emerRoleOptions || []
    return [{ label: "全部角色", value: "-1" }, { label: "数字员工", value: "-2" }, ...emerRoleOptions]
})
const showRecoveryBtn = computed(() => {
    return conversationStore.storeEmergencySchedulingInfo?.failBackStatus === 0 // 故障恢复执行状态 0否 1是
})
let statusValue = ref("all")
let expandedState = ref({})
const timelineData = computed(() => {
    return formatProcessList(conversationStore.storeEmergencySchedulingInfo.processList)
})
// 历程数据转换
const formatProcessList = (originalList) => {
    if (originalList?.length == 0) return []

    // 按processCode分组并排序
    const grouped = originalList.reduce((acc, item) => {
        ;(acc[item.processCode] = acc[item.processCode] || []).push(item)
        return acc
    }, {})

    // 提取前缀
    const extractPrefix = (standardTime) => {
        const match = standardTime.match(/^([A-Za-z]+\d+)/)
        return match ? match[1] : null
    }

    const prefixMap = {}
    const groupMemberList = conversationStore.storeCurrentGroupMemberList || []

    // 转换数据结构
    return Object.values(grouped).map((group) => {
        const createItem = (item, isParent, isFirstForPrefix) => {
            const taskStatus = item.taskStatus
            const isTimeout = item.timeOutStatus == "1"
            let executeUserName = ""
            if (item.executeUser) {
                executeUserName = groupMemberList?.find((user) => user.imUserId == item.executeUser)?.nickname || ""
            }
            let isMyTask = false
            if (item.imUserIds) {
                let imUserIdArry = item.imUserIds.split(",") || []
                if (imUserIdArry.includes(userStore.selfInfo.userID)) {
                    isMyTask = true
                }
            }
            const scheduledTimeFormat = item.scheduledTime ? `(${item.scheduledTime?.substr(5, 2)}/${item.scheduledTime?.substr(8, 2)} ${item.scheduledTime?.substr(11, 5)})` : ""
            return {
                ...item,
                timepoint: `${item.standardTime ? item.standardTime : ""} ${scheduledTimeFormat}`,
                isKeyPoint: isParent && isFirstForPrefix,
                isTimeout: isTimeout,
                status: taskStatus == "2" ? "completed" : taskStatus == "1" ? "executing" : "pending",
                roleName: roleOptions.value?.find((option) => option.value == item.roleId)?.label || "",
                executeUserName: executeUserName,
                isMyTask: isMyTask
            }
        }
        const processGroup = (items, isParent) => {
            return items.map((item, index) => {
                const prefix = extractPrefix(item.standardTime)
                let isFirstForPrefix = false

                if (prefix && isParent) {
                    if (!prefixMap[prefix]) {
                        prefixMap[prefix] = true
                        isFirstForPrefix = true
                    }
                }

                return createItem(item, isParent, isFirstForPrefix)
            })
        }

        const parent = processGroup([group[0]], true)[0]
        parent.expanded = false

        if (group.length > 1) {
            parent.child = processGroup(group.slice(1), false)
        }

        return parent
    })
}
// 展开/收起子项
const toggleExpand = (item) => {
    const key = item.mtEmergencyProcessInstId
    expandedState.value[key] = !expandedState.value[key]
}
// 状态统计数据
const statusCounts = computed(() => {
    let processCounts = conversationStore.storeEmergencySchedulingInfo?.processCounts
    const counts = {
        all: conversationStore.storeEmergencySchedulingInfo?.processList?.length || 0,
        completed: 0,
        executing: 0,
        timeout: 0,
        pending: 0
    }
    if (!processCounts) return counts
    let taskStatusStat = processCounts.taskStatusStat
    for (let key in taskStatusStat) {
        if (key == "2") {
            counts.completed = taskStatusStat[key]
        } else if (key == "1") {
            counts.executing = taskStatusStat[key]
        } else if (key == "0") {
            counts.pending = taskStatusStat[key]
        }
    }
    let timeOutStatusStat = processCounts.timeOutStatusStat
    for (let key in timeOutStatusStat) {
        if (key == "1") {
            counts.timeout = timeOutStatusStat[key]
        }
    }
    return counts
})
// 过滤后的列表数据
const filteredTimelineData = computed(() => {
    return timelineData.value
        .map((item) => {
            // 检查是否匹配过滤条件
            const matchesRole = roleValue.value == "-1" || (roleValue.value == "-2" && item.loopStatus == "1") || item.roleId == roleValue.value
            const matchesStatus = statusValue.value == "all" || (statusValue.value == "timeout" && item.isTimeout) || statusValue.value == item.status
            let hasMatchingChild = false
            // 处理子项
            let filteredItem = { ...item }
            if (item.child) {
                const filteredChildren = item.child.filter((child) => {
                    const childMatchesRole = roleValue.value == "-1" || (roleValue.value == "-2" && child.loopStatus == "1") || child.roleId == roleValue.value
                    const childMatchesStatus = statusValue.value == "all" || (statusValue.value == "timeout" && child.isTimeout) || statusValue.value == child.status
                    return childMatchesRole && childMatchesStatus
                })

                if (filteredChildren.length > 0) {
                    hasMatchingChild = true
                    filteredItem.child = filteredChildren
                } else {
                    delete filteredItem.child
                }
            }
            // 设置展开状态
            const key = item.mtEmergencyProcessInstId
            filteredItem.expanded = expandedState.value[key]
            // 如果自身匹配或有匹配的子项则显示
            return (matchesRole && matchesStatus) || hasMatchingChild ? filteredItem : null
        })
        .filter(Boolean)
})

// 监听过滤条件变化，自动展开包含匹配项的父项
watch([roleValue, statusValue], () => {
    timelineData.value.forEach((item) => {
        if (item.child) {
            // 定义 matchesRole 变量
            const matchesRole = roleValue.value === "-1" || item.roleId === roleValue.value
            const matchesStatus = statusValue.value === "all" || (statusValue.value === "timeout" && item.isTimeout) || statusValue.value === item.status
            const hasMatchingChild = item.child.some((child) => {
                const childMatchesRole = roleValue.value === "-1" || child.roleId === roleValue.value
                const childMatchesStatus = statusValue.value === "all" || (statusValue.value === "timeout" && child.isTimeout) || statusValue.value === child.status
                return childMatchesRole && childMatchesStatus
            })
            const key = item.mtEmergencyProcessInstId
            // 只有在父项没有匹配但子项匹配的情况下才自动展开
            if (!(matchesRole && matchesStatus) && hasMatchingChild) {
                expandedState.value[key] = true
            }
        }
    })
})
// 状态过滤
const onStatusFilter = (value) => {
    statusValue.value = value
}
</script>

<style scoped lang="less">
.process {
    width: 100%;
    height: 100%;
    .select-wrapper {
        width: 100%;
        height: 44px;
        background: #fff;
        border-bottom: 1px solid #dadbe3;
        padding: 0px 12px 0px 8px;
        box-sizing: border-box;
        display: flex;
        img {
            width: 44px;
            height: 44px;
            margin-top: -6px;
        }
        .role-select {
            flex: 1;
            /deep/ .el-select__wrapper {
                background: #eff0f6 !important;
                border-radius: 18px !important;
            }
            /deep/ .el-select__wrapper {
                box-shadow: none !important;
            }
            /deep/ .el-select__icon {
                font-size: 18px !important;
                color: #666 !important;
            }
            /deep/ .el-select__placeholder {
                color: #333 !important;
            }
        }
    }
    .btn-wrapper {
        width: 100%;
        height: 64px;
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
    }
    .list-wrapper {
        width: calc(100% - 24px);
        height: calc(100% - 44px - 64px - 10px);
        margin: 0px 12px;
        padding: 10px 0px;
        box-sizing: border-box;
        background: #fff;
        border-radius: 10px;
        .list-scrollbar-100 {
            height: 100%;
        }
        .list-scrollbar {
            height: calc(100% - 50px);
        }
        .process-footer {
            height: 50px;
            padding: 0px 55px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            // img {
            //     width: 24px;
            //     height: 24px;
            //     margin-right: 4px;
            // }
            /deep/ .el-icon {
                margin-right: 4px;
            }
        }
    }
    /deep/ .process-step-card {
        .timepoint {
            width: 100%;
            text-indent: 24px;
            // font-weight: 500;
            font-size: 13px;
            color: #999999;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            img {
                height: 13px;
                position: absolute;
                top: 2px;
                left: 10px;
            }
        }
        .title {
            width: 100%;
            min-width: 1px;
            min-height: 21px;
            margin-top: 4px;
            display: flex;
            align-items: center;
            .tooltip-icon {
                width: 16px;
                height: 16px;
                margin: 0px 4px;
                cursor: pointer;
                &:active {
                    opacity: 0.8;
                }
            }
            .title-text {
                max-width: calc(100% - 60px);
                // font-weight: 500;
                font-size: 15px;
                color: #333333;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .expand-icon {
                width: 12px;
                cursor: pointer;
                margin-left: 4px;
                transition: transform 0.5s ease;
            }
            .expand-icon.rotated {
                transform: rotate(-180deg);
            }
        }
        .role-img {
            height: 14px;
            margin-right: 2px;
        }
    }
}
</style>
<style lang="less">
.process-tooltip {
    max-width: 280px;
    padding: 0px 4px;
    background: #ffffff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.33);
    border: none !important;
    .desc-tooltip div {
        padding: 10px 12px;
        font-size: 14px;
        color: #333333;
        span {
            color: #1279fd;
        }
        &:last-child {
            border-top: 1px solid #e7e6ec;
        }
    }
}
</style>
