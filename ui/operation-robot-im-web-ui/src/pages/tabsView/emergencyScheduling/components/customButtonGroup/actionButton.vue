<template>
    <template v-if="showTooltip">
        <el-tooltip ref="tooltipRef" popper-class="action-button-tooltip" placement="top-end" trigger="click" effect="light" @hide="onHide">
            <template #content>
                <slot name="tooltip">
                    <div class="tooltip-content">
                        <div class="title">
                            {{ tooltipContent?.title }}
                            <el-icon @click="onClose"><CloseBold /></el-icon>
                        </div>
                        <div class="time" v-show="tooltipContent?.time">{{ tooltipContent?.time }}</div>
                        <div class="name" v-show="tooltipContent?.name">{{ tooltipContent?.name }}</div>
                        <div class="arrow-row" v-if="!tooltipContent?.time && !tooltipContent?.name"></div>
                        <div class="info">
                            <div class="arrow-group">
                                <el-icon @click="arrowState = 'left'" size="14" :color="arrowState == 'left' ? '#47e' : '#333'"><ArrowLeftBold /></el-icon>
                                <el-icon @click="arrowState = 'right'" size="14" :color="arrowState == 'right' ? '#47e' : '#333'"><ArrowRightBold /></el-icon>
                            </div>
                            <el-input v-show="arrowState === 'left'" v-model="artificialTaskResult" type="textarea" rows="5" placeholder="请输入任务结果" clearable />
                            <el-scrollbar class="task-result" v-show="arrowState === 'right'">
                                {{ taskResult }}
                                <div class="empty" v-if="!taskResult">暂无数据</div>
                            </el-scrollbar>
                        </div>
                        <div class="footer" v-show="arrowState === 'left'">
                            <el-button class="tooltip-button--primary" @click="onConfirm">{{ tooltipContent?.confirmBtn || "保存" }}</el-button>
                        </div>
                    </div>
                </slot>
            </template>
            <div :class="['action-button', typeClass]" v-bind="$attrs">{{ label }}</div>
        </el-tooltip>
    </template>
    <template v-else>
        <div :class="['action-button', typeClass]" v-bind="$attrs">{{ label }}</div>
    </template>
</template>

<script setup>
import { feedbackToast } from "@/utils/common"
const $emits = defineEmits(["onTooltipBtnClick"])
const props = defineProps({
    label: {
        type: String,
        default: "查看"
    },
    type: {
        type: String,
        default: "primary"
    },
    showTooltip: {
        type: Boolean,
        default: false
    },
    tooltipContent: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
const arrowState = ref("left")
const tooltipRef = ref(null)
// 计算按钮类型样式
const typeClass = computed(() => {
    if (!props.type) return ""
    return `action-button--${props.type}`
})

const taskResult = ref(props.tooltipContent?.taskResult || "")
const artificialTaskResult = ref(props.tooltipContent?.artificialTaskResult || "")

const onClose = () => {
    tooltipRef.value.hide()
}

let isCancel = true
const onHide = () => {
    if (isCancel) {
        artificialTaskResult.value = props.tooltipContent?.artificialTaskResult || ""
    } else {
        isCancel = true
    }
}

const onConfirm = () => {
    if (!artificialTaskResult.value) {
        feedbackToast({ message: "任务结果不允许为空", error: true })
        return
    }
    isCancel = false
    onClose()
    $emits("onTooltipBtnClick", artificialTaskResult.value)
}
</script>

<style lang="less" scoped>
.action-button {
    padding: 2px 10px;
    font-size: 13px;
    border-radius: 10px 0px;
    margin-left: 2px;
    cursor: pointer;
    &:active {
        opacity: 0.8;
    }
}
.action-button--primary {
    color: #fff;
    background-color: #1279fd;
}
.action-button--success {
    // background-color: #2fb500;
}
.action-button--warning {
    // background-color: #ff9a26;
}
.action-button--danger {
    color: #fff;
    background-color: #ff4242;
}
</style>
<style lang="less">
.action-button-tooltip {
    min-width: 90px;
    width: 300px;
    padding: 0px 4px;
    background: #ffffff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.33);
    box-sizing: border-box;
    border: none !important;
    .tooltip-content {
        width: 100%;
        position: relative;
        .title {
            width: 100%;
            height: 46px;
            line-height: 46px;
            padding: 0px 10px;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 15px;
            color: #333333;
            border-bottom: 1px solid #e7e6ec;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .el-icon {
                color: #666;
                font-size: 17px;
                position: absolute;
                top: 14px;
                right: 10px;
                cursor: pointer;
                &:active {
                    opacity: 0.8;
                }
            }
        }
        .time,
        .name {
            padding: 5px 10px 0px;
            box-sizing: border-box;
            font-size: 14px;
            color: #999;
        }
        .arrow-row {
            width: 100%;
            height: 20px;
        }
        .info {
            padding: 5px 10px 16px;
            box-sizing: border-box;
            color: #333;
            font-size: 14px;
            position: relative;
            .arrow-group {
                position: absolute;
                right: 8px;
                top: -18px;
                z-index: 2;
                .el-icon {
                    cursor: pointer;
                    margin: 0px 2px;
                    &:hover {
                        opacity: 0.6;
                    }
                }
            }
            .task-result {
                width: 100%;
                height: 159px;
                .empty {
                    width: 100%;
                    height: 100px;
                    line-height: 100px;
                    text-align: center;
                    color: #b2b2b2;
                }
            }
        }
        .footer {
            width: 100%;
            height: 44px;
            padding: 0px 10px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            .tooltip-button--primary {
                width: 86px;
                font-size: 14px;
                font-weight: normal;
                border-radius: 5px !important;
                background-color: #1279fd !important;
                border-color: #1279fd !important;
                color: #ffffff !important;
                &:hover,
                &:focus {
                    background-color: #0d68e0 !important;
                    border-color: #0d68e0 !important;
                }
                &:active {
                    background-color: #0750b3 !important;
                    border-color: #0750b3 !important;
                }
            }
        }
    }
}
</style>
