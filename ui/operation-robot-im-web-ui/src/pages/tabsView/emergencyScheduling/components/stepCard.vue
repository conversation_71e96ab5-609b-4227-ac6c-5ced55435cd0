<template>
    <div class="step-card" :class="{ 'step-card-danger': type == 'danger', 'step-card-content-expanded': expanded, 'child-card': isChild, 'child-card-is-last': isChild && isLast }" v-bind="$attrs">
        <div class="icon-line">
            <div class="icon-wrapper"><slot name="icon"></slot></div>
            <div class="line" v-if="!hideLine"></div>
        </div>
        <div class="content-wrapper" :class="{ 'child-content-is-last': isChild && isLast }">
            <slot name="content"></slot>
            <div class="right-top">
                <slot name="rightTop"></slot>
            </div>
            <div class="right-bottom">
                <slot name="rightBottom"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    isLast: {
        type: <PERSON><PERSON>an,
        default: false
    },
    expanded: {
        type: Boolean,
        default: false
    },
    isChild: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: ""
    },
    hideLine: {
        type: Boolean,
        default: false
    }
})
</script>

<style lang="less" scoped>
.step-card {
    width: 100%;
    padding: 4px 10px;
    box-sizing: border-box;
    display: flex;
    .icon-line {
        width: 40px;
        display: flex;
        justify-content: center;
        position: relative;
        .icon-wrapper {
            position: absolute;
            top: 12px;
            z-index: 2;
        }
        .line {
            width: 2px;
            height: calc(100% + 10px);
            background: #f1f1f1;
            position: absolute;
            top: 12px;
            z-index: 1;
        }
    }
    .content-wrapper {
        width: calc(100% - 40px);
        height: 64px;
        background: #f1f7ff;
        border-radius: 10px;
        transition: border-radius 0.5s ease;
        display: flex;
        align-content: center;
        flex-wrap: wrap;
        position: relative;
        .right-top {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 13px;
            color: #666666;
            display: flex;
            align-items: center;
        }
        .right-bottom {
            display: flex;
            position: absolute;
            right: 0px;
            bottom: 0px;
        }
    }
}
.step-card-danger {
    .content-wrapper {
        background: #fff5f5;
    }
}
.step-card-content-expanded {
    padding-bottom: 0px !important;
    .content-wrapper {
        border-radius: 5px 5px 0px 0px !important;
    }
}
.child-card {
    padding: 0px 10px !important;
    .content-wrapper {
        background: #f1f1f6;
        border-radius: 0px;
        border-bottom: 1px solid #e5e5e5;
    }
    .child-content-is-last {
        border-bottom: none !important;
        border-radius: 0px 0px 5px 5px !important;
    }
}
.child-card-is-last {
    padding: 0px 10px 4px !important;
}
</style>
