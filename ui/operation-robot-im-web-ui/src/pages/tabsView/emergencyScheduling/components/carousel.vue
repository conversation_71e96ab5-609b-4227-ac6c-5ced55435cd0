<template>
    <div class="carousel-wrapper" :style="wrapperStyle">
        <div class="carousel-container" @mouseenter="stopAutoPlay" @mouseleave="startAutoPlay">
            <div class="list-container">
                <div class="carousel-content" :style="{ transform: `translateX(-${(currentPage - 1) * 100}%)` }">
                    <div v-for="(page, index) in pages" :key="index" class="carousel-page">
                        <slot :items="page"></slot>
                    </div>
                </div>
            </div>
        </div>
        <div class="carousel-dots" v-if="hasMultiplePages">
            <span
                v-for="(_, index) in pages"
                :key="index"
                :class="['dot', { active: index + 1 === currentPage }]"
                @click="goToPage(index + 1)"
                @mouseenter="stopAutoPlay"
                @mouseleave="startAutoPlay"
            ></span>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue"

const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    itemsPerPage: {
        type: Number,
        default: 6
    },
    autoPlay: {
        type: Boolean,
        default: true
    },
    interval: {
        type: Number,
        default: 4000
    },
    height: {
        type: [Number, String],
        default: "auto"
    }
})

const currentPage = ref(1)
let autoPlayTimer = null

// 将数据分组成页
const pages = computed(() => {
    const result = []
    const total = props.list.length

    for (let i = 0; i < total; i += props.itemsPerPage) {
        result.push(props.list.slice(i, Math.min(i + props.itemsPerPage, total)))
    }

    return result
})

// 是否有多页
const hasMultiplePages = computed(() => pages.value.length > 1)

// 计算wrapper高度样式
const wrapperStyle = computed(() => {
    if (!hasMultiplePages.value) return {}

    // 多页时，使用props.height
    const heightValue = typeof props.height === "number" ? `${props.height}px` : props.height
    return {
        height: heightValue
    }
})

const nextPage = () => {
    if (currentPage.value < pages.value.length) {
        currentPage.value++
    } else {
        currentPage.value = 1 // 循环到第一页
    }
}

const goToPage = (page) => {
    currentPage.value = page
}

const startAutoPlay = () => {
    // 确保不会重复启动定时器
    stopAutoPlay()

    // 只有多页且需要自动播放时才启动
    if (props.autoPlay && hasMultiplePages.value) {
        autoPlayTimer = setInterval(nextPage, props.interval)
    }
}

const stopAutoPlay = () => {
    if (autoPlayTimer) {
        clearInterval(autoPlayTimer)
        autoPlayTimer = null
    }
}

// 监听列表变化，重置页码和自动播放
watch(
    () => props.list,
    () => {
        currentPage.value = 1 // 重置到第一页

        // 如果开启了自动播放，需要重新启动
        stopAutoPlay()
        startAutoPlay()
    },
    { deep: true }
)

// 监听多页状态变化，处理自动播放
watch(
    () => hasMultiplePages.value,
    (hasMultiple) => {
        if (hasMultiple) {
            startAutoPlay()
        } else {
            stopAutoPlay()
        }
    }
)

onMounted(() => {
    // 启动自动播放（如果需要）
    startAutoPlay()
})

onBeforeUnmount(() => {
    // 清理定时器
    stopAutoPlay()
})
</script>

<style scoped lang="less">
.carousel-wrapper {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.carousel-container {
    position: relative;
    flex: 1;
    overflow: hidden;
}

.list-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

.carousel-content {
    display: flex;
    width: 100%;
    transition: transform 0.5s ease;
}

.carousel-page {
    width: 100%;
    flex: 0 0 100%;
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 8px;

    .dot {
        width: 10px;
        height: 4px;
        background-color: #b7d1ff;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
            background-color: #0076f6;
            width: 16px;
        }
    }
}
</style>
