<template>
    <el-dialog class="team-member-manage" v-model="dialogVisible" width="1000" top="5vh" destroy-on-close :modal="false" :close-on-click-modal="false" :show-close="false">
        <template #header>
            <div class="header-content">
                <div class="title">编辑人员</div>
                <el-icon size="22" color="#575966" @click="onCancel"><Close /></el-icon>
            </div>
        </template>
        <div class="left-content">
            <div class="search-input">
                <img class="search-icon" src="@/assets/images/icons/icon_btn_search.png" />
                <el-input ref="searchInputRef" v-model="keywork" placeholder="搜索" clearable @input="onSearchInput" />
            </div>
            <div class="type-tabs">
                <div class="item item-active">按好友选</div>
            </div>
            <el-scrollbar class="user-list">
                <div
                    class="user-item"
                    :class="{
                        'user-item-active': isUserSelected(data),
                        'user-item-disabled': isUserDisabled(data)
                    }"
                    v-for="data in friendList"
                    :key="data.userID"
                    @click="onUserClick(data)"
                >
                    <div class="check-icon">
                        <el-icon v-if="isUserSelected(data)" color="#fff" size="13"><Check /></el-icon>
                    </div>
                    <img class="avatar" src="@/assets/images/icons/icon_avatar_light.png" />
                    <div class="user-name">{{ data.nickname || data.userID }}</div>
                </div>
                <div class="friend-empty" v-if="contactStore.storeFriendList.length == 0">
                    <el-icon size="16px"><Warning /></el-icon>暂无好友，请添加好友
                </div>
                <div class="friend-empty" v-else-if="friendList.length == 0">暂无数据</div>
            </el-scrollbar>
        </div>
        <div class="right-content">
            <div class="selected-title">
                <div>选择联系人</div>
                <div class="action-btns" @click="handleBatchDelete">
                    <img class="delete-icon" src="@/assets/images/icons/icon_btn_delete.png" />
                    批量删除
                </div>
            </div>
            <el-scrollbar class="selected-user-list">
                <el-table :data="tableData" style="width: 100%" height="465" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" label="序号" width="55" align="center" :selectable="(row) => row.imUserIdFrom === userStore.selfInfo.userID" />
                    <el-table-column prop="userName" label="姓名" align="center" width="90" />
                    <el-table-column prop="phone" label="手机号码" align="center" width="115" />
                    <el-table-column prop="roleName" label="角色" align="center" show-overflow-tooltip>
                        <template #default="scope">
                            <template v-if="scope.row.imUserIdFrom == userStore.selfInfo.userID || scope.row.imUserId == userStore.selfInfo.userID">
                                <el-select v-model="scope.row.roleId" placeholder="请选择">
                                    <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </template>
                            <template v-else>
                                {{ roleOptions.find((option) => option.value == scope.row.roleId)?.label || "" }}
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column prop="majorName" label="专业" align="center" width="115">
                        <template #default="scope">
                            <template v-if="scope.row.imUserIdFrom == userStore.selfInfo.userID || scope.row.imUserId == userStore.selfInfo.userID">
                                <el-select v-model="scope.row.major" placeholder="请选择" clearable>
                                    <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </template>
                            <template v-else>
                                {{ majorOptions.find((option) => option.value == scope.row.major)?.label || "" }}
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="70">
                        <template #default="scope">
                            <div
                                class="delete-btn"
                                v-if="scope.row.imUserIdFrom == userStore.selfInfo.userID && scope.row.imUserId != userStore.selfInfo.userID"
                                @click="handleDelete(scope.$index, scope.row)"
                            >
                                删除
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-scrollbar>
            <div class="footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onAddOrEditSave" color="#1279fd">保存</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { Search, Warning, Check } from "@element-plus/icons-vue"
import { ElMessageBox } from "element-plus"
import { feedbackToast } from "@/utils/common"
import useUserStore from "@/store/modules/user"
import useContactStore from "@/store/modules/contact"
import useConversationStore from "@/store/modules/conversation"
import { updateUserList } from "@/api/emergencyScheduling"

const $emits = defineEmits(["update:visible"])
const $props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
})

const userStore = useUserStore()
const contactStore = useContactStore()
const conversationStore = useConversationStore()

const keywork = ref("")
const friendList = ref([])
const dialogVisible = ref(false)
const multipleSelection = ref([])
const tableData = ref([])
let teamMemberOriginalList = []
let roleOptions = computed(() => {
    return conversationStore.storeEmergencySchedulingInfo.emerRoleOptions || []
})
const majorOptions = computed(() => {
    return conversationStore.storeEmergencySchedulingInfo.emerMajorOptions || []
})

// 监听对话框显示状态
watch(
    () => $props.visible,
    () => {
        dialogVisible.value = $props.visible
        nextTick(() => {
            if (dialogVisible.value) {
                initTableData()
            } else {
                keywork.value = ""
            }
        })
    }
)

// 监听好友列表数据变化
watch(
    () => contactStore.storeFriendList,
    (data) => {
        if (data) {
            nextTick(() => {
                friendList.value = toRaw(data)
            })
        }
    },
    {
        immediate: true,
        deep: true
    }
)

// 判断用户是否被选中
const isUserSelected = (user) => {
    return tableData.value?.some((item) => item.imUserId === user.userID)
}

// 判断用户是否禁用（自己或已在团队中的成员）
const isUserDisabled = (data) => {
    // 禁用自己
    if (data.userID === userStore.selfInfo.userID) {
        return true
    }

    // 检查是否是团队中已有的成员
    const existingMember = tableData.value.find((item) => item.imUserId === data.userID)
    if (existingMember && existingMember.manageStatus === "1") {
        return true
    }

    return false
}

// 初始化表格数据
const initTableData = () => {
    const teamMembers = JSON.parse(JSON.stringify(conversationStore.storeEmergencySchedulingInfo?.teamMerberList || []))
    teamMembers.forEach((item) => {
        item.roleId = item.roleId?.toString()
        item.major = item.major?.toString()
        item.manageStatus = "1"
    })
    tableData.value = teamMembers
    teamMemberOriginalList = JSON.parse(JSON.stringify(teamMembers))
}

// 选择人员
const onUserClick = (data) => {
    if (isUserDisabled(data)) return

    const existingIndex = tableData.value.findIndex((item) => item.imUserId === data.userID)
    if (existingIndex !== -1) {
        // 如果是新添加的成员（manageStatus === '0'），则可以移除
        if (tableData.value[existingIndex].manageStatus === "0") {
            tableData.value.splice(existingIndex, 1)
        }
    } else {
        // 如果是专业负责人添加的成员，默认角色为“运维人员”，默认专业为该专业负责人所属的专业
        let selfItem = tableData.value.find((item) => item.imUserId === userStore.selfInfo.userID)
        let majorName = ""
        let major = ""
        let roleName = ""
        let roleId = ""
        if (selfItem?.roleId === "5") {
            majorName = selfItem.majorName
            major = selfItem.major
            roleName = roleOptions.value.find((item) => item.value == "5")?.label
            roleId = "5"
        } else {
            roleName = roleOptions.value[0].label
            roleId = roleOptions.value[0].value
        }
        // 添加新成员到第一行
        tableData.value.unshift({
            userId: data.userID,
            userName: data.nickname || data.userID,
            imUserId: data.userID,
            majorName: majorName,
            major: major,
            roleName: roleName,
            roleId: roleId,
            imUserIdFrom: userStore.selfInfo.userID,
            manageStatus: "0" // 0表示新增成员，可以直接删除
        })
    }
}

// 处理多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除单个成员
const handleDelete = (index, row) => {
    ElMessageBox.confirm(`确认从团队成员中移除${row.userName}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
    }).then(async () => {
        if (row.manageStatus == "1") {
            // 对于团队原有成员，需要调用删除接口
            let list = JSON.parse(JSON.stringify(teamMemberOriginalList))
            let operList = list.filter((item) => item.imUserId != row.imUserId)
            onUpdateUserList(operList).then((rs) => {
                if (rs) {
                    tableData.value.splice(index, 1)
                    feedbackToast({ message: "删除成功", error: false })
                }
            })
        } else {
            // 对于新增成员，直接从表格中移除
            tableData.value.splice(index, 1)
            feedbackToast({ message: "删除成功", error: false })
        }
    })
}

// 批量删除
const handleBatchDelete = () => {
    if (multipleSelection.value.length === 0) {
        feedbackToast({ message: "请选择要删除的成员", type: "warning" })
        return
    }
    ElMessageBox.confirm(`已选${multipleSelection.value.length}个成员，确认批量删除?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
    }).then(() => {
        // 区分需要调用接口删除的成员和直接删除的成员
        const apiDeleteMembers = multipleSelection.value.filter((member) => member.manageStatus == "1")
        // 对团队原有成员调用删除接口
        if (apiDeleteMembers.length > 0) {
            let list = JSON.parse(JSON.stringify(teamMemberOriginalList))
            // 从list中过滤出不包含apiDeleteMembers的数据
            let operList = list.filter((item) => !apiDeleteMembers.some((member) => member.imUserId == item.imUserId))
            onUpdateUserList(operList).then((rs) => {
                if (rs) {
                    feedbackToast({ message: "批量删除成功", error: false })
                    // 从表格中移除所有选中的成员
                    tableData.value = tableData.value.filter((item) => {
                        return !multipleSelection.value.some((selected) => selected.imUserId == item.imUserId)
                    })
                    multipleSelection.value = []
                }
            })
        } else {
            // 从表格中移除所有选中的成员
            tableData.value = tableData.value.filter((item) => {
                return !multipleSelection.value.some((selected) => selected.imUserId == item.imUserId)
            })
            multipleSelection.value = []
            feedbackToast({ message: "批量删除成功", error: false })
        }
    })
}
// 检查数据有没有新增或者专业和角色有没有变化
const hasChanges = () => {
    const currentMap = new Map(tableData.value.map((item) => [item.imUserId, item]))
    const originalMap = new Map(teamMemberOriginalList.map((item) => [item.imUserId, item]))
    // 检查新增或修改
    for (const [imUserId, current] of currentMap) {
        const original = originalMap.get(imUserId)
        if (!original || current.major != original.major || current.roleId != original.roleId) {
            return true
        }
    }
    return false
}
// 人员新增、修改
const onAddOrEditSave = () => {
    if (!hasChanges()) {
        console.log("没有修改内容")
        dialogClose()
        return
    }
    onUpdateUserList(tableData.value).then((rs) => {
        if (rs) {
            feedbackToast({ message: "保存成功", error: false })
            dialogClose()
        }
    })
}
// 调人员更新的接口
const onUpdateUserList = async (operList) => {
    let result = false
    try {
        let groupId = conversationStore.storeCurrentConversation.groupID || ""
        let userList = []
        operList.forEach((item) => {
            userList.push({
                imUserId: item.imUserId || "",
                major: item.major || "",
                roleId: item.roleId || ""
            })
        })
        let params = {
            groupId: groupId,
            userList: userList
        }
        let res = await updateUserList(params)
        if (res && res.success) {
            result = true
        } else {
            result = false
            feedbackToast({ message: res?.head?.respMsg || "应急团队修改接口异常", error: true })
        }
    } catch (error) {
        result = false
    }
    return new Promise((resolve) => {
        resolve(result)
    })
}

// 取消
const onCancel = () => {
    if (hasChanges()) {
        ElMessageBox.confirm("当前编辑内容未保存，确认关闭？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
        }).then(() => {
            tableData.value = JSON.parse(JSON.stringify(teamMemberOriginalList))
            dialogClose()
        })
    } else {
        dialogClose()
    }
}

const dialogClose = () => {
    multipleSelection.value = []
    dialogVisible.value = false
    $emits("update:visible", false)
}

// 搜索功能
const onSearchInput = () => {
    if (!keywork.value) {
        // 如果搜索关键词为空，显示完整好友列表
        friendList.value = toRaw(contactStore.storeFriendList)
        return
    }

    // 过滤好友列表，匹配昵称或用户ID（不区分大小写）
    const keyword = keywork.value.toLowerCase()
    friendList.value = toRaw(contactStore.storeFriendList).filter((friend) => {
        const nickname = (friend.nickname || "").toLowerCase()
        const userID = (friend.userID || "").toLowerCase()
        return nickname.includes(keyword) || userID.includes(keyword)
    })
}
</script>

<style lang="less">
.team-member-manage {
    background: #f5f5f5;
    padding: 0px !important;
    background: rgb(254 254 255) !important;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.33);
    box-sizing: border-box;
    .el-dialog__header {
        width: 100%;
        height: 44px;
        padding: 0px 12px !important;
        box-sizing: border-box;
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        .header-content {
            width: 100%;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {
                font-size: 16px !important;
                font-weight: 500 !important;
                color: #1279fd;
            }
            .el-icon {
                cursor: pointer;
                &:hover {
                    color: #1279fd !important;
                }
            }
        }
    }
    .el-dialog__body {
        width: 100%;
        display: flex;
    }
    .left-content {
        width: calc(100% - 650px);
        // padding: 12px 20px 20px;
        border-right: 1px solid rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
        background: #eff0f6;
        .search-input {
            width: 100%;
            background: #fff;
            padding: 12px 12px 5px 5px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .search-icon {
                width: 36px;
                height: 36px;
                flex-shrink: 0;
            }
            .el-input__wrapper {
                border-radius: 15px !important;
                background: #eff0f6 !important;
                box-shadow: none !important;
            }
            .el-input {
                height: 30px;
            }
        }
        .type-tabs {
            width: 100%;
            height: 40px;
            padding: 0px 12px;
            box-sizing: border-box;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .item {
                margin: 0xp 10px;
                box-sizing: border-box;
                position: relative;
            }
            .item-active {
                color: #1279fd;
                &::after {
                    content: "";
                    position: absolute;
                    top: calc(100% + 6px);
                    left: 0px;
                    width: 100%;
                    height: 4px;
                    border-radius: 2px;
                    background: #0076f6;
                }
            }
        }
        .user-list {
            width: 100%;
            height: 460px;
            margin-top: 10px;
            padding: 0px 12px;
            box-sizing: border-box;
            .user-item {
                width: 100%;
                padding: 5px 0px;
                // margin: 2px 0px 0px -24px !important;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                cursor: pointer;
                div {
                    font-size: 15px;
                    color: #2d3040;
                    padding: 0px 5px;
                    box-sizing: border-box;
                }
                .check-icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 4px;
                    background: #fafafc;
                    border: 1px solid #e4e7ed;
                    box-sizing: border-box;
                    border-radius: 2px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .avatar {
                    width: 40px;
                    height: 40px;
                }
                .user-name {
                    min-width: 80px;
                    max-width: calc(100% - 62px);
                    color: #333;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                &:active {
                    opacity: 0.7;
                }
            }
            .user-item-active {
                .check-icon {
                    background: #1279fd;
                    border: 1px solid #1279fd;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .user-name {
                    color: #1279fd;
                }
            }
            .user-item-disabled {
                cursor: default;
                .check-icon {
                    background: #d3d3d3;
                    border: 1px solid #d3d3d3;
                }
                .user-name {
                    color: #333 !important;
                }
                &:active {
                    opacity: 1 !important;
                }
            }
            .friend-empty {
                width: 100%;
                padding: 20px 0px;
                color: #b2b2b2;
                display: flex;
                justify-content: center;
                align-items: center;
                i {
                    margin-right: 5px;
                }
            }
        }
    }
    .right-content {
        width: 650px;
        padding: 12px 10px 20px;
        box-sizing: border-box;
        .selected-title {
            width: 100%;
            height: 36px;
            padding: 0px 10px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 15px;
            color: #666;
            .action-btns {
                display: flex;
                align-items: center;
                font-size: 15px;
                color: #333333;
                cursor: pointer;
                .delete-icon {
                    width: 36px;
                    height: 36px;
                    cursor: pointer;
                }
                &:hover {
                    color: #1279fd;
                }
            }
        }
        .selected-user-list {
            width: 100%;
            height: 465px;
            margin-top: 5px;
            .delete-btn {
                color: #1279fd;
                cursor: pointer;
            }
            .el-checkbox__input.is-checked .el-checkbox__inner {
                background-color: #1279fd;
                border-color: #1279fd;
            }
            .el-checkbox__input.is-indeterminate .el-checkbox__inner {
                background-color: #1279fd;
                border-color: #1279fd;
            }
            th {
                background: #eff3f8 !important;
                font-weight: normal !important;
                color: #333 !important;
                border-right: 1px solid #fff !important;
            }
            tr {
                color: #333 !important;
            }
            .el-checkbox.is-disabled {
                display: none !important;
            }
        }
        .footer {
            width: 100%;
            margin-top: 12px;
            padding-right: 10px;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-end;
        }
    }
}
</style>
