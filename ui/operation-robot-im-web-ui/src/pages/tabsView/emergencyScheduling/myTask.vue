<template>
    <div class="my-task">
        <div class="btn-wrapper">
            <SummaryButton label="全部" :count="statusCounts.all" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'all'" @click="onStatusFilter('all')" />
            <SummaryButton label="已完成" :count="statusCounts.completed" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'completed'" @click="onStatusFilter('completed')" />
            <SummaryButton label="待执行" :count="statusCounts.pending" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'pending'" @click="onStatusFilter('pending')" />
            <SummaryButton label="执行中" :count="statusCounts.executing" direction="vertical" width="calc(20% - 6px)" :isSelected="statusValue == 'executing'" @click="onStatusFilter('executing')" />
            <SummaryButton
                label="超时"
                :count="statusCounts.timeout"
                direction="vertical"
                width="calc(20% - 6px)"
                :isSelected="statusValue == 'timeout'"
                @click="onStatusFilter('timeout')"
                type="danger"
            />
        </div>
        <div class="list-wrapper">
            <el-scrollbar class="list-scrollbar">
                <template v-for="(item, i) in filteredTaskData" :key="i">
                    <StepCard class="my-task-card" :hideLine="i == filteredTaskData.length - 1 && !item.expanded" :expanded="item.expanded" :type="item.isTimeout ? 'danger' : ''">
                        <template #icon>
                            <ExecutingIcon v-if="item.status === 'executing'" :type="item.isTimeout ? 'danger' : 'primary'" />
                            <CompletedIcon v-if="item.status === 'completed'" :type="item.isTimeout ? 'danger' : 'primary'" />
                            <PendingIcon v-if="item.status === 'pending'" />
                        </template>
                        <template #content>
                            <div class="timepoint">
                                <img v-if="item.isKeyPoint" src="@/assets/images/icons/icon_flag.png" />
                                {{ item.timepoint }}
                            </div>
                            <div class="title">
                                <el-tooltip popper-class="my-task-tooltip" placement="top-start" trigger="click" effect="light">
                                    <template #content>
                                        <div class="tooltip-info">
                                            <div>标准动作：{{ item.standardAction }}</div>
                                            <div><span>工具：</span>{{ item.tool }}</div>
                                        </div>
                                    </template>
                                    <img v-if="item.isTimeout" class="tooltip-icon" src="@/assets/images/icons/icon_info_red.png" />
                                    <img v-else class="tooltip-icon" src="@/assets/images/icons/icon_info_blue.png" />
                                </el-tooltip>
                                <span class="title-text"> {{ item.activityName }}</span>
                                <img
                                    v-if="item.child && item.child.length > 0"
                                    class="expand-icon"
                                    :class="{ rotated: item.expanded }"
                                    src="@/assets/images/icons/icon_arrow_down.png"
                                    @click.stop="toggleExpand(item)"
                                />
                            </div>
                        </template>
                        <template #rightBottom>
                            <ActionButton
                                :label="item.status === 'completed' ? '查看' : '完成'"
                                :type="item.isTimeout ? 'danger' : 'primary'"
                                showTooltip
                                :tooltipContent="{
                                    title: item.activityName,
                                    time: item.completionTime,
                                    name: item.executeUserName,
                                    artificialTaskResult: item.artificialTaskResult || item.taskResult,
                                    taskResult: item.taskResult
                                }"
                                @onTooltipBtnClick="
                                    (taskResult) => {
                                        onComplete(item, taskResult)
                                    }
                                "
                            ></ActionButton>
                            <ActionButton :type="item.isTimeout ? 'danger' : 'primary'" label="启动" v-if="item.status === 'pending'" @click="onReady(item)"> </ActionButton>
                        </template>
                    </StepCard>
                    <template v-if="item.child && item.expanded">
                        <StepCard
                            class="my-task-card"
                            isChild
                            :isLast="j == item.child.length - 1"
                            :hideLine="j == item.child.length - 1 && i == filteredTaskData.length - 1"
                            v-for="(child, j) in item.child"
                            :key="i + '_' + j"
                        >
                            <template #icon>
                                <StatusDot :type="child.isTimeout ? 'danger' : 'primary'" />
                            </template>
                            <template #content>
                                <div class="timepoint">
                                    <img v-if="child.isKeyPoint" src="@/assets/images/icons/icon_flag.png" />
                                    {{ child.timepoint }}
                                </div>
                                <div class="title child-title">
                                    <span class="title-text"> {{ child.activityName }}</span>
                                </div>
                            </template>
                            <template #rightBottom>
                                <ActionButton
                                    :label="child.status === 'completed' ? '查看' : '完成'"
                                    :type="child.isTimeout ? 'danger' : 'primary'"
                                    showTooltip
                                    :tooltipContent="{
                                        title: child.activityName,
                                        time: child.completionTime,
                                        name: child.executeUserName,
                                        artificialTaskResult: child.artificialTaskResult || child.taskResult,
                                        taskResult: child.taskResult
                                    }"
                                    @onTooltipBtnClick="
                                        (taskResult) => {
                                            onComplete(child, taskResult)
                                        }
                                    "
                                ></ActionButton>
                                <ActionButton :type="child.isTimeout ? 'danger' : 'primary'" label="启动" v-if="child.status === 'pending'" @click="onReady(child)"> </ActionButton>
                            </template>
                        </StepCard>
                    </template>
                </template>
                <el-empty v-if="filteredTaskData.length == 0" description="暂无数据" />
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup>
import SummaryButton from "./components/customButtonGroup/summaryButton.vue"
import StepCard from "./components/stepCard"
import StatusDot from "./components/statusIconGroup/statusDot"
import ExecutingIcon from "./components/statusIconGroup/executingIcon"
import CompletedIcon from "./components/statusIconGroup/completedIcon"
import PendingIcon from "./components/statusIconGroup/pendingIcon"
import ActionButton from "./components/customButtonGroup/actionButton.vue"
import useConversationStore from "@/store/modules/conversation"
import useEmergencyOperate from "./useEmergencyOperate.js"
const conversationStore = useConversationStore()
const { onReady, onComplete } = useEmergencyOperate()

let statusValue = ref("all")
let expandedState = ref({})
const taskData = computed(() => {
    return formatTaskList(conversationStore.storeEmergencySchedulingInfo?.myTaskList)
})
// 我的任务数据转换
const formatTaskList = (originalList) => {
    if (originalList?.length == 0) return []

    const groupMemberList = conversationStore.storeCurrentGroupMemberList || []
    const formattedList = originalList.map((item) => {
        const taskStatus = item.taskStatus
        const isTimeout = item.timeOutStatus == "1"
        let executeUserName = ""
        if (item.executeUser) {
            executeUserName = groupMemberList?.find((user) => user.imUserId == item.executeUser)?.nickname || ""
        }
        const scheduledTimeFormat = item.scheduledTime ? `(${item.scheduledTime?.substr(5, 2)}/${item.scheduledTime?.substr(8, 2)} ${item.scheduledTime?.substr(11, 5)})` : ""
        return {
            ...item,
            timepoint: `${item.standardTime ? item.standardTime : ""} ${scheduledTimeFormat}`,
            isTimeout: isTimeout,
            status: taskStatus == "2" ? "completed" : taskStatus == "1" ? "executing" : "pending",
            roleNames: conversationStore.storeEmergencySchedulingInfo?.emerRoleOptions?.find((option) => option.value == item.roleId)?.label || "",
            executeUserName: executeUserName
        }
    })

    return formattedList
}
// 展开/收起子项
const toggleExpand = (item) => {
    const key = item.mtEmergencyProcessInstId
    expandedState.value[key] = !expandedState.value[key]
}
// 状态统计数据
const statusCounts = computed(() => {
    let myTaskCounts = conversationStore.storeEmergencySchedulingInfo?.myTaskCounts
    const counts = {
        all: conversationStore.storeEmergencySchedulingInfo?.myTaskList?.length || 0,
        completed: 0,
        executing: 0,
        timeout: 0,
        pending: 0
    }
    if (!myTaskCounts) return counts
    let taskStatusStat = myTaskCounts.taskStatusStat
    for (let key in taskStatusStat) {
        if (key == "2") {
            counts.completed = taskStatusStat[key]
        } else if (key == "1") {
            counts.executing = taskStatusStat[key]
        } else if (key == "0") {
            counts.pending = taskStatusStat[key]
        }
    }
    let timeOutStatusStat = myTaskCounts.timeOutStatusStat
    for (let key in timeOutStatusStat) {
        if (key == "1") {
            counts.timeout = timeOutStatusStat[key]
        }
    }
    return counts
})
// 过滤后的列表数据
const filteredTaskData = computed(() => {
    return taskData.value
        .map((item) => {
            // 检查是否匹配过滤条件
            const matchesStatus = statusValue.value === "all" || (statusValue.value === "timeout" && item.isTimeout) || statusValue.value === item.status
            let hasMatchingChild = false
            // 处理子项
            let filteredItem = { ...item }
            if (item.child) {
                const filteredChildren = item.child.filter((child) => {
                    const childMatchesStatus = statusValue.value === "all" || (statusValue.value === "timeout" && child.isTimeout) || statusValue.value === child.status
                    return childMatchesStatus
                })

                if (filteredChildren.length > 0) {
                    hasMatchingChild = true
                    filteredItem.child = filteredChildren
                } else {
                    delete filteredItem.child
                }
            }
            // 设置展开状态
            const key = item.mtEmergencyProcessInstId
            filteredItem.expanded = expandedState.value[key]
            // 如果自身匹配或有匹配的子项则显示
            return matchesStatus || hasMatchingChild ? filteredItem : null
        })
        .filter(Boolean)
})

// 监听过滤条件变化，自动展开包含匹配项的父项
watch([statusValue], () => {
    taskData.value.forEach((item) => {
        if (item.child) {
            // 定义 matchesRole 变量
            const matchesStatus = statusValue.value === "all" || (statusValue.value === "timeout" && item.isTimeout) || statusValue.value === item.status
            const hasMatchingChild = item.child.some((child) => {
                const childMatchesStatus = statusValue.value === "all" || (statusValue.value === "timeout" && child.isTimeout) || statusValue.value === child.status
                return childMatchesStatus
            })
            const key = item.mtEmergencyProcessInstId
            // 只有在父项没有匹配但子项匹配的情况下才自动展开
            if (!matchesStatus && hasMatchingChild) {
                expandedState.value[key] = true
            }
        }
    })
})
// 状态过滤
const onStatusFilter = (value) => {
    statusValue.value = value
}
</script>

<style scoped lang="less">
.my-task {
    width: 100%;
    height: 100%;
    .btn-wrapper {
        width: 100%;
        height: 64px;
        padding: 10px;
        box-sizing: border-box;
        border-top: 1px solid #dadbe3;
        display: flex;
        justify-content: space-between;
    }
    .list-wrapper {
        width: calc(100% - 24px);
        height: calc(100% - 64px - 10px);
        margin: 0px 12px;
        padding: 10px 0px;
        box-sizing: border-box;
        background: #fff;
        border-radius: 10px;
        .list-scrollbar {
            height: 100%;
        }
    }
    /deep/ .my-task-card {
        width: 100%;
        padding: 4px 10px;
        box-sizing: border-box;
        display: flex;
        .timepoint {
            width: 100%;
            text-indent: 24px;
            // font-weight: 500;
            font-size: 13px;
            color: #999999;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            img {
                height: 13px;
                position: absolute;
                top: 2px;
                left: 10px;
            }
        }
        .title {
            width: 100%;
            margin-top: 4px;
            display: flex;
            align-items: center;
            .tooltip-icon {
                width: 16px;
                height: 16px;
                margin: 0px 4px;
                cursor: pointer;
                &:active {
                    opacity: 0.8;
                }
            }
            .title-text {
                max-width: calc(100% - 60px);
                // font-weight: 500;
                font-size: 16px;
                color: #333333;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .expand-icon {
                width: 12px;
                cursor: pointer;
                margin-left: 4px;
                transition: transform 0.5s ease;
            }
            .expand-icon.rotated {
                transform: rotate(-180deg);
            }
        }
        .child-title {
            .title-text {
                margin-left: 24px;
            }
        }
    }
}
</style>
<style lang="less">
.my-task-tooltip {
    max-width: 280px;
    padding: 0px 4px;
    background: #ffffff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.33);
    border: none !important;
    .tooltip-info div {
        padding: 10px 12px;
        font-size: 14px;
        color: #333333;
        span {
            color: #1279fd;
        }
        &:last-child {
            border-top: 1px solid #e7e6ec;
        }
    }
}
</style>
