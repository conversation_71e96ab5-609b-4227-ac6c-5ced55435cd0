import { createRouter, createWebHashHistory } from "vue-router"

const router = createRouter({
    history: createWebHashHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: "/login",
            name: "login",
            component: () => import("@/pages/login/index.vue"),
            meta: {
                title: "登录"
            }
        },
        {
            path: "/error",
            name: "error",
            component: () => import("@/pages/layout/error.vue"),
            meta: {
                title: "登录"
            }
        },
        {
            path: "/chat",
            name: "layout",
            component: () => import("@/pages/layout/index.vue"),
            meta: {
                title: "运维智能体平台"
            }
        },
        {
            path: "/monitorManage",
            name: "monitorManage",
            component: () => import("@/pages/monitor/monitorManage/index.vue"),
            meta: {
                title: "会话监控管理"
            }
        },
        {
            path: "/monitorDetails",
            name: "monitorDetails",
            component: () => import("@/pages/monitor/monitorDetails/index.vue"),
            meta: {
                title: "会话实例监控"
            }
        },
        {
            path: "/circuitRoute",
            name: "circuitRoute",
            component: () => import("@/pages/circuitRoute/CircuitRoute.vue"),
            meta: {
                title: "电路路由详情"
            }
        },
        {
            path: "/transFailureImpact",
            name: "transFailureImpact",
            component: () => import("@/pages/chat/components/chatContent/message/customFaultImpactAnalysis/transFailureImpact/TransFailureImpact.vue"),
            meta: {
                title: "传输光缆故障影响面分析详情"
            }
        },
        {
            path: "/vipCircuitImpactDetails",
            name: "vipCircuitImpactDetails",
            component: () => import("@/pages/chat/components/chatContent/message/customVipCircuitImpact/vipCircuitImpactDetails.vue"),
            meta: {
                title: "重要客户电路影响"
            }
        },
        {
            path: "/problemFeedback",
            name: "problemFeedback",
            component: () => import("@/pages/problemFeedback/index.vue"),
            meta: {
                title: "问题反馈"
            }
        },
    ]
})

router.beforeEach((to, from, next) => {
    if (to.meta && to.meta.title) {
        document.title = to.meta.title
    } else {
        document.title = "运维智能体平台"
    }
    next()
})

export default router
