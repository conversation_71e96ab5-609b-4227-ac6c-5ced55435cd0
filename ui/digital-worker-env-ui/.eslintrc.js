// https://eslint.org/docs/user-guide/configuring

module.exports = {
    root: true,
    parserOptions: {
        parser: 'babel-eslint'
    },
    env: {
        browser: true,
    },
    extends: [
        // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
        // consider switching to `plugin:vue/strongly-recommended` or `plugin:vue/recommended` for stricter rules.
        'plugin:vue/essential',
        // https://github.com/standard/standard/blob/master/docs/RULES-en.md
        'standard'
    ],
    // required to lint *.vue files
    plugins: [
        'vue'
    ],
    // add your custom rules here
    rules: {
        // allow async-await
        'generator-star-spacing': 'off',
        // allow debugger during development
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        // 缩进为4个空格
        'indent': ['error', 4, { SwitchCase: 1 }], // off（忽略）不验证规则、warn警告、error报错
        // 分号设置
        'semi': 1, // 0忽略 1、警告 2、报错
        // 末尾空行不配置
        'eol-last': 0,
        // 允许使用 ==
        'eqeqeq': 0,
        // 函数圆括号之前没有空格 never禁止后面有空格，always需要后面有空格
        'space-before-function-paren': ['error', 'never']
    }
}
