{"name": "example", "version": "1.0.0", "description": "数字员工构建环境", "author": "os.kangzhg <<EMAIL>>", "private": false, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "node build/updatedev.js && npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "test": "npm run unit", "lint": "eslint --fix --ext .js,.vue src test/unit", "build": "node build/build.js"}, "dependencies": {"axios": "^0.18.0", "crypto-js": "^4.2.0", "echarts": "^4.9.0", "element-ui": "^2.13.0", "js-cookie": "^3.0.5", "oss-common": "^1.4.23", "qiankun": "^2.10.16", "sass": "^1.26.3", "v-charts": "^1.19.0", "vue": "^2.5.2", "vue-i18n": "^8.11.2", "vue-router": "^3.0.1"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@kazupon/vue-i18n-loader": "^0.3.0", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^21.0.2", "babel-loader": "^7.1.1", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "co": "^4.6.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "execa": "^4.0.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jest": "^22.0.4", "jest-serializer-vue": "^0.3.0", "less": "^3.9.0", "less-loader": "^5.0.0", "listr": "^0.14.3", "node-notifier": "^5.1.2", "node-sass": "^4.13.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "sync-request": "^6.1.0", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "urllib": "^2.34.1", "vue-jest": "^1.0.2", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}