worker_processes  1;

error_log  /var/log/nginx/error.log warn;

events {
    worker_connections  1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
	server_tokens off;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '

                      '"$http_user_agent" "$http_x_forwarded_for"';
 limit_conn_zone $binary_remote_addr zone=one:10m;
    access_log  /var/log/nginx/access.log  main;

    client_header_timeout  600;
    client_body_timeout    600;
    send_timeout          600;

    sendfile    on;
	
    keepalive_timeout  600;
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_connect_timeout 10m;
    proxy_max_temp_file_size 100m;
 
    client_header_buffer_size 1024k;
    large_client_header_buffers 4 1024k;
   
    client_max_body_size 100m;
	
    server {
		listen       8989;
		server_name  localhost;
		add_header 'Access-Control-Allow-Origin' '*';
		   
		location / {
			add_header Cache-Control "no-cache,no-store";
			root   /usr/share/nginx/html;
			index  index.html index.htm;
			try_files $uri $uri/ /index.html =404;
			limit_conn one 10;
			limit_rate 10240k;
		}
		location /portal-gateway/ {
			keepalive_timeout  600;
			proxy_read_timeout 600;
			proxy_send_timeout 600;
			proxy_connect_timeout 600;

			proxy_pass http://*************:42010/portal-gateway/;
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			client_max_body_size 100m;
		}
		error_page  404  /404.html;
   		error_page  504  /50x.html;
    		error_page  413  /413.html;
    		error_page  400  /400.html;
		error_page  502  /50x.html;

		location = /50x.html {
			root   html;
		}
	}
}
