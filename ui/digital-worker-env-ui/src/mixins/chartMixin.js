
import { throttle } from '@/libs/tools'
const resizeChartMethod = '$__resizeChartMethod'

export default {
    created() {
        window.addEventListener('resize', this[resizeChartMethod])
    },
    beforeDestroy() {
        window.removeEventListener('reisze', this[resizeChartMethod])
    },
    methods: {
        // 通过防抖函数来控制resize的频率
        [resizeChartMethod]: throttle(function() {
            if (this.chart) {
                console.log('sss')
                this.chart.resize()
            }
        }, 800)
    }
}
