@import "mixins/mixins";
@import "mixins/utils";
@import "input-number";
@import "tooltip";
@import "common/var";

@include b(slider) {

  @include utils-clearfix;

  @include e(runway) {
    width: 100%;
    height: $--slider-height;
    margin: $--slider-margin;
    background-color: $--slider-runway-background-color;
    border-radius: $--slider-border-radius;
    position: relative;
    cursor: pointer;
    vertical-align: middle;

    &.show-input {
      margin-right: 160px;
      width: auto;
    }

    &.disabled {
      cursor: default;

      .el-slider__bar {
        background-color: $--slider-disable-color;
      }

      .el-slider__button {
        border-color: $--slider-disable-color;
      }

      .el-slider__button-wrapper {
        &:hover,
        &.hover {
          cursor: not-allowed;
        }

        &.dragging {
          cursor: not-allowed;
        }
      }

      .el-slider__button {
        &:hover,
        &.hover,
        &.dragging {
          transform: scale(1);
        }

        &:hover,
        &.hover {
          cursor: not-allowed;
        }

        &.dragging {
          cursor: not-allowed;
        }
      }
    }
  }

  @include e(input) {
    float: right;
    margin-top: 3px;
    width: 130px;

    &.el-input-number--mini {
      margin-top: 5px;
    }

    &.el-input-number--medium {
      margin-top: 0;
    }

    &.el-input-number--large {
      margin-top: -2px;
    }
  }

  @include e(bar) {
    height: $--slider-height;
    background-color: $--slider-main-background-color;
    border-top-left-radius: $--slider-border-radius;
    border-bottom-left-radius: $--slider-border-radius;
    position: absolute;
  }

  @include e(button-wrapper) {
    height: $--slider-button-wrapper-size;
    width: $--slider-button-wrapper-size;
    position: absolute;
    z-index: 1001;
    top: $--slider-button-wrapper-offset;
    transform: translateX(-50%);
    background-color: transparent;
    text-align: center;
    user-select: none;
    line-height: normal;
    @include utils-vertical-center;

    .el-tooltip {
      vertical-align: middle;
      display: inline-block;
    }

    &:hover,
    &.hover {
      cursor: grab;
    }

    &.dragging {
      cursor: grabbing;
    }
  }

  @include e(button) {
    width: $--slider-button-size;
    height: $--slider-button-size;
    border: solid 2px $--slider-main-background-color;
    background-color: $--color-white;
    border-radius: 50%;
    transition: .2s;
    user-select: none;

    &:hover,
    &.hover,
    &.dragging {
      transform: scale(1.2);
    }

    &:hover,
    &.hover {
      cursor: grab;
    }

    &.dragging {
      cursor: grabbing;
    }
  }

  @include e(stop) {
    position: absolute;
    height: $--slider-height;
    width: $--slider-height;
    border-radius: $--border-radius-circle;
    background-color: $--slider-stop-background-color;
    transform: translateX(-50%);
  }

  @include e(marks) {
    top: 0;
    left: 12px;
    width: 18px;
    height: 100%;

    @include e(marks-text) {
      position: absolute;
      transform: translateX(-50%);
      font-size: 14px;
      color: $--color-info;
      margin-top: 15px;
    }
  }

  @include when(vertical) {
    position: relative;
    .el-slider__runway {
      width: $--slider-height;
      height: 100%;
      margin: 0 16px;
    }
    .el-slider__bar {
      width: $--slider-height;
      height: auto;
      border-radius: 0 0 3px 3px;
    }
    .el-slider__button-wrapper {
      top: auto;
      left: $--slider-button-wrapper-offset;
      transform: translateY(50%);
    }
    .el-slider__stop {
      transform: translateY(50%);
    }
    &.el-slider--with-input {
      padding-bottom: #{$--input-medium-height + 22px};
      .el-slider__input {
        overflow: visible;
        float: none;
        position: absolute;
        bottom: 22px;
        width: 36px;
        margin-top: 15px;
        .el-input__inner {
          text-align: center;
          padding-left: 5px;
          padding-right: 5px;
        }
        .el-input-number__decrease,
        .el-input-number__increase
        {
          top: $--input-small-height;
          margin-top: -1px;
          border: $--input-border;
          line-height: 20px;
          box-sizing: border-box;
          transition: $--border-transition-base;
        }
        .el-input-number__decrease {
          width: 18px;
          right: 18px;
          border-bottom-left-radius: $--input-border-radius;
        }
        .el-input-number__increase {
          width: 19px;
          border-bottom-right-radius: $--input-border-radius;
          & ~ .el-input .el-input__inner {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }
        }
        &:hover {
          .el-input-number__decrease,
          .el-input-number__increase
          {
            border-color: $--input-hover-border;
          }
        }
        &:active {
          .el-input-number__decrease,
          .el-input-number__increase
          {
            border-color: $--input-focus-border;
          }
        }
      }
    }

    @include e(marks-text) {
      margin-top: 0;
      left: 15px;
      transform: translateY(50%);
    }
  }
}
