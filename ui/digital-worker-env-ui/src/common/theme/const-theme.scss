/**
 * 颜色设置
 */

// 主要按钮、普通信息
$--color-primary: #4477EE !default;

// 成功、正确、完成的结果
$--color-success: #00BB66 !default;

// 提醒、告警的信息
$--color-warning: #FF8800 !default;

// 错误、异常的结果
$--color-danger: #EE2200 !default;


// 页面背景
body {
    font-size: 12px;
    background: #EBECF1 !important;
    font-family: Nunito Sans,Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,SimSun,sans-serif;
}

// 浅绿色
$--light-green: #E6EEF7;

// 页面背景色
$--body-background: #EBECF1;

// 白色背景色
$--color-white: #fff; 

// 浅灰色标题栏 ----》 应用 #F5F6F7
$--color-grey:#F5F6F7;

// 边线框
$--border-color-base: #E0E0E0 !default;

// 表格行、列表行分隔线、表格表头背景、中竖线分隔线
$--table-border-color: #F0F0F0;

// 表格行、列表等鼠标移入的背景颜色
$--background-color-base: #EBECF1;

// 表格行、列表等选中时的背景颜色
$--color-primary-light-9: #FFEFA3;

/**
 * 字体设置
 */

 // 首选设置 Nunito Sans

 // 标题正文
 $--color-text-primary: #2D3040;
 $--color-text-regular: #2D3040;

 // 次要文字 #575966
 $--color-text-secondary: #575966;

 // 说明、描述文字：#ABACB2
 $--color-text-placeholder: #ABACB2;


 // 链接：#3355BB

 // 描述列表中的字段名：#FF5500

 // 描述列表中的字段名：#445588


 /**
  * 其他设置
  **/
// 圆角大小
$--border-radius-base: 2px;



// 统一遮罩层  Popup 默认值即是 #000000 50%

// 弹窗、浮层投影 

// 普通面板


// 按钮

$--button-font-size: 12px;

$--button-medium-font-size: 14px;
$--button-small-font-size: 13px;
$--button-mini-font-size: 12px;

// 默认设置为小按钮
$--button-padding-vertical: 7px;
$--button-padding-horizontal: 15px;

// small按钮设置 兼容使用的message框

/// padding||Spacing|3
$--button-small-padding-vertical: 7px !default;
$--msgbox-padding-primary: 10px;
$--message-close-size: 12px !default;





/**输入类**/ 

// input 
$--input-height: 28px;
$--font-size-base: 13px;


// radio

// time

// switch   TODO==》 宽高的源代码无效，线上依赖导致无法正常更改
$--switch-on-color: $--color-success;
$--table-header-background-color: #F0F0F0;


// tag   // 有写死部分 故在源文件内修改

// table  // 间距等 有写死部分 故在源文件内修改


// dialog  // 标题背景颜色 源文件内修改
$--dialog-title-font-size: 16px;
$--dialog-padding-primary: 5px;

$--dialog-head-color: #323747;
$--dialog-content-font-size: 13px !default;

/* Typography
-------------------------- */
$--font-size-base: 13px !default;


// message
$--color-success-lighter: #E5F8EF;
$--color-warning-lighter: #FFF2E5;
$--color-danger-lighter: #FDEDED;
$--color-info-lighter: #ECF1FD;

$--color-info-border-light:  #BDCFF9;
$--color-success-border-light: #99E3C1;
$--color-warning-border-light: #FFCF99;
$--color-danger-border-light: #F8BBBB;


$--message-font-color: #2D3040;

$--message-padding: 7px 15px 7px 20px !default;

