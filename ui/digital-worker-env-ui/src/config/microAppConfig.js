// 微前端应用配置
const isDev = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost'

// 微应用配置
export const microAppConfig = {
    // 知识库搜索应用
    'kg-ui': {
        name: 'kg-ui',
        entry: isDev ? 'http://localhost:8081/' : '/kg-ui/',
        container: '#knowledge-search',
        activeRule: isDev ? '/knowledgeBaseSearch' : '/kg-ui'
    },
    
    // 思维树&思维链应用
    'mindMap-ui': {
        name: 'mindMap-ui',
        entry: isDev ? 'http://localhost:8080/' : '/mindMap-ui/',
        container: '#mindMapContainer',
        activeRule: isDev ? '/mindMap-ui' : '/mindMap-ui'
    }
}

// 获取微应用入口地址
export function getAppEntry(appName) {
    const config = microAppConfig[appName]
    if (!config) {
        console.error(`未找到微应用配置: ${appName}`)
        return `/${appName}/`
    }
    return config.entry
}

// 获取微应用完整配置
export function getAppConfig(appName, props = {}) {
    const config = microAppConfig[appName]
    if (!config) {
        console.error(`未找到微应用配置: ${appName}`)
        return null
    }
    
    return {
        ...config,
        props: {
            ...props,
            // 添加环境信息
            isDev,
            timestamp: Date.now()
        }
    }
}

// 沙箱配置
export const sandboxConfig = {
    sandbox: {
        strictStyleIsolation: false,
        experimentalStyleIsolation: true,
        loose: true
    }
}

// 生产环境特殊配置
export const productionConfig = {
    // 生产环境下的特殊配置
    baseUrl: window.location.origin
    // 可以添加更多生产环境特定的配置
}

export default {
    microAppConfig,
    getAppEntry,
    getAppConfig,
    sandboxConfig,
    productionConfig,
    isDev
}
