// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import 'oss-common/theme/variable-theme.scss' // 主题
import 'oss-common/css/index.css' // 通用样式
import 'oss-common/icon/iconfont.css' // 图标
import 'oss-common/theme/font.css' // 字体
import enLocale from 'element-ui/lib/locale/lang/en'
import './assets/css/app.css'

import VueI18n from 'vue-i18n' // 国际化
import util from './libs/util'
import { registerMicroApps, start } from 'qiankun'
import { microAppConfig, sandboxConfig } from './config/microAppConfig'

// 使用配置文件中的微应用配置
registerMicroApps([
    {
        ...microAppConfig['kg-ui'],
        props: { targetPath: '/knowledgeBaseSearch' }
    },
    {
        ...microAppConfig['mindMap-ui'],
        props: {
            // 传递给思维树子应用的属性
            showRightSuffix: true,
            isNumberPerson: false,
            routeId: null
        }
    }
], {
    beforeLoad: [
        app => {
            console.log('[LifeCycle] before load %c%s', 'color: green;', app.name);
        },
    ],
    beforeMount: [
        app => {
            console.log('[LifeCycle] before mount %c%s', 'color: green;', app.name);
        },
    ],
    afterUnmount: [
        app => {
            console.log('[LifeCycle] after unmount %c%s', 'color: green;', app.name);
        },
    ],
})
console.log('已注册微前端');
start(sandboxConfig)
console.log('qiankun 已启动');
const i18nConfig = require('../config/i18nConfig')

Vue.use(ElementUI, {
    // i18n: (key, value) => i18n.t(key, value) //注释掉默认不使用国际化英文
})

Vue.use(VueI18n)

Vue.prototype.$utils = util

Vue.config.productionTip = false

const messages = {
    [i18nConfig[0].locale]: Object.assign({}, enLocale),
}
const i18n = new VueI18n({
    locale: i18nConfig[0].locale, // 设置地区，默认为配置数组的第一位
    silentTranslationWarn: true,
    messages,
})

let useI18nLanguage = false

function loadLanguageAsync() {
    if (useI18nLanguage) {
        i18nConfig.forEach((data) => {
            let { locale } = data
            // 本地调试的时候可以把改成!==,防止国际化每次去访问国际化文件，导致加载速度较慢
            // if (process.env.NODE_ENV !== 'development') {
            //     util.ajaxMethodWidthParams(url, 'get', {}).then((res) => {
            //         i18n.mergeLocaleMessage(locale, res)
            //     })
            // } else {
            // 正式环境使用拉包
            import(`@/i18nOnline/${locale}.js`).then((result) => {
                i18n.mergeLocaleMessage(locale, result.default)
            })
            // }
        })
    } else {
        // 直接拉取本地的国际化文件
        i18nConfig.forEach((data) => {
            let { locale } = data
            import(`@/i18nOnline/${locale}.js`).then((result) => {
                i18n.mergeLocaleMessage(locale, result.default)
            })
        })
    }
}

Vue.prototype.loadLanguageAsync = loadLanguageAsync

loadLanguageAsync()

export const WhetherUseI18n = useI18nLanguage // 是否使用国际化

// 路由钩子
router.beforeEach((to, from, next) => {
    console.log('当前路由:', to.fullPath);
    if (to.meta.title) {
        document.title = to.meta.title
    }
    next()
})
/* eslint-disable no-new */
export let vm = new Vue({
    el: '#app',
    i18n,
    router,
    components: { App },
    template: '<App/>',
})
