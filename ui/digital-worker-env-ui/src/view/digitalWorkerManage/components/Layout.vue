<template>
    <div class="fullContent" ref="fullContent">
        <header ref="header" class="mb10" v-show="showTopbar || showHightSearch != 'noAll'">
            <div class="topbar" v-show="showTopbar">
                <div class="btn-group">
                    <slot name="btnSort"></slot>
                </div>
            </div>
            <div class="rightPart" v-show="showHightSearch != 'noAll'">
                <el-input v-if="alllowClearKeyWord" @keyup.enter.native="keyWordSearch" style="width: 290px;" :placeholder="placeholder ? placeholder : $t('keywordInput')" v-model="keyWord" v-show="showHightSearch != 'noKeyword'" clearable>
                    <i slot="prefix" class="el-icon-search el-input__icon" @click="keyWordSearch" style="cursor: pointer;"></i>
                </el-input>
                <el-input v-else class="searchKeyword" @keyup.enter.native="keyWordSearch" style="width: 280px" :placeholder="placeholder ? placeholder : $t('keywordInput')" v-model="keyWord" v-show="showHightSearch != 'noKeyword'">
                    <i slot="suffix" class="el-icon-search el-input__icon" style="cursor: pointer;" @click="keyWordSearch"></i>
                </el-input>
                <div class="btn-group">
                    <slot name="btnSortRight"></slot>
                </div>
                <div class="btn-group ml-10" style="display: flex; align-items: center;">
                    <el-button @click="toggleSearchCondition" v-if="isToggleSearch">
                        {{ $t('highSearch') }}
                        <i v-show="!expandSearch" class="el-icon-arrow-down el-icon--right"></i>
                        <i v-show="expandSearch" class="el-icon-arrow-up el-icon--right"></i>
                    </el-button>
                    <div class="toolbar-right" v-if="isListSwitch">
                         <div class="view-toggle">
                            <div
                                :class="['view-button', { 'active': listType == 0 }]"
                                @click="onListSwitch(0)"
                            >
                                <img
                                :src="listType == 1 ? require('../images/view-icon-active.png') : require('../images/view-icon.png')"
                                alt="网格视图"
                                class="view-icon"
                                />
                            </div>
                            <div
                                :class="['view-button', { 'active': listType == 1 }]"
                                 @click="onListSwitch(1)"
                            >
                               <img
                                :src="listType == 0 ? require('../images/view-list-active.png') : require('../images/view-list.png')"
                                alt="列表视图"
                                class="view-icon"
                            />
                            </div>
                        </div>
                    <div class="view-toggle">
                           
                        </div>
                    </div>
                    <!-- <span v-if="listType == 0 && isListSwitch" class="switch-wrap ml5"><i style="font-size: 20px" class="el-icon-notebook-2 c-primary cursor" ></i></span>
                    <span v-if="listType == 1 && isListSwitch" class="switch-wrap ml5"><i style="font-size: 20px" class="el-icon-menu c-primary cursor" @click="onListSwitch(0)"></i> </span> -->
                </div>
            </div>
            <div v-show="expandSearch" ref="expandSearch" class="search-content">
                <slot name="expandSearch"></slot>
            </div>
        </header>
        <el-container class="el-container" :style="{ height: mainHeight + 'px' }">
            <slot name="mainContent"></slot>
        </el-container>

        <el-pagination
            v-show="showPagination"
            class="page-block"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="customPageSize"
            :page-size.sync="currentPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </div>
</template>

<script>
import Card from './Card'
export default {
    components: { Card },
    props: {
        placeholder: {
            type: String,
            default: '',
        },
        total: {
            type: Number,
            default: 0,
        },
        isListSwitch: {
            type: Boolean,
            default: false,
        },
        listSwitch: {
            type: [Number, String],
            default: 0,
        },
        isToggleSearch: {
            type: Boolean,
            default: true,
        },
        isSpecilSize: {
            type: Boolean,
            default: false,
        },
        customPageSize: {
            type: Array,
            default() {
                return [20, 30, 40, 50, 100]
            },
        },
        // 是否显示分页组件
        showPagination: {
            type: Boolean,
            default() {
                return true
            },
        },
        // all-显示全部，noKeyword-不显示模糊搜索框，noAll-不显示模糊搜索框及高级搜索按钮
        showHightSearch: {
            type: String,
            default: 'all',
        },
        // 是否显示按钮插槽的内容
        showTopbar: {
            type: Boolean,
            default: true,
        },
        // 是否允许清空关键字查询条件
        alllowClearKeyWord: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            expandSearch: false,
            mainHeight: 0,
            currentPage: 1,
            keyWord: '',
            currentPageSize: 20,
            listType: this.listSwitch,
        }
    },
    watch: {
        listSwitch(val) {
            this.listType = val
        },
    },
    methods: {
        toggleSearchCondition() {
            this.expandSearch = !this.expandSearch
            this.$nextTick(() => {
                this.setContainer()
            })
        },
        setContainer() {
            let that = this
            let header = 0
            if (this.showTopbar || this.showHightSearch != 'noAll') {
                header = that.$refs.header.clientHeight
            } else{
                header = 0
            }
            let bottom = this.showPagination == true ? 60 : 0,
                expandSearchHeight
            this.$nextTick(() => {
                // expandSearchHeight = that.expandSearch? (that.$refs.expandSearch.clientHeight): 0
                let height = 0
                if (header == 0) {
                    height = that.$refs.fullContent.clientHeight - bottom - header;
                } else {
                    height = that.$refs.fullContent.clientHeight - bottom - header - 10;
                }
                that.mainHeight = height
                that.$emit('setHeight', that.mainHeight)
            })
            // setTimeout(()=>{
            //     expandSearchHeight = that.expandSearch? that.$refs.expandSearch.clientHeight:0

            //     let height = that.$refs.fullContent.clientHeight - expandSearchHeight - bottom - header;
            //     that.mainHeight = height;
            // },10)
        },
        handleSizeChange(val) {
            this.$emit('handleSizeChange', val)
        },
        handleCurrentChange(val) {
            this.$emit('handleCurrentChange', val)
        },
        keyWordSearch() {
            this.currentPage = 1
            this.$emit('keyWordSearch', this.keyWord)
        },
        //列表切换 0 列表 1 方块
        onListSwitch(listSwitch) {
            this.listType = listSwitch
            this.$emit('listSwitchChange', this.listType)
        },
        getDefaultPageSize() {
            return this.currentPageSize
        },
    },
    mounted() {
        let that = this
        this.currentPageSize = this.customPageSize[0] || 20
        that.$nextTick(() => {
            that.setContainer()
            let interval = setInterval(() => {
                if (that.$refs.fullContent && that.$refs.fullContent.clientHeight) {
                    clearInterval(interval)
                    that.setContainer()
                }
            }, 100)
        })
        window.addEventListener('resize', that.setContainer)
    },
    destroyed() {
        window.removeEventListener('resize', this.setContainer)
    },
}
</script>

<style lang="scss" scoped type="text/css">
@import '@/common/theme/const-theme.scss';
.switch-wrap {
    display: inline-block;
    vertical-align: middle;
}
.c-primary {
    color: $--color-primary;
}

.c-danger {
    color: $--color-danger;
}

.card-title {
    color: $--color-primary;
}

header {
    min-height: 52px;
    padding: 10px;
    background: $--color-white;
    .topbar {
        float: left;
        justify-content: flex-start;
        align-items: center;
        .btn-group {
            font-size: 0; // 消除按钮之间因为行内标签而默认带的间距
        }
    }
    .rightPart {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}

.el-main {
    padding: 0 20px;
    background: $--color-white;
}
.el-container {
    padding: 10px;
    background: $--color-white;
}
.page-block {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 60px;
    padding-right: 20px !important;
    background: $--color-white;
}
.toolbar-right {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.view-toggle {
  display: flex;
  align-items: center;
  
 
}

.view-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #ffffff;
 
  &:first-child{
    border-radius: 3px 0 0 3px;
    border-right: none;
  }
  &:last-child{
    border-radius: 0px 3px 3px 0px;
    border-left: none;
  }
  img{
    height: 16px;
    width: 16px;
  }
}

.view-button:hover {
  border-color: #4477EE;
}

.view-button.active {
  background-color: #4477EE;
border: 1px solid #4477EE;
}
.view-button:not(.active) {
    border: 1px solid #CED4DB;
}
</style>
