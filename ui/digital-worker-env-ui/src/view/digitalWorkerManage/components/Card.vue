<template>
    <el-card class="mtb10" :body-style="{ padding: '0px' }" style="position: relative">
        <div class="card-top">
            <slot name="top"></slot>
        </div>
        <div class="card-main" :style="bodyHeight">
            <div class="card-body">
                <slot name="body"></slot>
            </div>
            <div class="card-tag-container" v-if="showTag">
                <span class="card-tag">
                    <slot name="tag"></slot>
                </span>
            </div>
        </div>
        <!-- <div class="echarts">
            <slot name="echarts"></slot>
        </div> -->
        <div v-if="showFoot" class="bottom clearfix">
            <slot name="bottom"></slot>
        </div>
    </el-card>
</template>
<script>
export default {
    props: {
        showFoot: {
            type: Boolean,
            default: true,
        },
        showTag: {
            type: Boolean,
            default: false,
        },
        bodyHeight: {
            type: Object,
            default: () => {
                return {
                    height: 180 + 'px' + '!important',
                }
            },
        },
    },
}
</script>
<style lang="scss" scoped>
@import 'oss-common/theme/const-theme.scss';
.bottom {
    height: 50px;
    line-height: 50px;
    padding: 0 12px;
    border-top: 1px solid $--background-color-base;
}
.card-top {
    padding: 5px 12px;
    min-height: 60px;
    max-height: 60px;
    line-height: 20px;
    font-size: 15px !important;
    .card-title {
        text-decoration: none !important;
    }
}
.card-main {
    height: 141px;
}
.card-body {
    padding: 5px 12px 5px 74px;
    height: 150px;
}
.card-tag-container {
    margin-left: 15px;
    height: 25px;
    overflow: hidden;
}

.card-tag {
    background: #F5F7FA;
    border-radius: 2px;
    font-weight: 400;
    font-size: 14px;
    color: #8D9094;
    line-height: 20px;
    padding: 2px 8px;
    display: inline-block; /* 内联块元素，宽度自适应内容 */
    white-space: nowrap;
    max-width: calc(100% - 30px); /* 防止超出容器 */
    overflow: hidden;
    text-overflow: ellipsis;
}
.card-tag:hover {
    overflow-x: auto;
}
.clearfix:before,
.clearfix:after {
    display: table;
    content: '';
}

.clearfix:after {
    clear: both;
}
</style>
