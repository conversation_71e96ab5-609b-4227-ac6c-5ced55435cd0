<template>
    <div class="digital-worker-manage fullContent" v-loading="loading" element-loading-text="加载中..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
        <Layout
            ref="layout"
            :total="totalCount"
            placeholder="关键词搜索"
            @setHeight="setTableHeight"
            @keyWordSearch="keyWordSearch"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
            :isListSwitch="true"
            @listSwitchChange="listSwitchChange"
        >
            <template slot="btnSort">
                <el-button type="primary" icon="el-icon-plus" @click="add">新增</el-button>
                <el-button icon="el-icon-delete" @click="batchDelete">批量删除</el-button>
            </template>
            <template slot="expandSearch">
                <!-- 条件搜索 -->
                <FormGenerate ref="form" @searchResult="searchResult" @reset="resetResult" :renderFormRule="renderFormRule" :span="8" :searchGroup="true" :lableWidth="150" />
            </template>
            <template slot="mainContent" v-if="listType === 0">
                <div class="stats">
                    <span class="stats-text">共 <a>{{totalCount}}</a> 个数字员工</span>
                </div>
                <el-main>
                    <el-row :gutter="15">
                        <el-col :span="6" v-for="(item, index) in tableData" :key="index">
                            <Card class="mt10" :showTag="true">
                                <template slot="top">
                                    <div class="card-head">
                                        <img v-if="!item.staffUrl" class="icon" src="./images/icon_avatar_light.png" />
                                        <img v-else :src="processIconUrl(item.staffUrl)" class="icon" />
                                        <div class="produce-name" :title="item.staffName + '（' + item.displayName + '）'" @click="view(item)">{{ item.staffName }}（{{ item.displayName }}）</div>
                                        <div class="checkbox">
                                            <el-checkbox size="medium" v-model="item.checked" :disabled="item.startupStatus == '1'"></el-checkbox>
                                        </div>
                                    </div>
                                </template>
                                <template slot="body">
                                    <div style="overflow: auto">
                                        <span style="display: inline; font-size: 14px"  class="text-info" :title="item.dutyRole">{{ item.dutyRole }}</span>
                                    </div>
                                </template>
                                <template slot="tag"> {{ getPostTypeText(item.postType) }} </template>
                                <template slot="bottom">
                                    <div class="card-bottom">
                                        <div class="tag">
                                            <div v-if="item.label">
                                                <el-tag type="primary" style="margin: 0 4px" v-for="(tag, tagIndex) in item.label.split(';')" :key="tagIndex">{{ tag }}</el-tag>
                                            </div>
                                        </div>
                                        <div class="btnArea">
                                           <el-switch
                                                :width="40"
                                                v-model="item.switchValue"
                                                active-color="#0EA35D"
                                                inactive-color="#DCDCDC"
                                                @change="handleSwitchChange(item)">
                                            </el-switch>
                                            <!-- <el-button type="text" @click="view(item)">查看</el-button> -->
                                            <!-- <el-button type="text" @click="edit(item)">编辑</el-button> -->
                                            <!-- <el-button type="text" @click="del(item)">删除</el-button> -->
                                            <img src="./images/edit.png" style="margin-left: 10px;"  @click="edit(item)" width="24px" height="24px" title="编辑"/>
                                            <img src="./images/delete.png" style="margin-left: 8px;"   @click="del(item)" width="24px" height="24px" title="删除"/>
                                        </div>
                                    </div>
                                </template>
                            </Card>
                        </el-col>
                        <el-col :span="24" class="text-center noData" v-if="tableData.length == 0">暂无数据</el-col>
                    </el-row>
                </el-main>
            </template>
            <template slot="mainContent" v-if="listType === 1">
                <el-table
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                    :height="tableHeight"
                    border
                    @selection-change="handleSelectionChange"
                >
                    <template slot="empty"><p>暂无数据</p></template>
                    <el-table-column type="selection" width="50" align="center" :selectable="checkSelectable"></el-table-column>
                     <el-table-column label="数字员工名称" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div style="display: flex; align-items: center; min-width: 0;">
                                <img v-if="!scope.row.staffUrl" style="width: 32px; height: 32px; margin-right: 8px; border-radius: 50%; flex-shrink: 0;" src="./images/icon_avatar_light.png" />
                                <img v-else :src="processIconUrl(scope.row.staffUrl)" style="width: 32px; height: 32px; margin-right: 8px; border-radius: 50%; flex-shrink: 0;" />
                                <span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; min-width: 0;">{{ scope.row.staffName }}（{{ scope.row.displayName }}）</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="postType" label="岗位类型" align="center" show-overflow-tooltip>
                        <template slot-scope="scope"> {{ getPostTypeText(scope.row.postType) }} </template>
                    </el-table-column>
                    <el-table-column prop="staffNo" label="数字员工编码" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="startupStatus" label="状态" align="center" width="80px" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.startupStatus == '1' ? 'success' : 'info'" size="medium">{{ scope.row.startupStatus == '1' ? '启用' : '禁用' }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="label" label="标签" align="center" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div v-if="scope.row.label">
                                <el-tag type="primary" size="medium" style="margin: 0 4px" v-for="(tag, tagIndex) in scope.row.label.split(';')" :key="tagIndex">{{ tag }}</el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="dutyRole" label="职责角色" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" width="200" align="center">
                        <template slot-scope="scope">
                            <div style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                                <img src="./images/view.png" @click="view(scope.row)" width="18px" height="18px" title="查看" style="cursor: pointer;"/>
                                <img src="./images/edit.png" @click="edit(scope.row)" width="18px" height="18px" title="编辑" style="cursor: pointer;"/>
                                <img src="./images/delete.png" @click="del(scope.row)" width="18px" height="18px" title="删除" style="cursor: pointer;"/>
                                <el-switch
                                    v-model="scope.row.switchValue"
                                     :width="35"
                                    active-color="#0EA35D"
                                    inactive-color="#DCDCDC"
                                    @change="handleSwitchChange(scope.row)">
                                </el-switch>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </Layout>
        <el-dialog :modal="false" class="wk-details-dialog" :title="dialogTitle" :visible.sync="dialogVisible" fullscreen>
            <DigitalWorkerDetails v-if="dialogVisible" :mtPlatStaffId="currentWorkerId" :staffName="staffName" @cancel="handleDetailCancel" :viewMode="isViewMode" />
        </el-dialog>
    </div>
</template>

<script>
import Layout from './components/Layout'
import FormGenerate from 'oss-common/components/FormGenerate'
import Card from './components/Card.vue'
import DigitalWorkerDetails from '../digitalWorkerDetails/digitalWorkerDetails.vue'
import { getAllDictionary, getDigitalWorkerList, digitalWorkerStatusSwitch, digitalWorkerDelete, downloadFileUrl, editLock } from '@/api/digitalWorkerApi'

export default {
    components: {
        Layout,
        FormGenerate,
        Card,
        DigitalWorkerDetails,
    },
    data() {
        return {
            loading: false,
            listType: 0,
            // 记录总条数
            totalCount: 0,
            // 表格的高度
            tableHeight: '200',
            // 保存查询条件
            searchParam: {
                keyWord: '',
                staffName: '',
                staffNo: '',
                displayName: '',
                label: '',
                pageNo: 1,
                pageSize: 20,
            },
            tableData: [],
            // 搜索条件配置
            renderFormRule: {
                staffName: {
                    label: '数字员工名称',
                    value: null,
                    type: 'input',
                },
                staffNo: {
                    label: '数字员工工号',
                    value: null,
                    type: 'input',
                },
                displayName: {
                    label: '显示名称',
                    value: null,
                    type: 'input',
                },
                label: {
                    label: '标签',
                    value: null,
                    type: 'input',
                },
            },
            dialogVisible: false,
            dialogTitle: '',
            isViewMode: false,
            currentWorkerId: '',
            staffName: '',
        }
    },
    computed: {
        // 计算选中的数据
        selectedItems() {
            return this.tableData.filter((item) => item.checked)
        },
    },
    mounted() {
        //         'del_flag', '删除状态'
        // 'startup_status', '启用状态'
        // 'post_type', '岗位类型'
        // 'model_type', '模块类型'
        // 'competence_type', '技能类型'
        // 'app_mode', '模式'
        this.postTypeOptions = []
        this.startupStatusOptions = []
        getAllDictionary(['post_type', 'startup_status']).then((res) => {
            if (res && res.success) {
                const data = res.body
                this.postTypeOptions = data['post_type']
                this.startupStatusOptions = data['startup_status']
            } else {
                this.$message.error(res.head.respMsg || '未知错误')
            }
        })
        setTimeout(() => {
            this.queryList()
        }, 200)
    },
    methods: {
        // 判断行是否可选择（启用状态不可选）
        checkSelectable(row) {
            return row.startupStatus != '1'
        },
        // 表格和卡片切换 0 列表 1 方块
        listSwitchChange(value) {
            this.listType = value
        },
        // 新增
        add() {
            this.dialogTitle = '新增数字员工'
            this.currentWorkerId = ''
            this.staffName = ''
            this.isViewMode = false
            this.dialogVisible = true
        },
        // 编辑
        async edit(item) {
            let res = await editLock({ mtPlatStaffId: item.mtPlatStaffId })
            if (res && res.head) {
                if (res.head.respCode === 0) {
                    this.dialogTitle = item.staffName
                    this.currentWorkerId = item.mtPlatStaffId
                    this.staffName = item.staffName
                    this.isViewMode = false
                    this.dialogVisible = true
                } else {
                    this.$message.warning('该数字员工已被锁定，请稍后再试')
                }
            }
        },
        // 批量删除
        batchDelete() {
            const selectedItems = this.selectedItems

            if (selectedItems.length === 0) {
                this.$message.warning('请选择要删除的数字员工')
                return
            }

            // // 检查是否有已发布或启用状态的数字员工
            // const cannotDeleteItems = selectedItems.filter((item) => item.startupStatus === '启用')

            // if (cannotDeleteItems.length > 0) {
            //     const names = cannotDeleteItems.map((item) => item.staffName).join('、')
            //     this.$message.error(`以下数字员工处于启用状态，请先禁用后再删除：${names}`)
            //     return
            // }

            // 弹出确认删除对话框
            this.$confirm('请确认是否删除选中的数据？', '提示', {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    // 执行删除操作
                    this.doDeleteItems(selectedItems)
                })
                .catch(() => {
                    // 取消删除操作
                })
        },
        // 删除单个数字员工
        del(item) {
            // 检查是否启用状态
            if (item.startupStatus == '1') {
                this.$message.error(`${item.staffName}处于启用状态，请先禁用后再删除`)
                return
            }

            // 弹出确认删除对话框
            this.$confirm('请确认是否删除选中的数据？', '提示', {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    // 执行删除操作
                    this.doDeleteItems([item])
                })
                .catch(() => {
                    // 取消删除操作
                })
        },
        // 执行删除操作
        doDeleteItems(items) {
            const ids = items.map((item) => item.mtPlatStaffId)

            // 使用新的接口格式
            digitalWorkerDelete({ mtPlatStaffIds: ids })
                .then((res) => {
                    if (res && res.success) {
                        this.$message.success('删除成功')
                        this.queryList()
                    } else {
                        this.$message.error(res.head.respMsg || '删除失败')
                    }
                })
                .catch((error) => {
                    console.error('删除失败', error)
                    this.$message.error('删除失败，请稍后重试')
                })
        },
        // 处理图标URL，添加下载前缀
        processIconUrl(url) {
            if (!url) return ''

            // 如果URL已经是完整的URL（包含http或https），则直接返回
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url
            }

            // 否则拼接downloadFileUrl返回的地址前缀
            return downloadFileUrl() + url
        },
        // 岗位类型值
        getPostTypeText(value) {
            const option = this.postTypeOptions.find((item) => item.codeValue == value)
            return option ? option.codeName : ''
        },
        // 启用/禁用
        getStartupStatusText(value) {
            const option = this.startupStatusOptions.find((item) => item.codeValue == value)
            return option ? option.codeName : ''
        },
        enable(item) {
            const statusText = item.startupStatus == '1' ? '禁用' : '启用'

            this.$confirm(`确认${statusText}该数字员工吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    digitalWorkerStatusSwitch({ mtPlatStaffId: item.mtPlatStaffId })
                        .then((res) => {
                            if (res && res.success) {
                                this.$message.success(`${statusText}成功`)
                                item.startupStatus = item.startupStatus == '1' ? '0' : '1'
                                // 同步更新开关状态
                                item.switchValue = item.startupStatus == '1'
                            } else {
                                this.$message.error(res.head.respMsg || '未知错误')
                            }
                        })
                        .catch((error) => {
                            console.error(`${statusText}失败`, error)
                            this.$message.error(`${statusText}失败，请稍后重试`)
                        })
                })
                .catch(() => {})
        },
        // 处理开关变化
        handleSwitchChange(item) {
            // 保存原始状态（开关变化前的状态）
            const originalSwitchValue = !item.switchValue
            const originalStartupStatus = item.startupStatus
            const statusText = item.switchValue ? '启用' : '禁用'

            this.$confirm(`确认${statusText}该数字员工吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    // 用户确认，执行API调用
                    digitalWorkerStatusSwitch({ mtPlatStaffId: item.mtPlatStaffId })
                        .then((res) => {
                            if (res && res.success) {
                                this.$message.success(`${statusText}成功`)
                                // 更新后端状态
                                item.startupStatus = item.switchValue ? '1' : '0'
                            } else {
                                this.$message.error(res.head.respMsg || '未知错误')
                                // API失败，恢复到原始状态
                                item.switchValue = originalSwitchValue
                                item.startupStatus = originalStartupStatus
                            }
                        })
                        .catch((error) => {
                            console.error(`${statusText}失败`, error)
                            this.$message.error(`${statusText}失败，请稍后重试`)
                            // API异常，恢复到原始状态
                            item.switchValue = originalSwitchValue
                            item.startupStatus = originalStartupStatus
                        })
                })
                .catch(() => {
                    // 用户取消，恢复到原始状态
                    item.switchValue = originalSwitchValue
                    item.startupStatus = originalStartupStatus
                    this.$message.info(`已取消${statusText}操作`)
                })
        },
        // 查看
        view(item) {
            this.dialogTitle = item.staffName
            this.currentWorkerId = item.mtPlatStaffId
            this.staffName = item.staffName
            this.isViewMode = true
            this.dialogVisible = true
        },
        // 点击重置，查询列表
        resetResult(obj) {
            this.$refs.layout.keyWord = null
            this.searchParam = {
                keyWord: '',
                staffName: '',
                staffNo: '',
                displayName: '',
                label: '',
                pageNo: 1,
                pageSize: 20,
            }
            this.queryList()
        },
        // 查询列表
        searchResult(obj) {
            let tmpObj = Object.assign(JSON.parse(JSON.stringify(obj)))
            this.searchParam.staffName = tmpObj.staffName || ''
            this.searchParam.staffNo = tmpObj.staffNo || ''
            this.searchParam.displayName = tmpObj.displayName || ''
            this.searchParam.label = tmpObj.label || ''

            this.queryList(1)
        },
        // 调用接口查询数据列表
        queryList(val) {
            if (val) {
                this.$refs.layout.currentPage = 1 // 分页重置为第一页
                this.searchParam.pageNo = 1
            }
            this.loading = true

            getDigitalWorkerList(this.searchParam)
                .then((res) => {
                    this.loading = false
                    if (res && res.success) {
                        const data = res.body
                        this.tableData = data.list.map((item) => {
                            return {
                                ...item,
                                checked: false, // 添加选中状态属性
                                switchValue: item.startupStatus == '1', // 添加开关状态属性
                            }
                        })
                        this.totalCount = data.total
                    } else {
                        this.$message.error(res.head.respMsg || '未知错误')
                        this.tableData = []
                        this.totalCount = 0
                    }
                })
                .catch((error) => {
                    this.loading = false
                    console.error('查询失败', error)
                    this.$message.error('查询失败，请稍后重试')
                    this.tableData = []
                    this.totalCount = 0
                })
        },
        // pagesize触发查询
        handleSizeChange(val) {
            this.searchParam.pageSize = val
            this.queryList()
        },
        // 分页查询
        handleCurrentChange(val) {
            this.searchParam.pageNo = val
            this.queryList()
        },
        // 关键字查询
        keyWordSearch(val) {
            this.searchParam.keyWord = val
            this.queryList(1)
        },
        // 设置表格高度
        setTableHeight(height) {
            this.tableHeight = height - 20
        },
        // 处理详情页面关闭事件
        handleDetailCancel(needRefresh = false) {
            this.dialogVisible = false
            if (needRefresh) {
                this.queryList()
            }
        },
        // 表格选择变化
        handleSelectionChange(selection) {
            // 更新选中状态
            this.tableData.forEach((item) => {
                item.checked = selection.some((selected) => selected.mtPlatStaffId === item.mtPlatStaffId)
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.digital-worker-manage {
    margin: 10px;
    .card-head {
        height: 60px;
        line-height: 60px;
        padding: 0 6px;
        width: 100%;
        display: flex;
        align-items: center;
        position: relative;
        .icon {
            width: 46px;
            height: 46px;
            margin-right: 10px;
        }
        .produce-name {
            width: calc(100% - 70px);
            font-family: Microsoft YaHei UI, Microsoft YaHei UI;
            font-weight: bold;
            font-size: 20px;
            color: #2D3040;
            line-height: 22px;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            &:hover {
                color: #47e;
            }
        }
        .checkbox {
            position: absolute;
            right: 4px;
            top: -2px;
        }
    }
    .card-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    // 开关组件样式优化
    ::v-deep .el-switch {
        .el-switch__core {
            width: 40px !important;
            height: 20px;
            border-radius: 10px;
            transition: all 0.3s ease;

            &:after {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                top: 2px;
                left: 2px;
                transition: all 0.3s ease;
            }
        }

        &.is-checked .el-switch__core:after {
            left: 22px;
        }

        // 悬停效果
        &:hover .el-switch__core {
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
            transform: scale(1.05);
        }

        // 表格中的开关样式
        .el-table & {
            margin: 0 auto;
            display: block;
        }
    }
}
@media screen and (min-width: 1921px) {
    .el-button {
        user-select: unset;
        font-size: 18px;
    }
    .el-table {
        font-size: 18px;
    }
}
::v-deep .oss-form-generate .el-form-item__label {
    margin-top: 10px !important;
}
.text-info{
    font-family: Microsoft YaHei UI, Microsoft YaHei UI;
    font-weight: 400;
    font-size: 14px;
    color: #575966;
    line-height: 24px;
}
.el-tag--primary{
    background: #E6EFFA;
    border-radius: 2px 2px 2px 2px;
    font-family: Microsoft YaHei UI, Microsoft YaHei UI;
    font-weight: 400;
    font-size: 14px;
    color: #0D6CE4;

}
.btnArea{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    img{
        cursor: pointer;
    }
}
.stats {
  padding: 10px 15px;
  border-bottom:#E7E7E7 1px solid;
  position: absolute;
  width: 98%;
}

.stats-text {
    font-family: Microsoft YaHei UI, Microsoft YaHei UI;
    font-weight: 400;
    font-size: 16px;
    color: #575966;
    a{
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: bold;
        font-size: 16px;
        color: #4477EE;
        padding:0 5px;
    }
}
/deep/ .el-main{
    margin-top:50px;
    padding:0 10px;
}
</style>
<style lang="scss">
.wk-details-dialog {
    z-index: 2001 !important;
    .el-dialog__header {
        display: none !important;
    }
    .el-dialog__body {
        padding: 0px !important;
    }
}
</style>
