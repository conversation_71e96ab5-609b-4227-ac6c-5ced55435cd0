<template>
    <div class="knowledge-skills-component" v-loading="loading" element-loading-spinner="el-icon-loading" element-loading-text="加载中...">
        <div class="card-container">
            <div class="card-wrapper" v-for="(card, index) in cardList" :key="index">
                <div class="card">
                    <div class="card-icon">
                        <img :src="getImageSrc(card.icon)" alt="Icon" v-if="card.icon" />
                        <i class="el-icon-document" v-else></i>
                    </div>
                    <div class="card-content">
                        <h3 class="title" @click="openSkillDetail(card)">{{ card.title }}</h3>
                        <p  v-if="card.description">{{ card.description }}</p>
                    </div>
                </div>
            </div>
            <div class="empty-content" v-if="cardList.length === 0">暂无数据</div>
        </div>

        <!-- 员工技能详情覆盖层 -->
        <div class="skill-overlay" v-if="skillDetailVisible">
            <WorkerSkills v-if="currentSkill.modelType == '0'" :currentSkill="currentSkill" :mtPlatStaffId="mtPlatStaffId" @close="handleClose" />
            <div class="os-iframe" v-else>
                <IframeContainer
                    :title="currentSkill.title"
                    :description="currentSkill.description"
                    :target-route="getTargetRoute(currentSkill)"
                    @back="handleClose"
                    @micro-app-route-change="handleRouteChange"
                    @iframe-message="handleAppMessage"
                    :mtPlatStaffId="mtPlatStaffId"
                />
            </div>
        </div>
    </div>
</template>

<script>
import WorkerSkills from './workerSkills.vue'
import IframeContainer from './components/IframeContainer.vue'
import { getKnowledgeAndSkillsConfigList } from '@/api/digitalWorkerApi'

export default {
    name: 'KnowledgeAndSkills',
    components: {
        WorkerSkills,
        IframeContainer
    },
    props: {
        mtPlatStaffId: ''
    },
    data() {
        return {
            cardList: [],
            // 弹窗相关数据
            skillDetailVisible: false,
            currentSkill: {
                icon: '',
                title: '',
                description: '',
                url: '',
            },
            loading: false,
        }
    },
    mounted() {
        this.fetchCardList()
    },
    methods: {
        // 根据modelName获取默认图片
        getDefaultIcon(modelName) {
            const iconMap = {
                '员工技能': './images/employee-skills.png',
                '知识库': './images/knowledge-base.png',
                '知识图谱': './images/knowledge-graph.png',
                '思维树&思维链': './images/thinking-tree.png',
                '任务': './images/AI-models.png'
            }
            return iconMap[modelName] || './images/employee-skills.png' // 默认使用员工技能图片
        },
        // 安全地获取图片路径
        getImageSrc(iconPath) {
            if (!iconPath || iconPath.trim() === '') {
                return ''
            }
            // 如果是本地图片路径，使用require
            if (iconPath.startsWith('./images/')) {
                return require(`${iconPath}`) // 移除 './' 前缀，使用 '@/' 别名
            }
            // 如果是网络图片或其他路径，直接返回
            return iconPath
        },
        // 获取卡片列表数据
        async fetchCardList() {
            this.loading = true
            try {
                let params = {
                    parentModel: 1
                }
                const response = await getKnowledgeAndSkillsConfigList(params)
                if (response.success) {
                    this.cardList = response.body.map((item) => ({
                        icon: item.modelUrl || this.getDefaultIcon(item.modelName),
                        title: item.modelName,
                        description: item.modelDesc,
                        modelType: item.modelType,
                        url: item.modelPath + '?mtPlatStaffId=' + this.mtPlatStaffId,
                    }))
                } else {
                    const errorMsg = (response && response.head && response.head.respMsg) || '获取数据失败'
                    this.$message.error(errorMsg)
                }
                this.loading = false
            } catch (error) {
                this.loading = false
                console.error('接口调用失败:', error)
                this.$message.error('接口调用失败，请稍后重试')
            }
        },
        // 打开技能详情
        openSkillDetail(card) {
            console.log('打开技能详情:', card)
            console.log('目标路由:', this.getTargetRoute(card))

            this.currentSkill = { ...card }
            this.skillDetailVisible = true
        },
        // 关闭技能详情
        handleClose() {
            console.log('关闭技能详情，准备清理状态')

            // 先隐藏覆盖层
            this.skillDetailVisible = false

            // 清理当前技能状态，确保下次进入是全新状态
            setTimeout(() => {
                this.currentSkill = {
                    icon: '',
                    title: '',
                    description: '',
                    url: ''
                }
                console.log('技能状态已清理')
            }, 200)
        },

        // 根据技能获取目标路由
        getTargetRoute(skill) {
            const url = skill.url
            console.log('skill.url======='+url+  url.includes('thematicMapManageNew'))
            if (url.includes('knowledgeBaseSearch')) {
                return '/knowledgeBaseSearch'
            } else if (url.includes('platformTaskManage')) {
                return '/platformTaskManage'
            } else if (url.includes('ThematicMapManageNew')) {
                return '/ThematicMapManageNew'
            } else if (url.includes('ftaManage')) {
                return '/ftaManage' // 默认知识相关的跳转到搜索页面
            } else {
                return '/knowledgeBaseSearch' // 默认路由
            }
        },

        // 处理微前端路由变化
        handleRouteChange(data) {
            console.log('微前端路由变化:', data)
        },

        // 处理来自应用的消息
        handleAppMessage(data) {
            console.log('收到应用消息:', data)
        }
    }
}
</script>

<style lang="less" scoped>
.knowledge-skills-component {
    position: relative;

    .card-container {
        display: flex;
        flex-wrap: wrap;
        margin: -10px; // 抵消卡片wrapper的padding
        padding: 5px;
        box-sizing: border-box;
    }

    .card-wrapper {
        width: 33.33%;
        padding: 5px;
        box-sizing: border-box;
    }

    .card {
        height: 220px;
        background-color: #fff;
        border-radius: 4px;
        padding: 20px;
        display: flex;
        border: 1px solid #ebeef5;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        box-sizing: border-box;

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;

            i {
                font-size: 24px;
                color: #909399;
            }
        }

        .card-content {
            flex: 1;
            overflow: hidden;

            .title {
                margin: 0 0 10px 0;
                font-family: Microsoft YaHei UI, Microsoft YaHei UI;
                font-weight: bold;
                font-size: 16px;
                color: #2D3040;
                line-height: 22px;
                &:hover {
                    color: #47e;
                    cursor: pointer;
                }
            }

            p {
                background: #F4F5F7;
                border-radius: 8px 8px 8px 8px;
                padding:20px;
                font-family: Microsoft YaHei UI, Microsoft YaHei UI;
                font-weight: 400;
                font-size: 14px;
                color: #727C8D;
                line-height: 20px;
            }
        }
    }

    // 技能详情覆盖层样式
    .skill-overlay {
        position: fixed;
        top: 64px;
        left: 10px;
        right: 10px;
        bottom: 0;
        z-index: 2000;
        // background-color: #f5f7fa;
        overflow-y: hidden;

        // 确保 workerSkills 组件在覆盖层中正确显示
        /deep/ .function-page {
            min-height: calc(100vh - 64px);
            width: 100%;
            overflow: hidden;
        }

        .os-iframe {
            height: calc(100vh - 60px);
            width: 100%;
        }
    }
}
</style>
