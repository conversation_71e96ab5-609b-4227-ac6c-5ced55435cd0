<template>
    <div class="worker-skills">
        <!-- 使用通用头部组件 -->
        <detail-header :title="currentSkill.title" :description="currentSkill.description" @close="handleClose" @confirm="handleConfirm" @back="handleBack" />
        <div class="category-container" :class="{ 'blur-background': showIframe }" v-loading="loading" element-loading-spinner="el-icon-loading" element-loading-text="加载中...">
            <div class="category-scroll-container" :class="{ 'has-left-arrow': showLeftArrow, 'has-right-arrow': showRightArrow }">
                <div class="scroll-arrow left-arrow" v-if="showLeftArrow" @click="scrollLeft">
                    <i class="el-icon-arrow-left"></i>
                </div>
                <div class="category-row" ref="categoryRow">
                    <div class="category-item" v-for="(category, index) in topCategories" :key="'top-' + index" @click="openAddCardDialog(category.codeValue)" :style="{ borderColor: getCategoryColor(category.codeName) }">
                        <div class="category-icon">
                            <img :src="getImageSrc(getCategoryIcon(category.codeName))" :alt="category.codeName" class="category-icon-img" />
                        </div>
                        <div class="category-title" :style="{ color: getCategoryColor(category.codeName) }">{{ category.codeName }}</div>
                    </div>
                </div>
                <div class="scroll-arrow right-arrow" v-if="showRightArrow" @click="scrollRight">
                    <i class="el-icon-arrow-right"></i>
                </div>
            </div>

            <div class="function-list-wrapper">
                <div class="function-list">
                    <function-card
                        v-for="(item, index) in functionItems"
                        :key="index"
                        :item="{
                            name: item.competenceName,
                            description: item.competenceDesc,
                            tags: item.label ? item.label.split(';') : [],
                            icon: processIconUrl(item.competenceUrl),
                        }"
                        :defaultIcon="getDefaultIconForItem(item)"
                        @command="handleCommand($event, index)"
                        @titleClick="handleTitleClick(index)"
                    >
                        <template #subtitle>
                            <div class="skill-category">{{ getCompetenceTypeName(item.competenceType) }}</div>
                        </template>
                    </function-card>
                </div>
                <div v-if="functionItems.length === 0" class="no-data-tip">暂无数据</div>
            </div>
        </div>

        <!-- iframe覆盖层 -->
        <div class="iframe-overlay" v-if="showIframe">
            <IframeContainer :title="iframeTitle" :description="currentItem.competenceDesc" :iframeSrc="currentItem.appUrl" :enableMicroFrontend="false" @back="closeIframe" />
        </div>

        <!-- 添加卡片对话框 -->
        <el-dialog
            class="add-app-dialog"
            :title="dialogTitle"
            :visible.sync="addCardDialogVisible"
            :width="newCardForm.competenceType == '4' || newCardForm.competenceType == '5' ? '800px' : '500px'"
            :append-to-body="true"
            @close="resetForm"
            :close-on-click-modal="false"
        >
            <el-form :model="newCardForm" :rules="cardFormRules" ref="cardForm" :label-width="newCardForm.competenceType == '4' || newCardForm.competenceType == '5' ? '75px' : '60px'">
                <el-form-item label="标题" prop="competenceName">
                    <el-input v-model="newCardForm.competenceName" placeholder="请输入标题"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input type="textarea" v-model="newCardForm.competenceDesc" :rows="3" placeholder="请输入描述"></el-input>
                </el-form-item>
                <el-form-item label="标签">
                    <div class="tag-input-container">
                        <div class="tag-list-wrapper">
                            <template v-if="newCardForm.label">
                                <el-tag
                                    :key="tag"
                                    v-for="tag in newCardForm.label.split(';')"
                                    closable
                                    :disable-transitions="false"
                                    @close="handleTagClose(tag)"
                                    class="tag-item"
                                    size="medium"
                                    effect="light"
                                >
                                    {{ tag }}
                                </el-tag>
                            </template>
                            <div class="tag-input-wrapper">
                                <el-input
                                    class="input-new-tag"
                                    v-if="inputTagVisible"
                                    v-model="inputTagValue"
                                    ref="saveTagInput"
                                    size="small"
                                    @keyup.enter.native="handleInputTagConfirm"
                                    @blur="handleInputTagConfirm"
                                    placeholder="请输入标签"
                                >
                                </el-input>
                                <el-button v-else class="button-new-tag" size="small" type="primary" plain icon="el-icon-plus" @click="showTagInput">添加标签</el-button>
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="图标">
                    <div class="icon-upload-container">
                        <div class="selected-icon" v-if="newCardForm.competenceUrl">
                            <img :src="processIconUrl(newCardForm.competenceUrl)" class="uploaded-image" alt="已上传图标" />
                            <i class="el-icon-delete remove-icon" @click="removeIcon"></i>
                        </div>
                        <el-upload v-else class="icon-uploader" action="#" :show-file-list="false" :auto-upload="false" :on-change="handleIconChange" accept="image/*">
                            <i class="el-icon-plus icon-uploader-icon" v-if="!newCardForm.competenceUrl"></i>
                            <!-- <div class="el-upload__text">点击上传图标</div> -->
                        </el-upload>
                    </div>
                </el-form-item>

                <!-- 新增表格展示 -->
                <el-form-item :label="newCardForm.competenceType == '4' ? '服务任务' : '目标流程'" v-if="newCardForm.competenceType == '4' || newCardForm.competenceType == '5'">
                    <el-table v-loading="tableLoading" ref="table" class="table-container" :data="tableData" border style="width: 100%" highlight-current-row @row-click="handleRowClick">
                        <template v-if="newCardForm.competenceType == '4'">
                            <el-table-column prop="servName" label="服务能力名称"></el-table-column>
                            <el-table-column prop="serviceLabelArr" label="能力标签">
                                <template slot-scope="scope">
                                    <el-tag v-for="(item, index) in scope.row.serviceLabelArr" :key="index" size="medium" :type="item.type" style="margin: 0 4px">
                                        {{ item.value }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                        </template>
                        <template v-if="newCardForm.competenceType == '5'">
                            <el-table-column prop="flowName" label="场景名称"></el-table-column>
                            <el-table-column prop="sceneType" label="场景类型" width="150" align="center">
                                <template slot-scope="scope"> {{ getSceneTypeName(scope.row.sceneType) }} </template>
                            </el-table-column>
                            <el-table-column prop="flowVersion" label="版本" width="150" align="center"></el-table-column>
                        </template>
                    </el-table>
                    <el-pagination
                        class="pagination-container"
                        background
                        @current-change="handlePageChange"
                        :current-page="currentPage"
                        :page-size="pageSize"
                        layout="prev, pager, next"
                        :total="total"
                    >
                    </el-pagination>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addCardDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveNewCard" :loading="saveLoading">保存</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import DetailHeader from './components/DetailHeader.vue'
import FunctionCard from './components/FunctionCard.vue'
import IframeContainer from './components/IframeContainer2.vue'
import { difyLogin, getAllDictionary, uploadFile, downloadFileUrl, getCompetenceList, saveCompetence, deleteCompetence, updateCompetence, queryUsedInfos, queryInfos } from '@/api/digitalWorkerApi'

export default {
    name: 'WorkerSkills',
    components: {
        DetailHeader,
        FunctionCard,
        IframeContainer,
    },
    props: {
        mtPlatStaffId: '',
        currentSkill: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            topCategories: [],
            functionItems: [],
            // iframe相关数据
            showIframe: false,
            iframeTitle: '',
            currentItem: null,

            // 添加卡片对话框相关数据
            addCardDialogVisible: false,
            newCardForm: {
                competenceType: '',
                competenceName: '',
                competenceDesc: '',
                label: '',
                competenceUrl: '',
            },
            cardFormRules: {
                competenceName: [{ required: true, message: '请输入标题', trigger: 'blur' }],
            },
            // 标签输入相关
            inputTagVisible: false,
            inputTagValue: '',
            // 添加编辑索引字段
            editIndex: -1, // -1表示新增，>=0表示编辑
            showLeftArrow: false,
            showRightArrow: false,
            // 表格相关数据
            tableData: [],
            currentPage: 1,
            pageSize: 5,
            total: 0,
            selectedRow: null, // 用于存储当前选中的行数据
            loading: false,
            tableLoading: false,
            saveLoading: false,
        }
    },
    computed: {
        dialogTitle() {
            if (this.editIndex >= 0) {
                return '编辑 - ' + this.getCompetenceTypeName(this.newCardForm.competenceType)
            } else {
                return '新增 - ' + this.getCompetenceTypeName(this.newCardForm.competenceType)
            }
        },
    },
    mounted() {
        this.loading = true
        this.sceneTypeList = []
        getAllDictionary(['competence_type', 'scene_type']).then((res) => {
            if (res && res.success) {
                const data = res.body
                this.topCategories = data['competence_type'] || []
                this.sceneTypeList = data['scene_type'] || []
            } else {
                this.$message.error(res.head.respMsg || '未知错误')
            }
        })

        // 调用 getCompetenceList 接口获取员工技能列表
        getCompetenceList({
            mtPlatStaffId: this.mtPlatStaffId,
            pageNo: 0,
            pageSize: 0,
        }).then((res) => {
            if (res && res.success) {
                const data = res.body.list
                this.functionItems = data.map((item) => ({
                    ...item,
                    competenceTypeName: this.getCompetenceTypeName(item.competenceType), // 新增：获取categoryName
                }))
            } else {
                this.$message.error(res.head.respMsg || '获取技能列表失败')
            }
            this.loading = false
        })

        this.$nextTick(() => {
            this.checkScrollable()
            window.addEventListener('resize', this.checkScrollable)

            // 添加滚动事件监听
            if (this.$refs.categoryRow) {
                this.$refs.categoryRow.addEventListener('scroll', this.checkScrollable)
            }
        })
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.checkScrollable)

        // 移除滚动事件监听
        if (this.$refs.categoryRow) {
            this.$refs.categoryRow.removeEventListener('scroll', this.checkScrollable)
        }
    },
    methods: {
        // 根据分类名称获取对应图标（用于分类标签，不带-icon后缀）
        getCategoryIcon(codeName) {
            const iconMap = {
                '简报类': './images/briefing-category.png',
                '问答类': './images/Q&A.png',
                '工作流': './images/workflow.png',
                'Agent': './images/agent.png',
                '定制推理能力': './images/customized.png',
                '智行工作流': './images/intelligent-workflow.png'
            }
            return iconMap[codeName] || './images/briefing-category.png' // 默认使用简报类图标
        },
        // 根据分类名称获取功能卡片默认图标（带-icon后缀）
        getFunctionCardIcon(codeName) {
            const iconMap = {
                '简报类': './images/briefing-category-icon.png',
                '问答类': './images/Q&A-icon.png',
                '工作流': './images/workflow-icon.png',
                'Agent': './images/agent-icon.png',
                '定制推理能力': './images/customized-icon.png',
                '智行工作流': './images/intelligent-workflow-icon.png'
            }
            return iconMap[codeName] // 默认使用简报类图标
        },
        // 根据分类名称获取对应颜色
        getCategoryColor(codeName) {
            const colorMap = {
                '简报类': '#0E3DAA',
                '问答类': '#8349EF',
                '工作流': '#2DB2EF',
                'Agent': '#EE5960',
                '定制推理能力': '#EE57E0',
                '智行工作流': '#05870D'
            }
            return colorMap[codeName] || '#0E3DAA' // 默认使用简报类颜色
        },
        // 获取功能卡片的默认图标
        getDefaultIconForItem(item) {
            const typeName = this.getCompetenceTypeName(item.competenceType)

            // 特殊处理工作流类型
            if (typeName === '工作流') {
                return this.getImageSrc('./images/workflow-icon.png')
            }

            // 根据类型名称获取对应的功能卡片图标（带-icon后缀）
            return this.getImageSrc(this.getFunctionCardIcon(typeName))
        },
        // 安全地获取图片路径
        getImageSrc(iconPath) {
            if (!iconPath || iconPath.trim() === '') {
                return ''
            }
            // 如果是本地图片路径，使用require
            if (iconPath.startsWith('./images/')) {
                try {
                    return require(`${iconPath}`) // 移除 './' 前缀，使用 '@/' 别名
                } catch (error) {
                    console.warn('图片加载失败:', iconPath, error)
                    return require('./images/briefing-category.png') // 默认图片
                }
            }
            // 如果是网络图片或其他路径，直接返回
            return iconPath
        },
        handleBack() {
            if (this.showIframe) {
                this.closeIframe()
                return
            }
            this.$emit('close')
        },
        handleClose() {
            this.$emit('close')
        },
        handleConfirm() {
            // 这里可以添加确认逻辑
            this.$emit('close')
        },
        handleCommand(command, index) {
            if (command === 'edit') {
                this.editItem(index)
            } else if (command === 'delete') {
                this.deleteItem(index)
            }
        },
        editItem(index) {
            // 设置编辑索引
            this.editIndex = index

            // 获取当前项目
            const currentItem = this.functionItems[index]

            // 填充表单数据
            this.newCardForm = {
                competenceType: currentItem.competenceType,
                competenceName: currentItem.competenceName,
                competenceDesc: currentItem.competenceDesc,
                label: currentItem.label || '',
                competenceUrl: currentItem.competenceUrl || '',
                mtPlatStaffCompetenceId: currentItem.mtPlatStaffCompetenceId,
            }

            // 打开对话框
            this.addCardDialogVisible = true
            if (currentItem.competenceType == '4') {
                this.selectedRow = { diServiceInfoId: currentItem.appId }
            } else if (currentItem.competenceType == '5') {
                this.selectedRow = { flowCode: currentItem.appId }
            } else {
                this.selectedRow = null
            }
            this.initTableData()
        },
        // 打开添加卡片对话框
        openAddCardDialog(category) {
            this.newCardForm.competenceType = category
            this.addCardDialogVisible = true
            this.initTableData()
        },

        initTableData() {
            if (this.newCardForm.competenceType == '4' || this.newCardForm.competenceType == '5') {
                this.currentPage = 1
                this.total = 0
                this.handlePageChange(this.currentPage) // 初始化加载表格数据
            }
        },

        // 保存新卡片
        saveNewCard() {
            this.$refs.cardForm.validate((valid) => {
                if (valid) {
                    if (this.newCardForm.competenceType == '4') {
                        if (!this.selectedRow) {
                            this.$message.error('请选择一个服务任务后再进行保存')
                            return
                        } else {
                            this.newCardForm.appId = this.selectedRow.diServiceInfoId // 将 diServiceInfoId 的值赋给 appId 字段
                        }
                    } else if (this.newCardForm.competenceType == '5') {
                        if (!this.selectedRow) {
                            this.$message.error('请选择一个目标流程后再进行保存')
                            return
                        } else {
                            this.newCardForm.appId = this.selectedRow.flowCode // 将 flowCode 的值赋给 appId 字段
                        }
                    }
                    this.saveLoading = true
                    // 直接从 newCardForm 对象中取值
                    const apiCall = this.newCardForm.mtPlatStaffCompetenceId ? updateCompetence : saveCompetence

                    const params = {
                        competenceDesc: this.newCardForm.competenceDesc || '',
                        competenceName: this.newCardForm.competenceName,
                        competenceType: this.newCardForm.competenceType,
                        competenceUrl: this.newCardForm.competenceUrl || '',
                        label: this.newCardForm.label,
                        mtPlatStaffId: this.mtPlatStaffId,
                        mtPlatStaffCompetenceId: this.newCardForm.mtPlatStaffCompetenceId || null,
                        appId: this.newCardForm.appId || '', // 传递 appId 参数
                    }

                    apiCall(params)
                        .then((res) => {
                            if (res && res.success) {
                                const savedData = res.body.platStaffCompetenceVm
                                savedData.competenceTypeName = this.getCompetenceTypeName(savedData.competenceType)

                                // 更新 functionItems
                                if (this.editIndex >= 0) {
                                    // 编辑模式：更新现有卡片
                                    this.functionItems[this.editIndex] = savedData

                                    this.$message({
                                        type: 'success',
                                        message: '编辑成功!',
                                    })
                                } else {
                                    // 新增模式：添加新卡片
                                    this.functionItems.push(savedData)

                                    this.$message({
                                        type: 'success',
                                        message: '添加成功!',
                                    })
                                }

                                // 关闭对话框
                                this.addCardDialogVisible = false

                                // 重置表单
                                this.resetForm()
                            } else {
                                this.$message.error(res.head.respMsg || '保存失败')
                            }
                            this.saveLoading = false
                        })
                        .catch((error) => {
                            this.saveLoading = false
                            console.error('保存技能失败:', error)
                            this.$message.error('保存技能失败，请稍后重试')
                        })
                } else {
                    return false
                }
            })
        },
        deleteItem(index) {
            // 删除功能实现
            this.$confirm('确认删除该技能?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    console.log('currentItem:', 111)
                    // 获取当前项目
                    const currentItem = this.functionItems[index]

                    console.log('currentItem:', currentItem)

                    // 调用删除接口
                    deleteCompetence({
                        mtPlatStaffCompetenceIds: [currentItem.mtPlatStaffCompetenceId],
                    })
                        .then((res) => {
                            if (res && res.success) {
                                // 删除成功，从列表中移除
                                this.functionItems.splice(index, 1)
                                this.$message({
                                    type: 'success',
                                    message: '删除成功!',
                                })
                            } else {
                                this.$message.error(res.head.respMsg || '删除失败')
                            }
                        })
                        .catch((error) => {
                            console.error('删除技能失败:', error)
                            this.$message.error('删除技能失败，请稍后重试')
                        })
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除',
                    })
                })
        },

        // 打开iframe
        openIframe(item) {
            console.log('item:', item)
            if (item && item.appUrl) {
                if (item.competenceType == '4' || item.competenceType == '5') {
                    window.open(item.appUrl + '&beforeRoutePath=robot')
                    // 有路由的方式跳转
                    // this.$router.push({ path: item.appUrl })
                } else {
                    // 调用 difyLogin 接口进行登录验证
                    difyLogin()
                        .then((res) => {
                            if (res && res.success) {
                                let accessToken = res.body.accessToken
                                let refreshToken = res.body.refreshToken
                                localStorage.setItem('console_token', accessToken)
                                localStorage.setItem('refresh_token', refreshToken)
                                // 登录成功，继续打开 iframe
                                this.iframeTitle = '员工技能-' + item.competenceName
                                this.currentItem = item
                                this.showIframe = true
                            } else {
                                // 登录失败，提示用户
                                this.$message.error(res.head.respMsg || 'Dify登录失败，请稍后重试')
                            }
                        })
                        .catch((error) => {
                            console.error('Dify登录接口调用失败:', error)
                            this.$message.error('Dify登录失败，请稍后重试')
                        })
                }
            } else {
                this.$message.error('无法打开 iframe，缺少有效地址')
            }
        },
        // 关闭iframe
        closeIframe() {
            this.showIframe = false
            this.currentItem = null
        },
        // 重置表单时清空选中行
        resetForm() {
            this.newCardForm = {
                competenceType: '',
                competenceName: '',
                competenceDesc: '',
                label: '',
                competenceUrl: '',
            }
            this.inputTagVisible = false
            this.inputTagValue = ''
            this.editIndex = -1 // 重置编辑索引
            this.selectedRow = null // 清空选中行
            if (this.$refs.cardForm) {
                this.$refs.cardForm.resetFields()
            }
        },

        // 处理图标URL，如果有url则拼接下载地址前缀
        processIconUrl(url) {
            if (!url) return ''

            // 如果URL已经是完整的URL（包含http或https），则直接返回
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url
            }

            // 否则拼接downloadFileUrl返回的地址前缀
            return downloadFileUrl() + url
        },
        // 图标相关方法
        handleIconChange(file) {
            if (!file) return

            // 构建FormData对象
            const formData = new FormData()
            formData.append('file', file.raw) // 使用file.raw获取文件流对象
            formData.append('path', 'file/staff')

            // 调用上传接口
            uploadFile(formData)
                .then((res) => {
                    if (res && res.success) {
                        // 上传成功，获取返回的文件地址
                        const fileUrl = res.body && res.body.docUrl
                        this.newCardForm.competenceUrl = fileUrl
                        console.log('上传成功:', this.newCardForm)
                    } else {
                        this.$message.error((res.head && res.head.respMsg) || '图标上传失败')
                    }
                })
                .catch((error) => {
                    console.error('图标上传失败:', error)
                    this.$message.error('图标上传失败，请稍后重试')
                })
        },

        removeIcon() {
            this.newCardForm.competenceUrl = ''
            this.newCardForm.icon = ''
        },

        handleTitleClick(index) {
            this.openIframe(this.functionItems[index])
        },

        scrollLeft() {
            if (this.$refs.categoryRow) {
                const scrollAmount = 150 // 固定滚动距离，大约是一个卡片的宽度
                this.$refs.categoryRow.scrollLeft -= scrollAmount
                this.$nextTick(() => {
                    this.checkScrollable()
                })
            }
        },

        scrollRight() {
            if (this.$refs.categoryRow) {
                const scrollAmount = 150 // 固定滚动距离，大约是一个卡片的宽度
                this.$refs.categoryRow.scrollLeft += scrollAmount
                this.$nextTick(() => {
                    this.checkScrollable()
                })
            }
        },

        checkScrollable() {
            if (this.$refs.categoryRow) {
                const { scrollWidth, clientWidth, scrollLeft } = this.$refs.categoryRow
                // 内容宽度大于容器宽度才显示箭头
                const isScrollable = scrollWidth > clientWidth + 10 // 增加容差值

                // 如果有滚动空间，根据滚动位置显示左右箭头
                this.showLeftArrow = isScrollable && scrollLeft > 2
                // 右侧容差值更大一点，确保滚动到最右侧时箭头消失
                this.showRightArrow = isScrollable && scrollLeft < scrollWidth - clientWidth - 2
            }
        },

        // 获取categoryName的方法
        getCompetenceTypeName(codeValue) {
            const category = this.topCategories.find((cat) => cat.codeValue === codeValue)
            return category ? category.codeName : ''
        },
        // 获取sceneTypeName的方法
        getSceneTypeName(codeValue) {
            const sceneType = this.sceneTypeList.find((type) => type.codeValue === codeValue)
            return sceneType ? sceneType.codeName : ''
        },
        // 添加标签输入方法
        showTagInput() {
            this.inputTagVisible = true
            this.$nextTick(() => {
                this.$refs.saveTagInput.$refs.input.focus()
            })
        },

        // 处理标签输入确认的方法
        handleInputTagConfirm() {
            let inputValue = this.inputTagValue
            if (inputValue) {
                inputValue = inputValue.trim()
                if (inputValue && !this.newCardForm.label.split(';').includes(inputValue)) {
                    this.newCardForm.label += (this.newCardForm.label ? ';' : '') + inputValue
                }
            }
            this.inputTagVisible = false
            this.inputTagValue = ''
        },
        handleTagClose(tag) {
            const index = this.newCardForm.label.split(';').indexOf(tag)
            if (index >= 0) {
                this.newCardForm.label = this.newCardForm.label
                    .split(';')
                    .filter((item) => item !== tag)
                    .join(';')
            }
        },

        // 处理表格行点击事件
        handleRowClick(row) {
            this.selectedRow = row // 更新选中的行数据
            if (this.$refs.table) {
                this.$refs.table.setCurrentRow(row)
            }
            console.log('selectedRow:', this.selectedRow)
        },

        // 处理分页变化
        handlePageChange(page) {
            this.currentPage = page
            const currentSelectedRow = this.selectedRow // 保存当前选中的行数据
            this.fetchTableData().then(() => {
                if (currentSelectedRow) {
                    // 恢复选中状态
                    let restoredRow = null
                    if (this.newCardForm.competenceType == '4') {
                        restoredRow = this.tableData.find((item) => item.diServiceInfoId == currentSelectedRow.diServiceInfoId)
                    } else {
                        restoredRow = this.tableData.find((item) => item.flowCode == currentSelectedRow.flowCode)
                    }
                    if (restoredRow) {
                        this.$nextTick(() => {
                            setTimeout(() => {
                                this.handleRowClick(restoredRow)
                            }, 100)
                        })
                    }
                }
            })
        },

        // 获取表格数据
        fetchTableData() {
            if (this.newCardForm.competenceType == '4') {
                this.tableLoading = true
                const params = {
                    belongSystem: '',
                    servName: '',
                    pageNo: this.currentPage,
                    pageSize: this.pageSize,
                    servStatus: '1',
                    servTypes: ['0', '1', '2'],
                    serviceLables: [],
                }
                return queryInfos(params)
                    .then((res) => {
                        if (res && res.success) {
                            this.tableData = res.body.list || []
                            for (let item of this.tableData) {
                                item.serviceLabelArr = this.handlerLabel(item.serviceLables)
                            }
                            this.total = res.body.total
                        } else {
                            this.$message.error(res.head.respMsg || '获取服务能力信息失败')
                        }
                        this.tableLoading = false
                    })
                    .catch((error) => {
                        this.tableLoading = false
                        console.error('获取服务能力信息失败:', error)
                        this.$message.error('获取服务能力信息失败，请稍后重试')
                    })
            } else if (this.newCardForm.competenceType == '5') {
                this.tableLoading = true
                const params = {
                    keyWord: '',
                    pageNo: this.currentPage,
                    pageSize: this.pageSize,
                }
                return queryUsedInfos(params)
                    .then((res) => {
                        if (res && res.success) {
                            this.tableData = res.body.list
                            this.total = res.body.total
                        } else {
                            this.$message.error(res.head.respMsg || '获取场景信息失败')
                        }
                        this.tableLoading = false
                    })
                    .catch((error) => {
                        this.tableLoading = false
                        console.error('获取场景信息失败:', error)
                        this.$message.error('获取场景信息失败，请稍后重试')
                    })
            }
        },
        handlerLabel(serviceLables) {
            if (serviceLables) {
                let serviceLabelArr = []
                let typeArr = serviceLables.split('.')
                for (let item of typeArr) {
                    let content = item.split(':')
                    if (content[0] === '省专业') {
                        let arr = content[1]
                        for (let code of arr.split(',')) {
                            serviceLabelArr.push({ labelType: 'provinceMajor', value: code, type: '' })
                        }
                    } else if (content[0] === '场景类型') {
                        let arr = content[1]
                        for (let code of arr.split(',')) {
                            serviceLabelArr.push({ labelType: 'sceneType', value: code, type: 'info' })
                        }
                    } else if (content[0] === '处置阶段') {
                        let arr = content[1]
                        for (let code of arr.split(',')) {
                            serviceLabelArr.push({ labelType: 'disposeStage', value: code, type: 'danger' })
                        }
                    } else if (content[0] === '数据类型') {
                        serviceLabelArr.push({ labelType: 'dataType', value: content[1], type: 'warning' })
                    } else if (content[0] === '危险等级') {
                        serviceLabelArr.push({ labelType: 'dangerLevel', value: content[1], type: '' })
                    } else {
                        let arr = content[1]
                        for (let code of arr.split(',')) {
                            serviceLabelArr.push({ labelType: 'networkType', value: code, type: 'success' })
                        }
                    }
                }
                return serviceLabelArr
            } else {
                return []
            }
        },
    },
}
</script>

<style lang="less" scoped>
.worker-skills {
    background-color: #f5f7fa;
    width: 100%;
    height: 100vh;
    padding: 0;
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.category-container {
    padding: 10px 0 0 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    transition: all 0.3s;
    margin: 0 auto;
    width: 100%;
    max-width: 100%;

    &.blur-background {
        filter: blur(2px);
        pointer-events: none;
    }
}

.category-scroll-container {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 10px;
    padding: 0;
    width: 100%;
}

.scroll-arrow {
    width: 26px;
    height: 26px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    position: absolute;
    z-index: 10;
    top: 50%;
    transform: translateY(-50%);

    &:hover {
        background-color: #f0f9ff;
        border-color: #a0cfff;
    }

    i {
        font-size: 14px;
        color: #409eff;
    }

    &.left-arrow {
        left: 10px;
    }

    &.right-arrow {
        right: 10px;
    }
}

.category-row {
    display: flex;
    gap: 15px;
    flex-shrink: 0;
    overflow-x: auto;
    scroll-behavior: smooth;
    width: 100%;
    padding: 0 10px; /* 与卡片列表padding一致 */

    /* 当有左右箭头时，确保内容不被遮挡 */
    .has-left-arrow & {
        padding-left: 40px; /* 给左箭头留出空间 */
    }

    .has-right-arrow & {
        padding-right: 40px; /* 给右箭头留出空间 */
    }

    scrollbar-width: none;
    &::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;
}

.category-item {
    flex: 1;
    min-width: 150px;
    height: 60px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    &:hover {
        background-color: #f0f9ff;
        border-color: #a0cfff;
    }

    .category-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;

        i {
            font-size: 16px;
            color: #909399;
        }

        .category-icon-img {
            width: 16px;
            height: 16px;
            object-fit: contain;
        }
    }

    .category-title {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: 13px;
        color: #4477EE;
        line-height: 13px;
    }
}

.function-list-wrapper {
    flex: 1;
    overflow: hidden;
    position: relative;
    width: 100%;
}

.function-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    overflow-y: auto;
    max-height: calc(100vh - 216px);
    padding: 0 10px 20px 10px;
    position: absolute;
    left: 0;
    right: 0;
}

// iframe覆盖层样式
.iframe-overlay {
    position: fixed;
    top: 64px;
    left: 10px;
    right: 10px;
    width: calc(100% - 20px);
    height: calc(100% - 64px);
    background-color: #fff;
    z-index: 100;
    display: flex;
    flex-direction: column;
}

.iframe-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    flex: 1;
    overflow: hidden;
    /deep/ .detail-header {
        width: 100%;
        position: absolute;
        top: 0;
        z-index: 10;
    }
    /deep/ .iframe-wrapper {
        width: 100%;
        height: calc(100% - 15px);
        position: absolute !important;
        top: 15px;
    }
    iframe {
        width: 100%;
        height: 100%;
        border: none;
        top: 10px;
    }
}

// 确保在小屏幕上也能正常显示
@media screen and (max-width: 1200px) {
    .function-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 768px) {
    .function-list {
        grid-template-columns: 1fr;
    }
}

// 标签输入样式
.tag-input-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 4px;

    .tag-list-wrapper {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 4px;
        min-height: 32px;
        padding: 5px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;

        &:empty {
            padding: 10px;
        }
    }

    .tag-item {
        margin: 3px;
    }

    .tag-input-wrapper {
        display: flex;
        align-items: center;
    }

    .input-new-tag {
        height: 28px;
        line-height: 28px;
        flex: 1;
        margin: 3px;
        /deep/ .el-input__inner {
            height: 28px !important;
        }
    }

    .button-new-tag {
        height: 28px;
        line-height: 28px;
        padding-top: 0;
        padding-bottom: 0;
        border-radius: 4px;
        margin: 3px 0px;
    }
}

.table-container {
    margin: 4px 0px;
    /deep/ tr {
        &:hover {
            cursor: pointer !important;
        }
    }
}
/deep/ .el-table th {
    padding: 0px !important;
    height: 40px !important;
}
/deep/ .el-table td {
    padding: 6px 0px !important;
    height: 40px !important;
}
/deep/ .el-table .cell {
    font-size: 14px !important;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    line-height: 0px !important;
}

// 图标上传样式
.icon-upload-container {
    // margin-bottom: 10px;

    .selected-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        overflow: hidden;

        .uploaded-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-icon {
            position: absolute;
            right: 5px;
            top: 5px;
            cursor: pointer;
            color: #f56c6c;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            padding: 3px;
            font-size: 16px;

            &:hover {
                color: #f78989;
                background-color: rgba(255, 255, 255, 0.9);
            }
        }
    }

    .icon-uploader {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        width: 100px;
        height: 100px;

        &:hover {
            border-color: #409eff;
        }

        .icon-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 100px;
            height: 100px;
            line-height: 100px;
            text-align: center;
        }

        // .el-upload__text {
        //     text-align: center;
        //     color: #606266;
        //     font-size: 12px;
        // }
    }
}

.upload-tip {
    line-height: 30px;
    font-size: 12px;
    color: #909399;
}

// 移除不需要的样式
.icon-list {
    display: none;
}

.skill-category {
    font-size: 12px;
    color: #909399;
    line-height: 1.2;
}

// 新增：暂无数据提示样式
.no-data-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 16px;
    color: #909399;
    opacity: 0.6;
}
</style>

<style lang="less">
/* 全局样式，不使用 scoped */
.custom-dropdown-menu {
    .el-dropdown-menu__item {
        font-size: 16px !important;
        padding: 6px 20px !important;
        text-align: center;
    }
}

.add-app-dialog {
    .el-dialog__header {
        padding: 10px 20px !important;
    }
    .el-dialog__headerbtn .el-dialog__close {
        margin-top: 5px !important;
    }
}
</style>
