<template>
    <div class="function-item">
        <div class="function-header">
            <div class="function-icon">
                <img v-if="!isIconClass && (item.icon || defaultIcon)" :src="item.icon || defaultIcon" alt="" />
                <i v-else :class="item.icon || 'el-icon-document'"></i>
            </div>
            <div class="function-title-container" :class="{ 'center-title': !$slots.subtitle }">
                <div class="function-title" @click="$emit('titleClick', item)">{{ item.name || item.title }}</div>
                <slot name="subtitle"></slot>
            </div>
        </div>
        <div class="function-description">{{ item.description }}</div>
        <div class="function-footer">
            <div class="tag-list">
                <el-tag size="small" type="primary" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{ tag }}</el-tag>
            </div>
            <div class="more-actions">
                <el-dropdown trigger="click" @command="handleCommand" placement="right">
                    <span class="el-dropdown-link">
                        <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown" class="custom-dropdown-menu">
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="delete" class="delete-item">删除</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'FunctionCard',
    props: {
        item: {
            type: Object,
            required: true,
        },
        defaultIcon: {
            type: String,
            default: '',
        },
    },
    computed: {
        isIconClass() {
            return this.item.icon && typeof this.item.icon === 'string' && this.item.icon.startsWith('el-icon-')
        },
    },
    methods: {
        handleCommand(command) {
            this.$emit('command', command, this.item)
        },
    },
}
</script>

<style lang="less" scoped>
.function-item {
    height: 225px;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .function-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .function-icon {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            flex-shrink: 0;

            img {
                max-width: 30px;
                max-height: 30px;
            }

            i {
                font-size: 20px;
                color: #909399;
            }
        }

        .function-title-container {
            flex: 1;

            &.center-title {
                height: 40px;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .function-title {
                    margin-bottom: 0;
                }
            }

            .function-title {
                font-size: 16px;
                font-weight: 500;
                color: #303133;
                cursor: pointer;
                margin-bottom: 4px;

                &:hover {
                    color: #409eff;
                }
            }

            .kb-meta {
                font-size: 12px;
                color: #909399;
                line-height: 1.2;
            }
        }
    }

    .function-description {
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
        margin-bottom: 15px;
        height: 105px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
    }

    .function-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tag-list {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            flex: 1;
        }

        .more-actions {
            flex-shrink: 0;
            margin-left: 10px;

            .el-dropdown-link {
                cursor: pointer;
                color: #909399;

                i {
                    font-size: 18px;
                }
            }
        }
    }
}
.delete-item {
  color: #ff4d4f;
}

.delete-item:hover {
    color: #ff4d4f;
    background-color: #fff2f0;
}
</style>
