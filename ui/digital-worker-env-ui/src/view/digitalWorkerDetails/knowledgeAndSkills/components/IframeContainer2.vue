<template>
    <div class="iframe-container">
        <DetailHeader :title="title" :description="description" @back="handleBack" @confirm="handleConfirm" @close="handleClose" />
        <div class="iframe-wrapper" v-loading="!iframeLoaded" element-loading-spinner="el-icon-loading" element-loading-text="加载中...">
            <iframe ref="contentIframe" :src="iframeSrc" frameborder="0" class="content-iframe" @load="handleIframeLoad"></iframe>
        </div>
    </div>
</template>

<script>
import DetailHeader from './DetailHeader.vue'

export default {
    name: 'IframeContainer',
    components: {
        DetailHeader,
    },
    props: {
        title: {
            type: String,
            default: '内容查看',
        },
        description: {
            type: String,
            default: '',
        },
        iframeSrc: {
            type: String,
            required: true,
        },
        // 是否向iframe传递消息
        sendMessage: {
            type: Boolean,
            default: false,
        },
        // 传递给iframe的消息内容
        messageData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            iframeLoaded: false,
        }
    },
    watch: {
        messageData: {
            handler(newVal) {
                if (this.iframeLoaded && this.sendMessage) {
                    this.postMessageToIframe(newVal)
                }
            },
            deep: true,
        },
    },
    methods: {
        handleBack() {
            this.$emit('back')
        },
        handleConfirm() {
            this.$emit('confirm')
        },
        handleClose() {
            this.$emit('close')
        },
        handleIframeLoad() {
            this.iframeLoaded = true
            this.$emit('iframe-loaded')

            // 如果需要发送消息，在iframe加载完成后发送
            if (this.sendMessage) {
                this.postMessageToIframe(this.messageData)
            }

            // 添加事件监听，接收来自iframe的消息
            window.addEventListener('message', this.receiveMessage)
        },
        postMessageToIframe(data) {
            const iframe = this.$refs.contentIframe
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage(data, '*')
            }
        },
        receiveMessage(event) {
            // 将接收到的消息传递给父组件
            this.$emit('iframe-message', event.data)
        },
    },
    beforeDestroy() {
        // 组件销毁前移除事件监听
        window.removeEventListener('message', this.receiveMessage)
    },
}
</script>

<style lang="less" scoped>
.iframe-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;

    .iframe-wrapper {
        flex: 1;
        overflow: hidden;
        position: relative;

        .content-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background-color: #fff;
        }
    }
}
</style>
