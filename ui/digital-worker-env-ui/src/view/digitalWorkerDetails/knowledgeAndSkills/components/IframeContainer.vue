<template>
    <div class="iframe-container">
        <DetailHeader :title="title" :description="description" @back="handleBack" @confirm="handleConfirm" @close="handleClose" />
        <div class="iframe-wrapper" v-loading="!iframeLoaded" element-loading-spinner="el-icon-loading" element-loading-text="加载中...">
            <!-- 微前端容器 -->
            <div v-if="useMicroFrontend">
                <!-- 思维树&思维链微前端容器 -->
                <div v-if="isMindMapApp" id="mindMapContainer" class="micro-app-container">
                    <!-- 微前端加载状态 -->
                    <div v-if="!iframeLoaded" class="micro-app-loading">
                        <div class="loading-content">
                            <div class="loading-spinner"></div>
                            <p>正在加载 {{ title }}...</p>
                        </div>
                    </div>
                </div>

                <!-- 知识库搜索微前端容器 -->
                <div v-else id="knowledge-search" class="micro-app-container">
                    <!-- 微前端加载状态 -->
                    <div v-if="!iframeLoaded" class="micro-app-loading">
                        <div class="loading-content">
                            <div class="loading-spinner"></div>
                            <p>正在加载 {{ title }}...</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 临时使用iframe作为备选方案 -->
            <iframe v-else ref="contentIframe" src="http://localhost:8081/" frameborder="0" class="content-iframe" @load="handleIframeLoad"></iframe>
        </div>
    </div>
</template>

<script>
import DetailHeader from './DetailHeader.vue'
import { loadMicroApp } from 'qiankun'
import { getAppConfig } from '@/config/microAppConfig'

export default {
    name: 'IframeContainer',
    components: {
        DetailHeader
    },
    props: {
        title: {
            type: String,
            default: '内容查看'
        },
        description: {
            type: String,
            default: '',
        },
        // iframeSrc: {
        //     type: String,
        //     required: true,
        // },
        // 是否向iframe传递消息
        sendMessage: {
            type: Boolean,
            default: false,
        },
        // 传递给iframe的消息内容
        messageData: {
            type: Object,
            default: () => ({}),
        },
        // 是否启用微前端模式
        enableMicroFrontend: {
            type: Boolean,
            default: true // 如需回滚，改为false即可
        },
        // 目标路由（支持动态切换）
        targetRoute: {
            type: String,
            default: '/knowledgeBaseSearch'
        },
        mtPlatStaffId: {
            type: String | Number,
            default: ''
        }
    },
    data() {
        return {
            iframeLoaded: false,
            microApp: null,
            // 控制是否强制进行环境预处理（主要用于调试）
            forceEnvironmentPrep: false
        }
    },
    computed: {
        useMicroFrontend() {
            return this.enableMicroFrontend
        },

        // 判断是否是思维树&思维链应用
        isMindMapApp() {
            return this.title === '思维树&思维链'
        },

        // 获取微应用配置
        microAppConfig() {
            if (this.isMindMapApp) {
                return getAppConfig('mindMap-ui', {
                    showRightSuffix: true,
                    isNumberPerson: false,
                    title: this.title,
                    description: this.description,
                    routeId: this.mtPlatStaffId
                })
            } else {
                return getAppConfig('kg-ui', {
                    targetPath: this.targetRoute || '/knowledgeBaseSearch',
                    title: this.title,
                    description: this.description,
                    routeId: this.mtPlatStaffId
                })
            }
        }
    },
    watch: {
        messageData: {
            handler(newVal) {
                if (this.iframeLoaded && this.sendMessage) {
                    this.postMessageToIframe(newVal)
                }
            },
            deep: true
        }
    },
    methods: {
        handleBack() {
            console.log('用户点击退出，准备销毁微前端')

            // 先销毁微前端，再触发退出事件
            this.destroyMicroApp()

            // 延迟一点时间确保销毁完成
            setTimeout(() => {
                this.$emit('back')
            }, 100)
        },
        handleConfirm() {
            this.$emit('confirm')
        },
        handleClose() {
            this.$emit('close')
        },
        handleIframeLoad() {
            this.iframeLoaded = true
            this.$emit('iframe-loaded')

            // 如果需要发送消息，在iframe加载完成后发送
            if (this.sendMessage) {
                this.postMessageToIframe(this.messageData)
            }

            // 添加事件监听，接收来自iframe的消息
            window.addEventListener('message', this.receiveMessage)
        },
        postMessageToIframe(data) {
            const iframe = this.$refs.contentIframe
            if (iframe && iframe.contentWindow) {
                try {
                    // 确保数据可以安全序列化
                    const safeData = this.sanitizeEventData(data)
                    iframe.contentWindow.postMessage(safeData, '*')
                } catch (error) {
                    console.error('发送消息失败:', error)
                }
            }
        },
        receiveMessage(event) {
            try {
                console.log('📨 收到消息:', event.data)

                // 处理来自微前端的消息
                if (event.data && event.data.type === 'MICRO_APP_ROUTE_CHANGE') {
                    console.log('🔗 子应用路由变化:', event.data)
                    // 只记录消息，不修改主应用URL（参考HTML测试页面）
                    this.$emit('micro-app-route-change', event.data)
                }

                // 处理微应用状态消息
                if (event.data && event.data.type === 'MICRO_APP_MESSAGE') {
                    console.log('📋 微应用状态消息:', event.data.data)
                }

                // 安全地传递消息，避免循环引用
                const safeEventData = this.sanitizeEventData(event.data)
                this.$emit('iframe-message', safeEventData)
            } catch (error) {
                console.error('处理消息时发生错误:', error)
            }
        },

        // 清理事件数据，避免循环引用
        sanitizeEventData(data, seen = new WeakSet()) {
            if (data === null || typeof data !== 'object') {
                return data
            }

            // 检测循环引用
            if (seen.has(data)) {
                return '[Circular Reference]'
            }

            seen.add(data)

            try {
                if (Array.isArray(data)) {
                    return data.map(item => this.sanitizeEventData(item, seen))
                }

                const sanitized = {}
                for (const key in data) {
                    if (data.hasOwnProperty(key)) {
                        // 跳过一些可能导致问题的属性
                        if (key === 'vnode' || key === 'component' || key === '$el' || key === '__vue__') {
                            continue
                        }

                        try {
                            sanitized[key] = this.sanitizeEventData(data[key], seen)
                        } catch (error) {
                            sanitized[key] = `[Error: ${error.message}]`
                        }
                    }
                }
                return sanitized
            } catch (error) {
                console.warn('数据清理失败:', error)
                return {
                    type: data.type || 'unknown',
                    timestamp: Date.now(),
                    message: '数据清理失败',
                    error: error.message
                }
            }
        },
        // 加载微前端应用
        async loadMicroApp() {
            try {
                console.log('开始加载微前端应用...')
                console.log('应用类型:', this.isMindMapApp ? '思维树&思维链' : '知识库搜索')
                console.log('目标路由:', this.targetRoute)

                // 先确保之前的应用完全销毁
                if (this.microApp) {
                    console.log('发现已存在的微前端应用，先销毁')
                    this.destroyMicroApp()
                    // 等待销毁完成
                    await new Promise(resolve => setTimeout(resolve, 300))
                }

                // 获取当前应用配置
                const config = this.microAppConfig

                // 先检查子应用是否可访问
                const response = await fetch(config.entry)
                if (!response.ok) {
                    throw new Error(`子应用无法访问: ${response.status}`)
                }

                // 确保容器存在
                const container = document.querySelector(config.container)
                if (!container) {
                    throw new Error(`微前端容器不存在: ${config.container}`)
                }

                // 彻底清空容器
                container.innerHTML = ''
                container.style.cssText = ''
                console.log('容器已彻底清空')

                console.log('容器元素:', container)
                console.log('容器尺寸:', container.getBoundingClientRect())

                // 确保容器有明确的尺寸约束
                container.style.position = 'relative'
                container.style.boxSizing = 'border-box'
                // 移除 overflow: hidden，允许滚动
                // 移除 contain: layout，避免影响滚动

                // 使用计算属性中的配置，并添加额外的运行时信息
                const microAppConfig = {
                    ...config,
                    props: {
                        ...config.props,
                        timestamp: Date.now(),
                        // 添加容器信息
                        containerInfo: {
                            width: container.clientWidth,
                            height: container.clientHeight,
                            id: container.id
                        },
                        // 添加路由导航信息（仅对知识库搜索应用）
                        ...(this.isMindMapApp ? {} : {
                            route: this.targetRoute,
                            testMode: true,
                            navigation: {
                                shouldNavigate: true
                            }
                        })
                    }
                }

                // 根据应用类型和环境优化沙箱配置
                const appSandboxConfig = this.getOptimizedSandboxConfig()

                // 只在开发环境输出详细日志
                if (process.env.NODE_ENV === 'development') {
                    console.log('微前端配置:', microAppConfig)
                    console.log('沙箱配置:', appSandboxConfig)
                }

                this.microApp = this.performanceWrapper(
                    () => loadMicroApp(microAppConfig, appSandboxConfig),
                    `加载${this.isMindMapApp ? '思维树' : 'kg-ui'}微应用`
                )

                // 只对知识库搜索应用发送路由信息
                if (!this.isMindMapApp) {
                    this.sendInitialRoute()
                }

                await this.microApp.mountPromise
                this.iframeLoaded = true

                // 只在开发环境输出详细日志
                if (process.env.NODE_ENV === 'development') {
                    console.log(`微前端应用加载成功: ${config.name}`)
                    console.log('容器内容:', container.innerHTML.substring(0, 200))
                }

                // 只对知识库搜索应用发送路由导航消息
                if (!this.isMindMapApp) {
                    this.navigateToRoute()
                } else {
                    // 对思维树应用发送特殊的初始化消息
                    this.sendMessageToMindMap({
                        type: 'INIT',
                        data: {
                            title: this.title,
                            description: this.description,
                            mtPlatStaffId: this.mtPlatStaffId
                        }
                    })
                }

                this.$emit('iframe-loaded')
            } catch (error) {
                // 根据环境决定错误处理方式
                if (process.env.NODE_ENV === 'development') {
                    console.error('微前端应用加载失败，降级到iframe模式:', error)
                } else {
                    console.warn('微前端应用加载失败，降级到iframe模式')
                }

                // 降级到iframe模式
                this.useMicroFrontend = false
                this.$nextTick(() => {
                    this.iframeLoaded = true
                })
            }
        },

        // 立即发送初始路由（避免闪烁）
        sendInitialRoute() {
            if (this.targetRoute && this.targetRoute !== '/') {
                console.log('立即发送初始路由:', this.targetRoute)

                // 设置全局变量，让B工程在mount前就能获取
                window.__MICRO_APP_INITIAL_ROUTE__ = this.targetRoute

                // 立即发送多种格式的消息
                const routeMessage = {
                    type: 'INITIAL_ROUTE',
                    route: this.targetRoute,
                    targetPath: this.targetRoute,
                    timestamp: Date.now()
                }

                // 立即发送
                window.postMessage(routeMessage, '*')

                // 延迟再发送一次，确保B工程能收到
                setTimeout(() => {
                    window.postMessage(routeMessage, '*')
                }, 50)

                console.log('初始路由已设置为全局变量:', window.__MICRO_APP_INITIAL_ROUTE__)
            }
        },

        // 导航到指定路由
        navigateToRoute() {
            if (this.targetRoute && this.targetRoute !== '/') {
                console.log('发送路由导航消息:', this.targetRoute)

                // 向微前端发送路由导航消息
                setTimeout(() => {
                    window.postMessage({
                        type: 'NAVIGATE_TO_ROUTE',
                        route: this.targetRoute,
                        timestamp: Date.now()
                    }, '*')
                }, 500) // 减少延迟时间
            }
        },

        // 获取优化的沙箱配置
        getOptimizedSandboxConfig() {
            const isDev = process.env.NODE_ENV === 'development'

            if (this.isMindMapApp) {
                // 思维树应用配置
                return {
                    sandbox: {
                        strictStyleIsolation: false,
                        experimentalStyleIsolation: true,
                        loose: true,
                        skipCheckForMultipleInstance: true
                    }
                }
            } else {
                // kg-ui 应用配置 - 生产环境使用更宽松的配置
                return {
                    sandbox: isDev ? {
                        strictStyleIsolation: false,
                        experimentalStyleIsolation: true,
                        loose: true
                    } : {
                        // 生产环境使用最宽松配置，减少错误
                        strictStyleIsolation: false,
                        experimentalStyleIsolation: false,
                        loose: true
                    }
                }
            }
        },

        // 安全的错误处理包装器
        safeExecute(fn, context = '操作') {
            try {
                return fn()
            } catch (error) {
                if (process.env.NODE_ENV === 'development') {
                    console.error(`${context}失败:`, error)
                }
                return null
            }
        },

        // 性能监控包装器
        performanceWrapper(fn, label) {
            if (process.env.NODE_ENV === 'development') {
                const start = performance.now()
                const result = fn()
                const end = performance.now()
                console.log(`${label} 耗时: ${(end - start).toFixed(2)}ms`)
                return result
            } else {
                return fn()
            }
        },

        // 向思维树应用发送消息
        sendMessageToMindMap(message) {
            if (this.isMindMapApp && this.microApp) {
                this.safeExecute(() => {
                    window.postMessage({
                        type: 'MAIN_APP_TO_MINDMAP',
                        target: 'mindMap-ui',
                        data: message
                    }, '*')

                    if (process.env.NODE_ENV === 'development') {
                        console.log('发送消息到思维树应用:', message)
                    }
                }, '发送消息到思维树应用')
            }
        },

        // 处理来自思维树应用的消息
        handleMindMapMessage(event) {
            this.safeExecute(() => {
                const { type, data } = event.data || {}

                if (type === 'MINDMAP_TO_MAIN_APP') {
                    if (process.env.NODE_ENV === 'development') {
                        console.log('收到思维树应用消息:', data)
                    }

                    switch (data.type) {
                        case 'LOADED':
                            if (process.env.NODE_ENV === 'development') {
                                console.log('思维树应用加载完成')
                            }
                            this.$message.success('思维树&思维链模块加载完成')
                            break
                        case 'ROUTE_CHANGE':
                            if (process.env.NODE_ENV === 'development') {
                                console.log('思维树应用路由变化:', data.route)
                            }
                            break
                        case 'DATA_CHANGE':
                            if (process.env.NODE_ENV === 'development') {
                                console.log('思维树应用数据变化:', data.data)
                            }
                            break
                        case 'ERROR':
                            console.error('思维树应用错误:', data.error)
                            this.$message.error(`思维树应用错误: ${data.error}`)
                            break
                        default:
                            break
                    }

                    // 向父组件传递消息
                    this.$emit('mindmap-message', data)
                }
            }, '处理思维树应用消息')
        },

        // 销毁微前端应用
        destroyMicroApp() {
            if (this.microApp) {
                try {
                    // 2. 卸载微前端应用
                    this.microApp.unmount()
                    this.microApp = null

                    // 3. 彻底清空容器内容
                    const container = document.getElementById('knowledge-search')
                    if (container) {
                        container.innerHTML = ''
                        // 强制重置容器样式
                        container.style.cssText = ''
                        console.log('微前端容器已彻底清空')
                    }

                    // 4. 清理可能的全局状态
                    if (window.__POWERED_BY_QIANKUN__) {
                        // 清理qiankun相关的全局状态
                        delete window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__
                    }

                    // 5. 重置组件状态
                    this.iframeLoaded = false

                    console.log('微前端应用已完全销毁')
                } catch (error) {
                    console.error('销毁微前端应用时出错:', error)
                }
            } else {
                console.log('没有微前端应用需要销毁')
            }
        }
    },
    mounted() {
        // 添加消息监听器
        window.addEventListener('message', this.handleMindMapMessage)

        // 组件挂载后尝试加载微前端应用
        if (this.useMicroFrontend) {
            this.loadMicroApp()
        } else {
            // 如果不使用微前端，直接显示iframe
            this.iframeLoaded = true
        }
    },
    beforeDestroy() {
        console.log('组件销毁，清理微前端资源')

        // 组件销毁前移除事件监听
        window.removeEventListener('message', this.receiveMessage)
        window.removeEventListener('message', this.handleMindMapMessage)

        // 销毁微前端应用
        this.destroyMicroApp()
    }
}
</script>

<style lang="less" scoped>
.iframe-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    max-width: 100%;
    /* 移除 max-height: 100vh 限制 */
    overflow: visible; /* 允许滚动 */
    position: relative;
    box-sizing: border-box;
    .iframe-wrapper {
        flex: 1;
        overflow: visible; /* 允许内容滚动 */
        position: relative;
        min-height: 600px; /* 设置最小高度而不是0 */
        box-sizing: border-box;

        .content-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background-color: #fff;
        }

        .micro-app-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e1e1;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        .loading-route {
            color: #666;
            font-size: 12px;
            margin-top: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .micro-app-container {
            width: 100%;
            height: auto; /* 改为自动高度 */
            min-height: 100vh;
            background-color: #EBECF1;
            overflow: auto; /* 允许内部滚动 */
            position: relative;
            box-sizing: border-box;
            /* 保持样式隔离但允许滚动 */
            contain: style paint;
        }

        /* 思维树应用的特殊样式 */
        #mindMapContainer {
            isolation: isolate;
            z-index: 1;
            /* 思维树可能需要更大的空间 */
            min-height: 100vh;
        }

        /* 知识库搜索应用的样式 */
        #knowledge-search {
            isolation: isolate;
            z-index: 1;
        }
    }
}
</style>
