<template>
    <div class="detail-header">
        <div class="back-button" @click="handleBack">
            <i class="el-icon-arrow-left"></i>
        </div>
        <div class="header-content">
            <h2 class="title">{{ title }}</h2>
            <p class="description" :title="description">
                {{ description }}
            </p>
        </div>
        <!-- <div class="action-buttons">
            <el-button type="primary" @click="handleConfirm">确定</el-button>
            <el-button icon="el-icon-close" class="close-button" @click="handleClose"></el-button>
        </div> -->
    </div>
</template>

<script>
export default {
    name: 'DetailHeader',
    props: {
        title: {
            type: String,
            required: true,
        },
        description: {
            type: String,
            default: '',
        },
    },
    methods: {
        handleBack() {
            this.$emit('back')
        },
        handleConfirm() {
            this.$emit('confirm')
        },
        handleClose() {
            this.$emit('close')
        },
    },
}
</script>

<style lang="less" scoped>
.detail-header {
    min-height: 66px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px 18px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    z-index: 10;
    flex-shrink: 0;

    .back-button {
        font-size: 18px;
        margin-right: 15px;
        cursor: pointer;
    }

    .header-content {
        max-width: calc(100% - 35px);
        flex: 1;

        .title {
            font-size: 16px;
            margin: 0 0 5px 0;
            font-weight: 500;
        }

        .description {
            font-size: 12px;
            color: #606266;
            margin: 0;
            line-height: 1.5;
            white-space: nowrap; /* 强制文本不换行 */
            overflow: hidden; /* 隐藏溢出内容 */
            text-overflow: ellipsis; /* 显示省略号 */
        }
    }

    .action-buttons {
        display: flex;
        gap: 10px;

        .close-button {
            border: none;
            padding: 9px;
        }
    }
}
</style>
