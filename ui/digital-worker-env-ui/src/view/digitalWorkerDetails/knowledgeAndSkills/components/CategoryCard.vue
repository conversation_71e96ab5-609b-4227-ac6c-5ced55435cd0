<template>
    <div class="category-card" :class="{ 'worker-skill-card': isWorkerSkill }" @click="$emit('click')">
        <div class="card-icon">
            <i :class="icon"></i>
        </div>
        <div class="card-title">{{ title }}</div>
    </div>
</template>

<script>
export default {
    name: 'CategoryCard',
    props: {
        title: {
            type: String,
            required: true,
        },
        icon: {
            type: String,
            default: 'el-icon-plus',
        },
        isWorkerSkill: {
            type: Boolean,
            default: false,
        },
    },
}
</script>

<style lang="less" scoped>
.category-card {
    width: 120px;
    height: 100px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        border-color: #c6e2ff;
        background-color: #f5f7fa;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #ecf5ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;

        i {
            font-size: 20px;
            color: #409eff;
        }
    }

    .card-title {
        font-size: 14px;
        color: #606266;
    }

    // 员工技能页面特殊样式
    &.worker-skill-card {
        flex: 1;
        min-width: 150px;
        width: auto;
        height: auto;
        flex-direction: row;
        padding: 15px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

        &:hover {
            background-color: #f0f9ff;
            border-color: #a0cfff;
        }

        .card-icon {
            width: 24px;
            height: 24px;
            border-radius: 0;
            background-color: transparent;
            margin-right: 10px;
            margin-bottom: 0;

            i {
                color: #909399;
            }
        }

        .card-title {
            color: #303133;
        }
    }
}
</style>
