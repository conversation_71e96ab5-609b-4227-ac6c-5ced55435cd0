<template>
    <div class="container">
        <!-- 对话互动 -->
        <div class="section" v-for="(item,i) in cardList" :key="i">
            <div class="section-header">
                <img :src="item.icon" v-if="item.icon">
                <i v-else class="el-icon-chat-dot-round section-icon"></i>
                <span class="section-title">{{item.title}}</span>
                <div class="section-actions">
                    <el-button size="small" @click="handleManage(item.url)" v-if="!sceneList">管理</el-button>
                </div>
            </div>
            <p class="section-description">
                {{item.description}}
            </p>
            <div class="conversation-item" v-if="sceneList">
                <div class="conversation-left">
                    <div class="conversation-icon">
                        <i class="el-icon-message"></i>
                    </div>
                    <span class="conversation-name">{{ sceneList.sceneName }}</span>
                </div>
                <div class="conversation-actions">
                    <el-button type="text" size="small" @click="openSceneDialog(sceneList, 'view')">查看</el-button>
                    <el-button type="text" size="small" @click="openSceneDialog(sceneList, 'edit')">编辑</el-button>
                    <el-button type="text" size="small" @click="handleDelete()">删除</el-button>
                </div>
            </div>
        </div>

        <!-- 数字人互动 -->
        <!-- <div class="section">
        <div class="section-header">
          <i class="el-icon-user-solid section-icon"></i>
          <span class="section-title">数字人互动</span>
          <div class="section-actions">
            <el-button size="small" @click="handleManage">管理</el-button>
          </div>
        </div>
        <p class="section-description">
          数字人模拟人类外貌和声音、面部表情、肢体动作与你自然交流，提供更友好的交互体验。
        </p>
      </div> -->

        <!-- 大屏 -->
        <!-- <div class="section">
        <div class="section-header">
          <i class="el-icon-monitor section-icon"></i>
          <span class="section-title">大屏</span>
          <div class="section-actions">
            <el-button size="small" @click="handleManage">管理</el-button>
          </div>
        </div>
      </div> -->

        <!-- 数字人 -->
        <!-- <div class="section">
        <div class="section-header">
          <i class="el-icon-user section-icon"></i>
          <span class="section-title">数字人</span>
          <div class="section-actions">
            <el-button size="small" @click="handleManage">管理</el-button>
          </div>
        </div>
        <div class="conversation-item">
          <div class="conversation-left">
            <div class="conversation-icon">
              <i class="el-icon-user"></i>
            </div>
            <span class="conversation-name">数字人名称</span>
          </div>
          <div class="conversation-actions">
            <el-button type="text" size="small">查看</el-button>
            <el-button type="text" size="small">编辑</el-button>
            <el-button type="text" size="small">删除</el-button>
          </div>
        </div>
      </div> -->

        <el-dialog title="会话场景管理" :visible.sync="dialogVisible" width="80%" @close="handleDialogClose" append-to-body>
            <div style="position:relative;min-height:400px;">
                <iframe v-show="!iframeLoading" :src="iframeUrl" width="100%" height="600" frameborder="0"
                    @load="handleIframeLoad"></iframe>
                <div v-if="iframeLoading" class="iframe-loading-mask">
                    <el-spinner></el-spinner>
                    <div style="margin-top: 10px;">加载中...</div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getKnowledgeAndSkillsConfigList, removeInteractiveScene, getInteractiveSceneList } from '@/api/digitalWorkerApi'
export default {
    name: 'Interface',
    props: {
        mtPlatStaffId: ''
    },
    data() {
        return {
            dialogVisible: false,
            iframeLoading: true,
            iframeUrl: '',
            cardList: [],
            sceneList: {}
        }
    },
    methods: {
        // 获取卡片列表数据
        async fetchCardList() {
            this.loading = true
            try {
                let params = {
                    parentModel: 4
                }
                const response = await getKnowledgeAndSkillsConfigList(params)
                if (response.success) {
                    this.cardList = response.body.map((item) => ({
                        icon: item.modelUrl || '',
                        title: item.modelName,
                        description: item.modelDesc,
                        modelType: item.modelType,
                        url: item.modelPath + '?mtPlatStaffId=' + this.mtPlatStaffId,
                    }))
                } else {
                    const errorMsg = (response && response.head && response.head.respMsg) || '获取数据失败'
                    this.$message.error(errorMsg)
                }
                this.loading = false
            } catch (error) {
                this.loading = false
                console.error('接口调用失败:', error)
                this.$message.error('接口调用失败，请稍后重试')
            }
        },
        handleLogin() {
            console.log('登录')
        },
        handleEdit() {
            console.log('编辑')
        },
        handleDelete() {
            this.$confirm('是否删除该会话场景？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                removeInteractiveScene({ mtPlatStaffId: this.mtPlatStaffId}).then(res => {
                    this.$message.success('删除成功')
                    this.fetchSceneList()
                }).catch(() => {
                    this.$message.error('删除失败')
                })
            }).catch(() => {
                // 用户取消
            })
        },
        handleManage(url) {
            this.iframeUrl = url
            this.dialogVisible = true
            this.iframeLoading = true
        },
        handleDigitalHumanManage() {
            console.log('数字人管理')
        },
        handleIframeLoad() {
            this.iframeLoading = false
        },
        handleDialogClose() {
            this.iframeUrl = ''
            this.iframeLoading = true
        },
        fetchSceneList() {
            getInteractiveSceneList({ mtPlatStaffId: this.mtPlatStaffId }).then(res => {
                console.log('获取场景列表成功', res.body)
                this.sceneList = res.body
            })
        },
        openSceneDialog(scene, type) {
            let url = `/opia-ui/#/sessionScenarioMaintenanceDetail?id=${scene.diBehaviorSceneId}&beforeRoutePath=robot`
            if (type === 'view') {
                url += '&type=view'
            }
            this.iframeUrl = url
            this.dialogVisible = true
            this.iframeLoading = true
        },
        handleIframeMessage(event) {
            // 可根据需要校验 event.origin
            const data = event.data
            if (
                data &&
                data.from === 'opia-ui' &&
                data.path === 'sessionScenarioMaintenance'
            ) {
                this.dialogVisible = false
                this.fetchSceneList()
            }
        }
    },
    mounted() {
        this.fetchCardList()
        this.fetchSceneList()
        window.addEventListener('message', this.handleIframeMessage)
    },
    beforeDestroy() {
        window.removeEventListener('message', this.handleIframeMessage)
    }
}
</script>

<style scoped>
.container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.section {
    background-color: white;
    margin-bottom: 20px;
    padding: 20px;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
}

.section-icon {
    color: #999;
    margin-right: 8px;
    font-size: 16px;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.section-actions {
    margin-left: auto;
}

.section-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 20px 24px;
}

.conversation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    margin-left: 24px;
}

.conversation-left {
    display: flex;
    align-items: center;
}

.conversation-icon {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: #999;
}

.conversation-name {
    color: #333;
    font-size: 14px;
}

.conversation-actions {
    display: flex;
    gap: 10px;
}

.conversation-actions .el-button {
    color: #409eff;
    padding: 0;
}

.conversation-actions .el-button:hover {
    color: #66b1ff;
}

.iframe-loading-mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.card {
    width: calc(33.33% - 20px);
    background-color: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-img {
    width: 100%;
    height: 200px;
    background-color: #f0f0f0;
}

.card-content {
    padding: 20px;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-actions .el-button {
    color: #409eff;
    padding: 0;
}

.card-actions .el-button:hover {
    color: #66b1ff;
}
</style>