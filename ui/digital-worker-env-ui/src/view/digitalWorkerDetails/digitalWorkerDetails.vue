<template>
    <div class="digital-worker-details" v-loading="loading" element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="fixed-header">
            <div class="header">
                <div class="title-section">
                    <el-button icon="el-icon-arrow-left" class="back-button" size="small" circle @click="handleCancelClick"></el-button>
                    <div class="title">{{ workerName }}</div>
                </div>
                <div class="actions">
                    <el-button @click="handleCancelClick">取消</el-button>
                    <el-button v-if="!isViewMode" @click="handleSaveDraft">暂存</el-button>
                    <!-- <el-button @click="handleTest">测试</el-button> -->
                    <el-button v-if="!isViewMode" type="primary" @click="handleSubmit">提交</el-button>
                </div>
            </div>

            <div class="tabs-container">
                <el-tabs v-model="activeTab" class="centered-tabs">
                    <el-tab-pane label="基本信息" name="basicInfo"></el-tab-pane>
                    <el-tab-pane label="知识&技能" name="knowledge"></el-tab-pane>
                    <el-tab-pane label="感知触发" name="perception"></el-tab-pane>
                    <el-tab-pane label="交互体验" name="interaction"></el-tab-pane>
                    <el-tab-pane label="团队协作" name="teamwork"></el-tab-pane>
                    <el-tab-pane label="集成开发" name="integration"></el-tab-pane>
                    <el-tab-pane label="数据&工具" name="dataTools"></el-tab-pane>
                </el-tabs>
            </div>
        </div>

        <div class="content">
            <!-- 当activeTab为knowledge时显示知识和技能组件 -->
            <BasicInfo v-if="activeTab === 'basicInfo'" ref="basicInfoComponent" :initialData="workerData" @form-change="handleFormChange" :disabled="isViewMode" />
            <KnowledgeAndSkills v-else-if="activeTab === 'knowledge'" ref="knowledgeComponent" :mtPlatStaffId="mtPlatStaffId" />
            <interactiveExperience  v-else-if="activeTab === 'interaction'" ref="interactionComponent" :mtPlatStaffId="mtPlatStaffId"/>
            <!-- 其他标签页内容置空 -->
            <div v-else class="empty-content">
                <p>该标签页内容暂未实现</p>
            </div>
        </div>
    </div>
</template>

<script>
import KnowledgeAndSkills from './knowledgeAndSkills/index.vue'
import BasicInfo from './basicInfo.vue'
import InteractiveExperience from './interactiveExperience.vue'
import { digitalWorkerSubmit, editLockGetInfoById, digitalWorkerSave, editLock, editRemoveLock, downloadFileUrl } from '@/api/digitalWorkerApi'

export default {
    name: 'DigitalWorkerDetails',
    components: {
        BasicInfo,
        KnowledgeAndSkills,
        InteractiveExperience
    },
    props: {
        mtPlatStaffId: '',
        staffName: '',
        viewMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            activeTab: 'basicInfo',
            workerData: {},
            workerName: '',
            originalWorkerData: null,
            currentWorkerData: null,
            contentChanged: false,
            isPublished: false,
            isSavedAsDraft: false,
            isEdit: false,
            lockTimer: null,
            isViewMode: false,
            cleanupDone: false,
            loading: false,
        }
    },
    created() {
        // 设置查看模式
        this.isViewMode = this.viewMode

        // 判断是否为编辑模式
        this.isEdit = !!this.mtPlatStaffId

        if (this.isEdit) {
            // 编辑模式下，从接口获取详细数据
            this.getWorkerData(this.mtPlatStaffId)

            // 如果是编辑模式（非查看模式），启动锁定计时器
            if (!this.isViewMode && this.mtPlatStaffId) {
                this.startLockTimer()
            }
            this.workerName = this.staffName
        } else {
            this.workerName = '新增数字员工'
        }
    },
    mounted() {
        if (!this.isEdit) {
            this.$nextTick(() => {
                if (this.$refs.basicInfoComponent) {
                    this.originalWorkerData = JSON.parse(JSON.stringify(this.$refs.basicInfoComponent.formData))
                    this.currentWorkerData = JSON.parse(JSON.stringify(this.originalWorkerData))
                }
            })
        }
    },
    beforeDestroy() {
        // 组件销毁前清除定时器并解除锁定
        if (!this.cleanupDone) {
            this.clearLockTimer()
            this.removeEditLock()
            this.cleanupDone = true
        }
    },
    methods: {
        // 处理图标URL，如果有staffUrl则拼接下载地址前缀
        processIconUrl(url) {
            if (!url) return ''

            // 如果URL已经是完整的URL（包含http或https），则直接返回
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url
            }

            // 否则拼接downloadFileUrl返回的地址前缀
            return downloadFileUrl() + url
        },

        // 详情
        getWorkerData(mtPlatStaffId) {
            editLockGetInfoById({ mtPlatStaffId: mtPlatStaffId })
                .then((res) => {
                    if (res && res.success) {
                        const data = res.body
                        const formattedData = {
                            employeeName: data.staffName || '',
                            employeeId: data.staffNo || '',
                            displayName: data.displayName || '',
                            positionType: data.postType || '',
                            tags: data.label || '',
                            roleResponsibility: data.dutyRole || '',
                            mtPlatStaffId: data.mtPlatStaffId || '',
                            version: data.version || 0,
                            startupStatus: data.startupStatus || '',
                            createdTime: data.createdTime || '',
                            createdUserName: data.createdUserName || '',
                            updatedTime: data.updatedTime || '',
                            updatedUserName: data.updatedUserName || '',
                            // 保存原始staffUrl，用于提交时使用
                            originalStaffUrl: data.staffUrl || '',
                        }
                        this.workerData = formattedData

                        this.$nextTick(() => {
                            if (this.$refs.basicInfoComponent) {
                                this.$refs.basicInfoComponent.formData = formattedData

                                this.originalWorkerData = JSON.parse(JSON.stringify(formattedData))
                                this.currentWorkerData = JSON.parse(JSON.stringify(this.originalWorkerData))
                            }
                        })
                    } else {
                        this.$message.error(res.head.respMsg || '获取详情失败')
                    }
                })
                .catch((error) => {
                    console.error('查询失败', error)
                    this.$message.error('查询失败，请稍后重试')
                })
        },
        // 启动锁定计时器
        startLockTimer() {
            // 先清除可能存在的计时器
            this.clearLockTimer()

            // 如果有ID才启动计时器
            if (this.mtPlatStaffId) {
                // 先立即执行一次锁定
                this.lockEdit()

                // 设置定时器，每50秒执行一次锁定
                this.lockTimer = setInterval(() => {
                    this.lockEdit()
                }, 50000)
            }
        },
        // 清除锁定计时器
        clearLockTimer() {
            if (this.lockTimer) {
                clearInterval(this.lockTimer)
                this.lockTimer = null
            }
        },
        // 执行锁定操作
        lockEdit() {
            if (this.mtPlatStaffId) {
                editLock({ mtPlatStaffId: this.mtPlatStaffId }).catch((error) => {
                    console.error('锁定失败', error)
                })
            }
        },
        // 解除锁定
        removeEditLock() {
            if (this.mtPlatStaffId && !this.isViewMode) {
                editRemoveLock({ mtPlatStaffId: this.mtPlatStaffId }).catch((error) => {
                    console.error('解除锁定失败', error)
                })
            }
        },
        handleFormChange(formData) {
            console.log('formData:', formData)
            this.currentWorkerData = JSON.parse(JSON.stringify(formData))
            this.checkForChanges()
        },
        checkForChanges() {
            if (this.originalWorkerData && this.currentWorkerData) {
                this.contentChanged = JSON.stringify(this.originalWorkerData) !== JSON.stringify(this.currentWorkerData)
            }
        },
        handleCancelClick() {
            if (this.contentChanged && !this.isPublished && !this.isSavedAsDraft && !this.isViewMode) {
                this.$confirm('您有未保存的更改，确定要离开吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                })
                    .then(() => {
                        this.onCancel(false)
                    })
                    .catch(() => {})
            } else {
                const needRefresh = this.isPublished || this.isSavedAsDraft
                this.onCancel(needRefresh)
            }
        },
        onCancel(needRefresh = false) {
            // 清除锁定计时器并解除锁定
            if (!this.cleanupDone) {
                this.clearLockTimer()
                this.removeEditLock()
                this.cleanupDone = true
            }

            this.$emit('cancel', needRefresh)
        },
        validateAndGetFormData() {
            return new Promise((resolve, reject) => {
                if (this.$refs.basicInfoComponent) {
                    const basicInfoComponent = this.$refs.basicInfoComponent

                    basicInfoComponent.$refs.employeeForm.validate((valid) => {
                        if (valid) {
                            resolve(basicInfoComponent.formData)
                        } else {
                            this.$message.error('请完善基本信息')
                            reject('表单验证失败')
                        }
                    })
                } else {
                    reject('基本信息组件未加载')
                }
            })
        },
        async handleSaveDraft() {
            this.loading = true
            try {
                const formData = await this.validateAndGetFormData()

                // 构建暂存请求参数，与提交接口参数相同
                const requestData = {
                    staffName: formData.employeeName,
                    staffNo: formData.employeeId,
                    displayName: formData.displayName,
                    postType: formData.positionType,
                    label: formData.tags,
                    dutyRole: formData.roleResponsibility,
                    staffUrl: formData.originalStaffUrl,
                    mtPlatStaffId: formData.mtPlatStaffId || '',
                    startupStatus: '',
                    createdTime: '',
                    createdUserName: '',
                    updatedTime: '',
                    updatedUserName: '',
                    version: formData.version || 0,
                }

                console.log('暂存数据:', requestData)
                const res = await digitalWorkerSave(requestData)

                if (res && res.success) {
                    this.loading = false
                    this.$message.success('暂存成功')

                    // 更新状态
                    this.isSavedAsDraft = true
                    this.isEdit = true
                    this.getWorkerData(res.body.mtPlatStaffId)
                    this.contentChanged = false
                    this.startLockTimer()
                } else {
                    this.loading = false
                    const errorMsg = (res && res.head && res.head.respMsg) || '暂存失败'
                    this.$message.error(errorMsg)
                }
            } catch (error) {
                this.loading = false
                console.error('暂存失败:', error)
                // this.$message.error('暂存失败: ' + (error.message || '未知错误'))
            }
        },
        async handleTest() {
            try {
                const formData = await this.validateAndGetFormData()

                console.log('测试数据:', formData)

                console.log('测试成功')
            } catch (error) {
                console.error('测试失败:', error)
            }
        },
        async handleSubmit() {
            this.loading = true
            try {
                const formData = await this.validateAndGetFormData()

                const requestData = {
                    staffName: formData.employeeName,
                    staffNo: formData.employeeId,
                    displayName: formData.displayName,
                    postType: formData.positionType,
                    label: formData.tags,
                    dutyRole: formData.roleResponsibility,
                    staffUrl: formData.originalStaffUrl,
                    mtPlatStaffId: formData.mtPlatStaffId || '',
                    startupStatus: formData.startupStatus || 0,
                    createdTime: '',
                    createdUserName: '',
                    updatedTime: '',
                    updatedUserName: '',
                    version: formData.version || 0,
                }

                console.log('发送数据:', requestData)

                const response = await digitalWorkerSubmit(requestData)

                if (response && response.success) {
                    this.$message.success('提交成功')

                    this.isPublished = true
                    this.workerName = formData.employeeName || this.workerName
                    this.originalWorkerData = JSON.parse(JSON.stringify(formData))
                    this.checkForChanges()

                    this.onCancel(true)
                } else {
                    this.loading = false
                    const errorMsg = (response && response.head && response.head.respMsg) || '提交失败'
                    this.$message.error(errorMsg)
                }
            } catch (error) {
                this.loading = false
                console.error('提交失败:', error)
                // this.$message.error('提交失败: ' + (error.message || '未知错误'))
            }
        },
    },
}
</script>

<style lang="less">
.digital-worker-details {
    padding: 10px;
    background-color: #ebecf1;
    min-height: 100vh;
    box-sizing: border-box;
    overflow: hidden;

    .fixed-header {
        background-color: #fff;
        z-index: 10;
        position: relative;
        &:after {
            content: '';
            position: absolute;
            left: 10px;
            right: 10px;
            width: calc(100% - 20px);
            height: 1px;
            background: #ebeef5;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #ebeef5;

        .title-section {
            display: flex;
            align-items: center;

            .back-button {
                margin-right: 10px;
                border: 1px solid #dcdfe6;

                &:hover {
                    background-color: #f5f7fa;
                }
            }

            .title {
                font-size: 18px;
                font-weight: bold;
            }
        }

        .actions {
            display: flex;
            gap: 10px;
        }
    }

    .tabs-container {
        background-color: #fff;
        padding: 0 10px;

        .el-tabs__nav-wrap::after {
            display: none;
        }

        .el-tabs__active-bar {
            height: 3px;
        }
    }

    .centered-tabs {
        .el-tabs__header {
            display: flex;
            justify-content: center;
            margin: 5px 0 10px !important;
        }

        .el-tabs__nav-wrap {
            justify-content: center;
        }
    }

    .content {
        height: calc(100vh - 130px);
        min-height: calc(100vh - 130px) !important;
        padding: 10px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow-y: hidden;
        position: relative;
        .empty-content {
            width: 100%;
            height: 200px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #909399;
            font-size: 16px;
        }
    }
}
</style>
