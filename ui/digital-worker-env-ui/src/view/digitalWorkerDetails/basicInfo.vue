<template>
    <div class="dw-basic-info">
        <el-form :model="formData" :rules="rules" ref="employeeForm" label-width="150px" class="employee-form" :disabled="disabled">
            <div class="form-content">
                <div class="left-content">
                    <!-- 数字员工名称 -->
                    <el-form-item label="数字员工名称：" prop="employeeName" class="required-field">
                        <el-input v-model="formData.employeeName" placeholder="请输入数字员工名称" :disabled="disabled"></el-input>
                    </el-form-item>

                    <!-- 数字员工工号 -->
                    <el-form-item label="数字员工工号：" prop="employeeId" class="required-field">
                        <el-input v-model="formData.employeeId" placeholder="请输入数字员工工号" :disabled="disabled"></el-input>
                    </el-form-item>

                    <!-- 显示名称 -->
                    <el-form-item label="显示名称：" prop="displayName">
                        <el-input v-model="formData.displayName" placeholder="请输入数字员工的显示名称" :disabled="disabled"></el-input>
                    </el-form-item>

                    <!-- 岗位类型 -->
                    <el-form-item label="岗位类型：" prop="positionType" class="required-field">
                        <div class="position-type-cards">
                            <div
                                v-for="(item, index) in positionTypes"
                                :key="index"
                                class="position-card"
                                :class="{ active: formData.positionType == item.codeValue, disabled: disabled }"
                                @click="!disabled && selectPositionType(item.codeValue)"
                            >
                                {{ item.codeName }}
                            </div>
                        </div>
                    </el-form-item>

                    <!-- 标签 -->
                    <el-form-item label="标签：" prop="tags">
                        <div class="tag-input-container">
                            <div class="tag-list-wrapper">
                                <template v-if="formData.tags">
                                    <el-tag
                                        :key="tag"
                                        v-for="tag in formData.tags.split(';')"
                                        :closable="!disabled"
                                        :disable-transitions="false"
                                        @close="handleTagClose(tag)"
                                        class="tag-item"
                                        size="medium"
                                        effect="light"
                                        :type="disabled ? 'info' : ''"
                                    >
                                        {{ tag }}
                                    </el-tag>
                                </template>
                                <div class="tag-input-wrapper">
                                    <el-input
                                        class="input-new-tag"
                                        v-if="inputTagVisible"
                                        v-model="inputTagValue"
                                        ref="saveTagInput"
                                        size="small"
                                        @keyup.enter.native="handleInputTagConfirm"
                                        @blur="handleInputTagConfirm"
                                        placeholder="请输入标签"
                                    >
                                    </el-input>
                                    <el-button v-else class="button-new-tag" size="small" type="primary" plain icon="el-icon-plus" @click="showTagInput">添加标签</el-button>
                                </div>
                            </div>
                        </div>
                    </el-form-item>

                    <!-- 职责角色 -->
                    <el-form-item label="职责角色：" prop="roleResponsibility" class="required-field">
                        <div class="textarea-container">
                            <el-input
                                type="textarea"
                                v-model="formData.roleResponsibility"
                                placeholder="描述数字员工的职责角色，（按提示词的方式，对数字员工的职责角色给予概要介绍，起到了解该数字员工的工作范围及目的）"
                                :rows="10"
                                :disabled="disabled"
                            ></el-input>
                        </div>
                    </el-form-item>
                </div>

                <div class="right-content">
                    <!-- 图标 -->
                    <el-form-item label="图标：" prop="icon">
                        <div class="icon-upload">
                            <div class="icon-preview" @click="triggerUpload">
                                <img v-if="formData.originalStaffUrl" :src="processIconUrl(formData.originalStaffUrl)" alt="图标" class="preview-image" />
                                <div v-else class="placeholder-icon">
                                    <i class="el-icon-plus"></i>
                                </div>

                                <!-- 删除按钮 -->
                                <div v-if="formData.originalStaffUrl && !disabled" class="delete-icon" @click.stop="deleteIcon">
                                    <i class="el-icon-delete"></i>
                                </div>
                            </div>
                            <input type="file" ref="iconUpload" accept="image/*" style="display: none" @change="handleIconUpload" />
                        </div>
                    </el-form-item>
                </div>
            </div>

            <!-- 精选提示词区域 - 非禁用状态下显示 -->
            <div class="prompt-section" v-if="!disabled">
                <div class="prompt-section-title">精选提示词：</div>
                <div class="prompt-cards">
                    <el-popover v-for="(tab, index) in promptTabs" :key="index" placement="top" width="300" trigger="click" popper-class="prompt-popover" :ref="`popover-${tab.codeName}`">
                        <div class="tooltip-content">
                            {{ tab.codeValue }}
                            <div class="tooltip-actions">
                                <el-button type="primary" @click="insertPromptTemplate(tab.codeName)">插入提示词</el-button>
                            </div>
                        </div>
                        <div slot="reference" class="prompt-card" :class="{ active: activePromptTab == tab.codeName }" @click="activePromptTab = tab.codeName">
                            {{ tab.codeName }}
                        </div>
                    </el-popover>
                </div>
            </div>

            <!-- <div class="form-actions">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="submitForm">保存</el-button>
            </div> -->
        </el-form>
    </div>
</template>

<script>
import { getAllDictionary, uploadFile, downloadFileUrl } from '@/api/digitalWorkerApi'
export default {
    name: 'BasicInfo',
    props: {
        initialData: {
            type: Object,
            default: () => ({
                employeeName: '',
                employeeId: '',
                displayName: '',
                positionType: '',
                tags: '',
                roleResponsibility: '',
                originalStaffUrl: '',
            }),
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            formData: {
                employeeName: '',
                employeeId: '',
                displayName: '',
                positionType: '',
                tags: '',
                roleResponsibility: '',
                icon: '',
                originalStaffUrl: '',
            },
            rules: {
                employeeName: [{ required: true, message: '请输入数字员工名称', trigger: 'blur' }],
                employeeId: [{ required: true, message: '请输入数字员工工号', trigger: 'blur' }],
                positionType: [{ required: true, message: '请选择岗位类型', trigger: 'change' }],
                roleResponsibility: [{ required: true, message: '请输入职责角色', trigger: 'blur' }],
            },
            positionTypes: [],
            activePromptTab: 'knowledge',
            promptTabs: [],
            // 标签输入相关
            inputTagVisible: false,
            inputTagValue: '',
        }
    },
    created() {
        getAllDictionary(['post_type', 'duty_role_tip']).then((res) => {
            if (res && res.success) {
                const data = res.body || {}
                this.positionTypes = data['post_type'] || []
                this.promptTabs = data['duty_role_tip'] || []
            } else {
                this.$message.error(res.head.respMsg || '未知错误')
            }
        })
        // 初始化表单数据
        if (this.initialData) {
            this.formData = { ...this.formData, ...this.initialData }
        }
    },
    watch: {
        // 监听初始数据变化
        initialData: {
            handler(newVal) {
                if (newVal) {
                    this.formData = { ...this.formData, ...newVal }
                    console.log('初始数据变化:', this.formData)
                }
            },
            deep: true,
            immediate: true,
        },
        // 监听表单数据的变化
        formData: {
            handler(newVal) {
                // 当表单数据发生变化时，通知父组件
                this.$emit('form-change', newVal)
            },
            deep: true, // 深度监听对象内部属性的变化
        },
    },
    methods: {
        selectPositionType(type) {
            this.formData.positionType = type
        },
        triggerUpload() {
            // 如果表单被禁用，则不执行上传操作
            if (this.disabled) return

            this.$refs.iconUpload.click()
        },
        // 处理图标URL，如果有staffUrl则拼接下载地址前缀
        processIconUrl(url) {
            if (!url) return ''

            // 如果URL已经是完整的URL（包含http或https），则直接返回
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url
            }

            // 否则拼接downloadFileUrl返回的地址前缀
            return downloadFileUrl() + url
        },
        handleIconUpload(e) {
            const file = e.target.files[0]
            if (!file) return

            // 显示上传中状态
            // this.$message({
            //     message: '图标上传中...',
            //     type: 'info',
            // })

            // 构建FormData对象
            const formData = new FormData()
            formData.append('file', file)
            formData.append('path', 'file/staff')

            // 调用上传接口
            uploadFile(formData)
                .then((res) => {
                    if (res && res.success) {
                        // 上传成功，获取返回的文件地址
                        const fileUrl = res.body && res.body.docUrl
                        this.formData.originalStaffUrl = fileUrl
                        console.log('上传成功:', this.formData)
                        // this.$message.success('图标上传成功')
                    } else {
                        // 上传失败，清空文件输入框
                        this.$refs.iconUpload.value = ''
                        this.$message.error((res.head && res.head.respMsg) || '图标上传失败')
                    }
                })
                .catch((error) => {
                    console.error('图标上传失败:', error)
                    this.$refs.iconUpload.value = ''
                    this.$message.error('图标上传失败，请稍后重试')
                })
        },
        deleteIcon(e) {
            e.stopPropagation()
            this.formData.originalStaffUrl = ''
            // this.$message.success('图标已删除')

            // 清空文件输入框，允许重新选择相同文件
            if (this.$refs.iconUpload) {
                this.$refs.iconUpload.value = ''
            }
        },
        insertPromptTemplate(type) {
            // 查找对应类型的提示词模板
            const tab = this.promptTabs.find((item) => item.codeName == type)
            if (tab) {
                this.formData.roleResponsibility = tab.codeValue

                // 关闭弹窗
                const popoverRef = this.$refs[`popover-${tab.codeName}`]
                if (popoverRef && popoverRef.length > 0) {
                    popoverRef[0].doClose()
                }

                // this.activePromptTab = ''
            }
        },
        submitForm() {
            this.$refs.employeeForm.validate((valid) => {
                if (valid) {
                    // 表单验证通过，可以提交数据
                    console.log('提交的表单数据:', this.formData)
                    this.$message.success('保存成功')
                } else {
                    this.$message.error('请完善表单信息')
                    return false
                }
            })
        },
        cancel() {
            // 取消编辑，可以返回上一页或清空表单
            this.$confirm('确定取消编辑？未保存的内容将丢失', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    // 可以跳转到列表页或者重置表单
                    this.$refs.employeeForm.resetFields()
                })
                .catch(() => {
                    // 用户取消操作，不做任何处理
                })
        },
        // 添加标签输入方法
        showTagInput() {
            this.inputTagVisible = true
            this.$nextTick(() => {
                this.$refs.saveTagInput.$refs.input.focus()
            })
        },

        // 处理标签输入确认的方法
        handleInputTagConfirm() {
            let inputValue = this.inputTagValue
            if (inputValue) {
                inputValue = inputValue.trim()
                if (inputValue && !this.formData.tags.split(';').includes(inputValue)) {
                    this.formData.tags += (this.formData.tags ? ';' : '') + inputValue
                }
            }
            this.inputTagVisible = false
            this.inputTagValue = ''
        },
        handleTagClose(tag) {
            const index = this.formData.tags.split(';').indexOf(tag)
            if (index >= 0) {
                this.formData.tags = this.formData.tags
                    .split(';')
                    .filter((item) => item !== tag)
                    .join(';')
            }
        },
    },
}
</script>

<style lang="less" scoped>
.dw-basic-info {
    background-color: #fff;

    .employee-form {
        position: relative;

        .form-content {
            display: flex;

            .left-content {
                flex: 1;
                margin-right: 20px;
            }

            .right-content {
                width: 200px;
                /deep/ .el-form-item__label {
                    width: 80px !important;
                }
                /deep/ .el-form-item__content {
                    margin-left: 80px !important;
                }
            }
        }

        .required-field {
            /deep/ .el-form-item__label:before {
                content: '*';
                color: #f56c6c;
                margin-right: 4px;
            }
        }

        .position-type-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 4px 0px;

            .position-card {
                height: 34px;
                line-height: 34px;
                padding: 0px 12px;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                    border-color: #c0c4cc;
                }

                &.active {
                    color: #47e;
                    background: #ecf1fd;
                    border-color: #b4c9f8;
                }
            }
            .position-card.disabled {
                background: #ebecf1;
                cursor: no-drop;
            }
        }
    }

    .prompt-section {
        display: flex;
        align-items: center;
        margin-top: 10px;
        padding-left: 148px;
        box-sizing: border-box;

        .prompt-section-title {
            font-size: 14px;
            color: #606266;
        }

        .prompt-cards {
            display: flex;
            gap: 10px;

            .prompt-card {
                padding: 6px 12px;
                cursor: pointer;
                font-size: 14px;
                color: #333;
                background-color: #f5f7fa;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                text-align: center;
                transition: all 0.3s;

                &:hover {
                    border-color: #c0c4cc;
                }

                &.active {
                    color: #47e;
                    background: #ecf1fd;
                    border-color: #b4c9f8;
                }
            }
        }
    }

    .form-actions {
        margin-top: 20px;
        text-align: center;
    }

    .icon-upload {
        .icon-preview {
            width: 100px;
            height: 100px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            position: relative;
            overflow: hidden;

            &:hover {
                border-color: #409eff;

                .delete-icon {
                    opacity: 1;
                }
            }

            .preview-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .placeholder-icon {
                font-size: 28px;
                color: #8c939d;
            }

            .delete-icon {
                position: absolute;
                top: 0;
                right: 0;
                width: 24px;
                height: 24px;
                background-color: rgba(0, 0, 0, 0.5);
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                opacity: 0;
                transition: opacity 0.3s;

                &:hover {
                    background-color: rgba(0, 0, 0, 0.7);
                }
            }
        }
    }

    // 标签输入样式
    .tag-input-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-top: 4px;
        margin-bottom: 4px;

        .tag-list-wrapper {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 4px;
            min-height: 32px;
            padding: 5px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            background-color: #fff;

            &:empty {
                padding: 10px;
            }
        }

        .tag-item {
            margin: 3px;
        }

        .tag-input-wrapper {
            display: flex;
            align-items: center;
        }

        .input-new-tag {
            height: 28px;
            line-height: 28px;
            flex: 1;
            margin: 3px;
            /deep/ .el-input__inner {
                height: 28px !important;
            }
        }

        .button-new-tag {
            height: 28px;
            line-height: 28px;
            padding-top: 0;
            padding-bottom: 0;
            border-radius: 4px;
            margin: 3px 0px;
        }
    }
}
</style>
<style lang="less">
.prompt-popover {
    .tooltip-content {
        font-size: 14px;

        .tooltip-actions {
            text-align: center !important;
            margin-top: 5px;

            .el-button {
                width: 100%;
                padding: 8px 15px;
                box-sizing: border-box;
                font-size: 14px;
            }
        }
    }
}
</style>
