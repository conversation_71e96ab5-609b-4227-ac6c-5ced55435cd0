import util from '@/libs/util.js' // 或者通过bus总线引入亦可

// 调用参数带@RequestBody 的post接口
export const getInfo = () => {
    return util.ajaxPostData('/api/getInfo', { pra: 'ssss' })
}

// get需额外提供方法声明
export const getInfoThroughGet = () => {
    return util.ajaxMethodWidthParams('/api/getInfo', 'get')
}

// 默认post方式 ,后台不带@RequestBody
export const getInfoWithoutBody = () => {
    return util.ajaxGetData('/api/getInfoCommon', {
        pra: '不带requestBody的请求'
    })
}
