import util from '@/libs/util.js'
const axios = util.ajaxPostData
const projectName = '/portal-gateway/maintenance-plat-web'
const projectName2 = '/portal-gateway/cn-dids-web'

// 获取字典值
export function getAllDictionary(param) {
    return axios(`${projectName2}/api/dict/selectMap/getAllDictionary`, param)
}

// 上传附件
export function uploadFile(param) {
    return util.ajaxFileUpload(`${projectName2}/api/dfs/uploadFile`, param)
}

// 文件显示
export function downloadFileUrl() {
    return `${projectName2}/api/dfs/downloadFile?fileName=`
}

// dify登录
export function difyLogin() {
    return axios(`${projectName}/api/dify/login`)
}

// 获取数字员工列表
export function getDigitalWorkerList(param) {
    return axios(`${projectName}/api/staff/page`, param)
}

// 数字员工-提交
export function digitalWorkerSubmit(param) {
    return axios(`${projectName}/api/staff/submit`, param)
}

// 数字员工-启用禁用状态切换
export function digitalWorkerStatusSwitch(param) {
    return axios(`${projectName}/api/staff/statusSwitch`, param)
}

// 数字员工-编辑详情
export function editLockGetInfoById(param) {
    return axios(`${projectName}/api/staff/editLockGetInfoById`, param)
}

// 数字员工-暂存
export function digitalWorkerSave(param) {
    return axios(`${projectName}/api/staff/store`, param)
}

// 数字员工-删除
export function digitalWorkerDelete(param) {
    return axios(`${projectName}/api/staff/delete`, param)
}

// 数字员工-编辑锁定
export function editLock(param) {
    return axios(`${projectName}/api/staff/editLock`, param)
}

// 数字员工-编辑解除锁定
export function editRemoveLock(param) {
    return axios(`${projectName}/api/staff/editRemoveLock`, param)
}

// 数字员知识&工技能配置-配置List获取
export function getKnowledgeAndSkillsConfigList(param) {
    return axios(`${projectName}/api/staff/config/getConfigList`, param)
}

// 数字员工技能-分页查询
export function getCompetenceList(param) {
    return axios(`${projectName}/api/staff/competence/page`, param)
}

// 新增员工技能
export function saveCompetence(param) {
    return axios(`${projectName}/api/staff/competence/saveCompetence`, param)
}

// 修改员工技能
export function updateCompetence(param) {
    return axios(`${projectName}/api/staff/competence/updateCompetence`, param)
}

// 删除员工技能
export function deleteCompetence(param) {
    return axios(`${projectName}/api/staff/competence/delete`, param)
}

// 选择目标流程
export function queryUsedInfos(param) {
    return axios(`${projectName2}/api/workflow/info/queryUsedInfos`, param)
}

// 选择服务任务
export function queryInfos(param) {
    return axios(`${projectName2}/api/service/info/queryInfos`, param)
}

// 删除会话场景
export function removeInteractiveScene(param) {
    return axios(`${projectName}/api/staff/interactive/scene/remove`, param)
}

// 获取会话场景列表
export function getInteractiveSceneList(param) {
    return axios(`${projectName}/api/staff/interactive/scene/getInfo`, param)
}
