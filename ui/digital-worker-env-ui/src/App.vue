<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
    name: 'App',
    mounted() {
        // 默认修改title
        let title = this.$route.meta.title
        if (title) {
            document.title = title
        }
    }
}
</script>

<style lang="scss">
// 引入修改全局滚动条样式
@import "oss-common/css/customScrollBar.scss";
#app {
  margin: 0;
  padding: 0;
  background-color: #EBECF1;
}
</style>
