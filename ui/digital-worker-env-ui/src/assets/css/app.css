.el-button{
    user-select: unset;
    font-size: 14px;
}

.el-table{
    font-size: 14px;
}
.el-input__inner{
    height: 34px;
    line-height: 34px;
}

.el-loading-spinner {
    font-size: 40px;
    font-weight: bold;
}
.el-loading-mask .el-loading-spinner .el-loading-text {
    font-size: 18px;
}

.all-reset-font-css {
    font-size: 14px!important;
}
.all-reset-font-css .el-form-item__label,.el-input__inner,.el-textarea__inner,.el-select-dropdown__item,.el-table .cell,.el-radio__label,
/* .el-button, */
textarea,input,
.custom-tree-node,
.el-pagination__total,
.el-pagination__jump,
/* .el-tag, */
.el-message-box__content,
.el-table__empty-text,
.his-time,
.el-menu-item,
.el-submenu__title{
    font-size: 14px!important;
}

.el-tag--medium,.el-form-item__label{
    font-size: 14px!important;
}


.el-table th {
    height: 48px!important;
}

.el-table td {
    height: 48px!important;
}

.no-height-now .el-table td {
    height: 30px!important;
}

.no-height-now .el-table th {
    height: 30px!important;
}

.el-table th .cell {
    font-size: 16px!important;
}

.no-height-now .el-table th .cell {
    font-size: 15px!important;
}
.el-table__fixed-right .el-table__fixed-header-wrapper, .el-table__fixed-right .el-table__fixed-body-wrapper, .el-table__fixed-right .el-table__fixed-footer-wrapper {
    right: -1px;
}

.el-table th.is-sortable {
    padding-top: 9px!important;
}

