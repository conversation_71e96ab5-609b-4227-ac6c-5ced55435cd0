/* eslint-disable no-template-curly-in-string */
export default {
    'common': {
        'errorRow': '第{0}行',
        'addTitle': '新增{0}',
        'viewTitle': '查看{0}',
        'editTitle': '编辑{0}',
        'enableSuccess': '启用成功',
        'prohibitSuccess': '禁用成功',
        'dataHandle': '数据处理中...',
        'config': '配置',
        'required': '{0}必填',
        'exists': '{0}已经存在',
        'number': '{0}必须为数字',
        'integer': '{0}必须为整数',
        'positiveInteger': '{0}必须为正整数',
        'nonnegativeInteger': '{0}必须为非负整数',
        'largeThenMin': '{0}的最大值必须大于等于最小值',
        'isEquired': '必填',
        'invalid': '无效',
        'isExit': '已经存在',
        'isNumber': '只允许数字',
        'isDel': '该{0}已被关联，不得删除！',
        'enLetter': '英文字母、数字、下划线',
        'delDesc': '确认删除该{0}吗？',
        'startEndDesc': '{0}必须大于{1}',
        'batchDeleteTip': '请先勾选记录！',
        'selectObj': '先勾选记录',
        'configNet': '配置网元',
        'configEms': '配置EMS',
        'input': '输入',
        'output': '输出',
        'impSuccess': '导入成功',
        'impFailure': '导入失败',
        'excelFileLimit': '只能上传xls/xlsx格式文件',
        'excelSizeLimit': '上传excel大小不能超过',
        'excelError': '上传excel处理错误'
    },
    'datatypemgr': {
        'entityName': '数据类型',
        'relateDataError': '该数据类型已被关联，不得删除！',
        'duplicateName': '数据类型名称已经存在！',
        'legalFileSizeMinNumber': '合法文件大小最小值必须大于等于0，且小数位数最多保留3位',
        'legalFileSizeMaxNumber': '合法文件大小最大值必须大于等于0，且小数位数最多保留3位',
        'legalFileSizeMin': '合法文件大小最小值',
        'legalFileNumMin': '合法文件数最小值',
        'numberOfLegalRecordsMin': '合法文件记录数最小值',
        'legalFileSizeMax': '合法文件大小最大值',
        'legalFileNumMax': '合法文件数最大值',
        'numberOfLegalRecordsMax': '合法文件记录数最大值',
        'viewIndicatorItem': '查看指标项',
        'configGroup': '配置指标组',
        'addGroup': '添加指标组'
    },
    'datagroupmgr': {
        'entityName': '指标组',
        'indicatorItemName': '指标项',
        'duplicateName': '指标组名称已经存在！',
        'duplicateFieldName': '指标项名称已经存在！',
        'datafieldRequired': '指标项配置列表不能为空！',
        'relateDataError': '该指标组已被关联，不得删除！',
        'relateDataItemError': '该指标项已被关联，不得删除！'
    },
    'mergetypemgr': {
        'entityName': '合并指标类型',
        'mergeTypeName': '合并指标类型',
        'duplicateName': '合并指标类型名称已经存在！',
        'targetCount': '指标',
        'consolidatedIndicatorTypeName': '合并指标类型名称',
        'delgroup': '确认移除该指标组及其指标项吗？',
        'delgitem': '确认移除该指标项吗？',
        'datafieldRequired': '合并指标列表不能为空！'
    },
    'taskmodelmgr': {
        'entityName': '任务模型',
        'taskType': '任务类型',
        'taskModelName': '任务模型名称',
        'defaultAppId': '默认执行程序',
        'duplicateName': '任务模型名称已经存在！',
        'relateDataError': '该任务模型已被关联，不得删除！'
    },
    'taskmgr': {
        'allSpecialities': '所有专业',
        'operator': '操作人',
        'operateRecord': '操作记录',
        'entityName': '任务',
        'topNode': '任务管理',
        'periodType': '周期类型',
        'taskModelName': '任务模型名称',
        'defaultAppId': '默认执行程序',
        'isConcurrent': '执行方式',
        'isConcurrentDesc': '即当前一周期的任务未完成时，当前周期是否允许执行',
        'timeout': '超时时间',
        'second': '秒',
        'minute': '分',
        'hours': '时',
        'timeoutDesc': '0表示无限制',
        'periodConfig': '执行周期配置',
        'completeNotice': '结束通知',
        'completeNoticeInterface': '通知接口',
        'executeTask': '执行任务',
        'chooseTaskModel': '请先选择任务模型后再新增任务！',
        'triggerDesc': '确认立即执行该任务吗？',
        'triggerStartDesc': '确认启用该任务吗？',
        'triggerIsdDesc': '确认禁用该任务吗？',
        'existRunningTask': '该任务正在执行中，请确认！',
        'periodSecond': '执行周期的每隔秒数',
        'periodMinute': '执行周期的每隔分钟数',
        'periodHour': '执行周期的每隔小时数',
        'everyHour1': '执行周期的小时',
        'everyMinute1': '执行周期的分钟',
        'everyDay': '执行周期的星期',
        'everyDay2': '每天',
        'everyWeek': '每周',
        'everyMonth': '每月',
        'sun': '日',
        'everyHour2': '执行周期的小时',
        'everyMinute2': '执行周期的分钟',
        'everyDate': '执行周期的日',
        'everyHour3': '执行周期的小时',
        'everyMinute3': '执行周期的分钟',
        'duplicateName': '任务名称已经存在'
    },
    'emsmgr': {
        'entityName': 'EMS名称',
        'emsType': 'EMS类型',
        'editEms': '编辑EMS',
        'addEms': '新增EMS',
        'viewEms': '查看EMS',
        'emsNameRequired': 'EMS名称必填',
        'emsTypeRequired': 'EMS类型必填',
        'auasRequired': '编码必填',
        'manufacturerRequired': '厂商必填',
        'auasIsExit': '编码已经存在',
        'auasIsNumber': '编码只允许数字',
        'emsNameIsExit': 'EMS名称已经存在',
        'ipAddressRequired': 'IP地址无效',
        'editNet': '编辑网元',
        'addNet': '新增网元',
        'viewNet': '查看网元',
        'entityNameinvalid': 'EMS名称无效',
        'IpAddressrequired': 'IP地址必填'
    },
    'emginfomgr': {
        'downImportModule': '下载导入模板',
        'netSeries': '网元系列',
        'friendlyName': '友好名称',
        'office': '局向',
        'spareIpAddress': '备用IP地址',
        'officelength': '允许输入10个字符',
        'netNamerequired': '网元名称必填',
        'friendlyNamerequired': '友好名称必填',
        'spareIpAddressinvalid': '备用IP地址无效',
        'friendlyNameisExit': '友好名称已存在',
        'netNameisExit': '网元名称已存在',
        'connIsExitNotDel': '网元连接已存在，请在网元连接中先删除！'
    },
    'ipsegmentmgr': {
        'ipRange': 'IP范围',
        'addressSegment': '地址段',
        'showIpSegment': '查看IP网段',
        'editIpSegment': '编辑IP网段',
        'segmentName': '网段名称',
        'ipaddressSegment': 'IP地址段',
        'netMask': '掩码位',
        'delDesc': '确认删除该IP网段吗',
        'start': '起',
        'end': '止',
        'segmentNamerequired': '网段名称必填',
        'segmentNameinvalid': '网段名称无效',
        'arearequired': '区域必填',
        'ipaddressSegmentrequired': 'IP地址段必填',
        'netMaskrequired': '掩码位必填',
        'ipRangestartisEquired': 'IP范围起必填',
        'ipRangeendisEquired': 'IP范围止必填',
        'ipRangestartisinvalid': 'IP范围起无效',
        'ipRangeendisinvalid': 'IP范围止无效',
        'ipaddressSegmentinvalid': 'IP地址段无效',
        'segmentNameisExit': '网段名称已存在',
        'ipaddressSegmentisExit': 'IP地址段已存在'
    },
    'interfacetypemgr': {
        'connProtocol': '连接协议',
        'interfaceProtocol': '接口协议',
        'showInterfacetype': '查看接口类型',
        'editInterfacetype': '编辑接口类型',
        'interfacetypeName': '接口类型名称',
        'sessionComponent': '会话组件',
        'addConfig': '添加参数',
        'paramName': '参数名称',
        'paramFlag': '参数标识',
        'paramType': '参数类型',
        'isNull': '是否可为空',
        'editParma': '编辑参数',
        'showParma': '查看参数',
        'isMulSelect': '是否可多选',
        'separator': '分隔符',
        'paramSourceType': '参数来源类型',
        'paramSource': '参数来源',
        'relationObjType': '关联对象类型',
        'isPassSave': '是否加密保存',
        'defaultValue': '默认值',
        'paramformat': '参数格式',
        'maxLength': '最大长度',
        'paramformatDesc': '正则表达式,为空代表不作限制',
        'delConfigDesc': '确认删除该参数吗',
        'close': '关闭',
        'is_numeric': '是否数值',
        'is_multi_row': '是否多行',
        'inputType': '输入框类型'
    },
    'interfacemgr': {
        'interfacetype': '接口类型',
        'showInterface': '查看接口',
        'editInterface': '编辑接口',
        'interfaceName': '接口名称',
        'instructionSet': '指令集',
        'logOffCmd': '注销网元命令',
        'maximumConnection': '最大连接数',
        'timeOut': '连接超时时间',
        'emptyTimeOut': '连接空闲超时时间',
        'strCode': '字符编码',
        'connConfig': '连接配置',
        'paramValue': '参数值',
        'commParam': '常用参数',
        'selection': '选中',
        'notSpecify': '不指定',
        'specify': '指定',
        'registerNeCmd': '注册网元命令',
        'maxConnTip': '0表示无限制'
    },
    'connectmgr': {
        'interface': '接口',
        'showConnect': '查看连接',
        'editConnect': '修改连接',
        'connectName': '连接名称',
        'specifyTheNe': '是否指定网元',
        'neEms': '网元/EMS'
    },
    'shieldplanmgr': {
        'shieldplanName': '屏蔽计划名称',
        'shieldplanNum': '屏蔽网元数',
        'planName': '计划名称',
        'planStatus': '计划状态',
        'notHand': '未进行',
        'handing': '进行中',
        'theEnd': '已结束',
        'shieldTime': '屏蔽时间',
        'addNet': '添加网元',
        'importNet': '导入网元',
        'exportNet': '导出网元',
        'localNetwork': '所属本地网',
        'largeArea': '所属大区',
        'cutNumber': '割接单号',
        'shieldplanList': '屏蔽网元列表',
        'selectDesc': '请勾选网元记录！',
        'delDesc': '确认移除所选网元吗？',
        'shieldplan': '网元屏蔽计划',
        'shieldplanNamerequired': '屏蔽计划名称必填',
        'shieldplanNameisExit': '屏蔽计划名称已存在',
        'shieldTimestartrequired': '屏蔽计划开始时间必填',
        'shieldTimeendrequired': '屏蔽计划结束时间必填',
        'shieldplanEndStart': '屏蔽计划开始时间不能大于结束时间',
        'shieldplanDelDesc': '确认要删除屏蔽计划?'
    },
    'instructsetmgr': {
        'showInstructset': '查看指令集',
        'editInstructset': '编辑指令集',
        'instructsetName': '指令集名称',
        'instructSubmitter': '指令提交符',
        'commandPrompt': '指令提示符',
        'terminatInstruct': '终止指令符',
        'resultBlock': '指令结果块标识',
        'excEnd': '指令执行结束标识',
        'excNormal': '执行正常标识',
        'excExcept': '执行异常标识',
        'clearResultContent': '清除指令结果中内容',
        'timeOut': '超时时间',
        'second': '秒',
        'interactiveConfig': '交互配置',
        'interactiveInfo': '交互信息',
        'operinfo': '操作信息',
        'matchFlag': '匹配标识',
        'sendContent': '发送内容'
    },
    'instructmouldmgr': {
        'ordinary': '普通',
        'jurisdiction': '权限',
        'highRisk': '高危',
        'instructFalg': '指令标识',
        'addInstructmould': '添加指令模板',
        'instructmouldName': '指令模板名称',
        'safetyGrade': '安全等级',
        'classLabel': '分类标签',
        'analyticalTemplate': '解析模板',
        'instructmouldDesc': '指令模板描述',
        'whiteBill': '白名单',
        'blackBill': '黑名单',
        'networkInfo': '网元信息',
        'selectInstructmould': '请选择指令模板',
        'setupCmdBw': '黑白名单配置'
    },
    'instructmgr': {
        'instructmould': '指令模板',
        'showInstruct': '查看指令',
        'editInstruct': '编辑指令',
        'instructText': '指令文本',
        'paramSourceScript': '参数来源脚本',
        'paramSourceScriptLink': '参数来源数据库连接',
        'typeExcResult': '执行结果判断类型',
        'typeExcResultSql': '执行结果判断脚本',
        'typeExcResultSqlLink': '执行结果判断数据库连接',
        'retryCount': '重试次数',
        'second': '次',
        'times': '次数',
        'reInterval': '重连时间间隔',
        'millisecond': '毫秒',
        'isResultOutParam': '是否使用上一跳结果作为入参',
        'operConfig': '操作配置'
    },
    'analysismouldmgr': {
        'linkInstructSet': '指令集',
        'editAnalysismould': '编辑解析模板',
        'showAnalysismould': '查看解析模板',
        'analysismouldName': '解析模板名称',
        'relateDataError': '该解析模板已被关联，不得删除！',
        'analysisMethod': '解析方式',
        'groovySql': 'Groovy脚本',
        'startFlag': '解析开始标识',
        'endFlag': '解析结束标识',
        'analyticReg': '解析正则表达式',
        'analyticConfig': '解析配置'
    },
    'instructitemmgr': {
        'code': '虚指令项编码',
        'name': '虚指令项名称',
        'edit': '编辑虚指令项',
        'show': '查看虚指令项',
        'version': '版本号',
        'in': '中',
        'commonly': '一般',
        'reverseInstruct': '反向指令',
        'selectedInstruct': '待选指令',
        'editReverse': '反向指令配置',
        'showReverse': '反向指令查看',
        'reverseInstructName': '反向指令名称',
        'historyVersionList': '版本记录',
        'versionStatus': '版本状态',
        'inuseVersion': '在用版本',
        'historyVersion': '历史版本',
        'setInuseVersion': '设置为在用版本',
        'setInuseVersionTip': '确认将该版本设置为在用版本吗？',
        'parserScript': '解析脚本',
        'duplicateName': '虚指令项名称已经存在，不能重复！'
    },
    'virtualinstructmgr': {
        'code': '虚指令编码',
        'name': '虚指令名称',
        'edit': '编辑虚指令',
        'show': '查看虚指令',
        'itemList': '虚指令项列表',
        'shieldplanList': '屏蔽网元列表',
        'toBeSelectedInstructItem': '待选虚指令项',
        'virtualCmdType': '虚指令类型'
    },
    'instructsafemgr': {
        'selectNetElement': '网元列表不能为空',
        'selectInstructTempl': '指令列表不能为空'
    },
    'stdfield': {
        'viewTitle': '查看字段',
        'editTitle': '编辑字段',
        'fieldName': '字段名称',
        'fieldCode': '字段编码',
        'fieldType': '字段类型',
        'inputWay': '输入位置',
        'fieldLevel': '应用场景',
        'isAutoRltService': '是否添加到服务',
        'fieldDemo': '示例',
        'remark': '说明',
        'leftMenuTypeInfo': '类型信息',
        'leftMenuDemoInfo': '示例说明',
        'attrConfig': '属性配置',
        'attrName': '属性名称',
        'attrCode': '编码',
        'attrType': '类型',
        'addAttr': '添加属性',
        'fieldNameRequired': '字段名称必填',
        'fieldCodeRequired': '字段编码必填',
        'fieldCodePatternError': '字段编码只能输入英文、数字、下划线，只能英文开头',
        'fieldTypeRequired': '字段类型必填',
        'inputWayRequired': '输入位置必填',
        'fieldLevelRequired': '应用场景必填',
        'attrNameRequired': '属性名称必填',
        'attrCodeRequired': '属性编码必填',
        'attrCodePatternError': '属性编码只能输入英文、数字、下划线，只能英文开头',
        'attrTypeRequired': '属性类型必填',
        'delAttrDescWithChild': '确认移除该属性及其子属性吗？',
        'delAttrDescWithoutChild': '确认移除该属性吗？',
        'duplicateName': '字段编码已经存在！'
    },
    'microservice': {
        'cmptAlias': '微服务名称',
        'cmptName': '微服务编码',
        'microDescription': '微服务说明',
        'msAppServicesSum': 'API数量',
        'instNum': '运行实例数',
        'cmptCode': '服务编码',
        'cmptStatus': '服务状态',
        'cmptFlag': '服务标签',
        'cmptView': '微服务详情',
        'apiList': 'API列表',
        'runObj': '运行实例',
        'errorParam': '错误码参照',
        'openPort': '开放端口',
        'srcLink': '资源池',
        'mirrorImage': '镜像',
        'environmentVariable': '环境变量',
        'pathMapping': '路径映射',
        'readyProbe': '就绪探针',
        'apiName': 'API名称',
        'intfAddr': '接口地址',
        'retrunFmat': '返回格式',
        'postMentch': '请求方式',
        'instanceName': '实例名称',
        'host': '主机',
        'image': '镜像',
        'status': '状态',
        'readyRestart': '就绪/重启',
        'startTime': '启动时间',
        'errorCode': '错误码',
        'description': '说明',
        'apiView': 'Api详情',
        'microService': '所属微服务',
        'requestParameters': '请求参数',
        'returnResult': '返回结果',
        'objectExample': '对象示例',
        'returnToExample': '返回示例',
        'isRequired': '是否必填',
        'hostPath': '宿主机',
        'mountPath': '内'
    },
    'serviceaudit': {
        'attrTreeCodeMess': '只能输入字母或数字与字母组合、中间可以使用短横线、下划线',
        'viewAudit': '服务审核',
        'approve': '同意',
        'refuse': '拒绝',
        'apply': '申请',
        'audit': '审核',
        'chargeAudit': '处室负责人审核',
        'platAudit': '平台审核',
        'complete': '完成',
        'seq': '次',
        'dsh': '待审核',
        'ywc': '审核通过',
        'yjj': '已拒绝',
        'thisAudit': '本次审核',
        'auditRecord': '审核记录',
        'auditOpinion': '审核意见',
        'expireTime': '过期时间',
        'issueApply': '发布申请',
        'offlineApply': '下线申请',
        'callApply': '调用申请',
        'applyTime': '申请时间',
        'applyUser': '申请人',
        'auditOpinionRequired': '请选择审核意见',
        'expireTimeRequired': '请选择过期时间',
        'expireTimeLessNow': '过期时间不能小于当前时间',
        'auditFailureServiceMsg': '审核失败,服务状态上报失败'
    },
    'myService': {
        'servDesc': '服务说明',
        'myFavList': '我的收藏',
        'myApplyList': '我的调用',
        'myPubList': '我的发布',
        'applyPage': '服务调用申请',
        'pubPage': '服务发布申请',
        'applyRecord': '调用记录',
        'platAudit': '平台审核',
        'ypub': '已发布',
        'ystop': '已暂停',
        'yoffline': '已下线',
        'ycancel': '已注销',
        'djoint': '未联调',
        'yjoint': '已联调',
        'registered': '已注册',
        'confirmCancelCollectOrNot': '是否取消收藏？',
        'confirmCancelOrNot': '是否注销服务？',
        'duration': '持续时间',
        'invokestatus': '调用状态',
        'invoderesult': '调用结果',
        'heartabnormal': '心跳异常',
        'onlinesuccess': '上线申请提交成功,请等待审核完成',
        'jointoroffline': '只有已联调或下线的服务才允许上线',
        'offlineandcancel': '只有下线的服务才允许注销',
        'cancelsuccess': '注销成功',
        'suspendandoffline': '只有暂停的服务才允许下线',
        'offlinesuccess': '下线申请提交成功,请等待审核完成',
        'cancelService': '注销服务',
        'onlinecallapply': '只有上线的服务才允许调用申请',
        'senderDetail': '调用者详情',
        'senderCode': '调用者名称',
        'authType': '授权类型',
        'requestRateType': '访问频率限制粒度',
        'requestRateNum': '访问频率限制次数',
        'requestTraffType': '访问流量限制粒度',
        'requestTraffValue': '访问流量限制值(MB)',
        'ips': '访问IP数',
        'delIpDesc': '确认删除该IP地址吗',
        'dataShared': '数据共享',
        'serviceApply': '服务申请',
        'configParam': '调用者参数编辑',
        'senderUser': '调用者',
        'ipsNumError': '访问IP至少保留一个',
        'serviceImportMethod': '步骤1:选择服务导入方式',
        'serviceTextImport': '步骤2:输入服务文本',
        'serviceFileImport': '步骤2:选择文件',
        'serviceInterConfirm': '步骤3:确定接口',
        'serviceImport': '服务导入',
        'intfAddrRequired': '接口地址必填，结尾不能加/',
        'belongDomainRequired': '所属域必填',
        'belongSystemRequired': '所属系统必填',
        'serviceTextRequired': '输入服务文本不能为空!',
        'serviceTextError': '输入服务文本出错，请检查!',
        'serviceText': '文本导入',
        'serviceFile': '文件导入',
        'importNext': '下一步',
        'importPrev': '上一步',
        'importService': '导入',
        'chooseFile': '选择文件',
        'importError': '导入失败,服务名称重复:'
    },
    'servicedetails': {
        'serviceView': '服务详情',
        'serviceAdd': '服务添加',
        'serviceEdit': '服务修改',
        'serviceCopy': '服务复制',
        'testInfo': '测试信息',
        'paramDescription': '参数说明',
        'testAgain': '再次测试',
        'testSuccess': '测试成功',
        'testError': '测试失败',
        'dependentDomain': '所属域',
        'system': '所属系统',
        'labelName': '标签名称',
        'capabilityPriority': '能力优先级',
        'singleAccessMax': '单次访问流量阈值',
        'heartbeat': '心脏探测',
        'reqMethodName': '方法名称',
        'serviceDescription': '服务描述',
        'versionHis': '版本信息',
        'sendTime': '发布时间',
        'editContent': '修订内容',
        'encapsulated': '已封装',
        'composition': '组合',
        'image': '图片',
        'doNotAddepeatedly': '请勿重复添加',
        'favSucceeded': '收藏成功',
        'favCancelled': '收藏失败',
        'escfavSucceeded': '取消收藏成功',
        'escfavCancelled': '取消收藏失败',
        'favDel': '取消收藏',
        'topCenter': '编码规则:{总线标签}.{所属系统}.{服务名称的拼音首字母大写}_{方法名称}.{调用方式}',
        'inputCreateName': '请输入负责人',
        'message1': '值：支持JSONPath，${standard.input.#field#}表示引用标准服务的输入参数,#field#是标准字段的英文名称',
        'message2': '缓存的key最多支持两个，用竖线(|)分隔',
        'message3': '${#service_code#.input.JSONPath}是目标服务的输入参数，${#service_code#.output.JSONPath}是目标服务的输出结果',
        'reqHeadersRow': '请求头最多允许配置10个参数',
        'reqBodyRow': '请求参数最多允许配置10个参数',
        'respContentRow': '返回结果最多允许配置10个参数',
        'statusCodeRow': '状态码最多允许配置10个参数',
        'servNamerequired': '服务名称必填',
        'createdUserNamerequired': '负责人必填',
        'descriptionrequired': '服务描述必填',
        'apiUrlrequired': '服务URL必填',
        'reqHeadersCodeIsRequired': '请求头名称必填',
        'reqBodyCodeIsRequired': '请求参数名称必填',
        'respContentCodeIsRequired': '返回结果名称必填',
        'statusCodeNameIsRequired': '状态码必填',
        'reqHeadersLength': '请求头参数超出最大范围(0-2000)',
        'respContentLength': '返回结果参数超出最大范围(0-1000)',
        'statusCodeLength': '状态码参数超出最大范围(0-1000)',
        'reqBodyLength': '请求参数超出最大范围(0-2000)',
        'reqDemoLength': '请求示例超出最大范围(0-2000)',
        'respDemoLength': '返回示例超出最大范围(0-2000)',
        'testHeadersLength': '测试配置请求头超出最大范围(0-2000)',
        'testParamLength': '测试配置请求参数超出最大范围(0-2000)',
        'paramLength': '最多只能输入2000个字符',
        'submitTip': '提交成功',
        'submitErrorTip': '提交失败',
        'operationFailureMsg': '系统异常：未配置审核人，请联系管理员',
        'operationFailureMsg2': '服务参数解析失败',
        'addParam': '添加参数',
        'delParamDescWithChild': '确认移除该参数及其子参数吗？',
        'delParamDescWithoutChild': '确认移除该参数吗？',
        'operationFailureApply': '该服务正在申请审核，不允许操作？',
        'operationFailureVersion': '服务已存在相同的版本号，请确认？',
        'operationFailureKey': '无法根据ID找到对应的记录？',
        'showNewTag': '查看新标签信息',
        'checkNewTag': '检测到新标签',
        'tabsNewTag': '切换新标签信息',
        'tabsNewBelong': '切换新归属标签',
        'showNewBelong': '查看新归属标签',
        'servNameIsExit': '服务名已经存在',
        'serviceViewHis': '服务详情(历史版本)',
        'timeoutIsQ': '响应超时必填',
        'timeoutIsNumber': '响应超时只允许正整数',
        'pkgSizeLimitIsQ': '单次访问流量阈值必填',
        'pkgSizeLimitIsNumber': '单次访问流量阈值只允许正整数',
        'reqTraffValueIsQ': '流量限制(MB)必填',
        'reqTraffValueIsNumber': '流量限制(MB)只允许正整数',
        'reqRateNumIsQ': '频率限制(次)必填',
        'reqRateNumIsNumber': '频率限制(次)只允许正整数',
        'delError': '只允许删除已注册的服务',
        'pauseSucceeded': '暂停成功',
        'recoverySuccessful': '恢复成功',
        'reqMethodNameRequired': '方法名称必填',
        'stopServeNO': '只有暂停的服务才允许恢复',
        'onLineToStop': '只有上线的服务才允许暂停'
    },
    'servicemgr': {
        'servName': '服务名称',
        'servCode': '编码',
        'servStatus': '状态',
        'chooseRule': '选中规则',
        'packageService': '封装服务',
        'combineService': '组合服务',
        'selectGroupServiceTip': '必须选择两个或以上的已发布或暂停的服务，才能组合服务',
        'bePackagedService': '被封装服务',
        'currentServStatus': '当前服务状态',
        'packageServNme': '封装后名称',
        'groupServName': '组合服务名称',
        'origParamName': '原参数名称',
        'origParamType': '原参数类型',
        'paramMapIn': '参数映射',
        'respMapCheck': '结果验证',
        'oldServiceParamName': '原服务参数名称',
        'sourceType': '来源类型',
        'mapRelation': '映射关系',
        'sourceTypeFixed': '固定值',
        'sourceTypeCache': '缓存',
        'sourceTypeStand': '标准字段',
        'sourceTypeStdParam': '标准参数',
        'sourceTypePrev': '服务参数',
        'sourceTypeSpec': '指定属性',
        'sourceTypeOrigin': '原始输出',
        'standardFieldName': '标准字段输出',
        'standardFieldType': '标准字段类型',
        'addField': '添加字段',
        'validateSuccess': '验证成功！',
        'paramCodeRequired': '参数名称必填',
        'sourceTypeRequired': '来源类型必填',
        'valueExpPatternError': 'JSONPath的格式错误，请修改！',
        'valueExpRequired': '值必填!',
        'delFieldDescWithChild': '确认移除该字段及其子项吗？',
        'delFieldDescWithoutChild': '确认移除该字段吗？',
        'combineContent': '组合内容',
        'executeMethod': '执行方式',
        'executeMethodParallel': '并发',
        'executeMethodSequence': '顺序',
        'errorRowOfReqHeader': '请求头第{0}行',
        'errorRowOfReqHeaderMap': '请求头参数映射第{0}行',
        'errorRowOfReq': '请求参数第{0}行',
        'errorRowOfReqMap': '参数映射第{0}行',
        'errorRowOfResp': '返回结果第{0}行',
        'atleaseTwoService': '组合服务至少要两个服务',
        'layoutService': '编排服务',
        'layout': '编排',
        'layoutServiceName': '编排服务名称',
        'selectOneService': '请选择至少一个服务',
        'paramCodeDuplicate': '与第{0}行的参数名称重复',
        'resultCodeRequired': '标准字段输出必填',
        'resultCodeDuplicate': '与第{0}行的标准字段输出重复',
        'valueExpStandFieldError': '{0}在请求参数中不存在，请修改！',
        'valueExpNoServiceError': '服务编码不存在，请修改！',
        'valueExpNoFieldError': '{0}在指定的服务中不存在，请修改！',
        'layoutServiceConfig': '编排启动服务未配置',
        'getServCodeError': '获取服务编码失败',
        'reloadLayoutFlow': '重新加载',
        'layoutFlowError': '流程编排失败,不允许进行提交操作',
        'layoutFlowStart': '流程编排未启动,不允许进行提交操作',
        'startFlowParam': '启动流程参数',
        'applyTypeP': '发布审批中',
        'applyTypeO': '下线审批中',
        'applyTypeV': '调用审批中',
        'applyTypePError': '服务发布审批中,不允许修改',
        'applyTypeVError': '服务调用申请正在审核,是否重新申请'
    },
    'serviceMonitorLifeS': {
        'servCode': '服务编码',
        'servName': '服务说明',
        'zxjk': '在线监控',
        'beginTimes': '开始时间',
        'endTimes': '结束时间',
        'fwwg': '服务网关',
        'glzx': '管理中心',
        'jkzx': '监控中心',
        'tbfw': '同步服务',
        'smzq': '生命周期',
        'smzqzt': '状态',
        'smzqczsj': '操作时间',
        'zxjkZc': '注册',
        'zxjkFb': '发布',
        'zxjkZt': '正常',
        'zxjkHf': '恢复'
    },
    'nodemonitoring': {
        'status': '状态',
        'server': '服务器',
        'cpu': 'CPU',
        'disk': '硬盘',
        'mem': '内存',
        'partition': '分区',
        'freeSpace': '可用空间',
        'useRatio': '使用率',
        'total': '节点数',
        'normal': '正常',
        'abnormal': '异常',
        'viewPerformance': '查看性能',
        'performanceImage': '节点性能曲线',
        'sysname': '系统名称',
        'sysversion': '系统版本',
        'sysruntime': '系统运行时间（周）',
        'cpunum': 'CPU核数',
        'tcpConnNum': 'TCP连接数',
        'cpuperformance': 'CPU性能',
        'memUseRatio': '内存使用率',
        'totalCapacity': '总容量',
        'used': '已使用',
        'diskUseRatio': '硬盘使用率',
        'partitions': '硬盘分区',
        'fileSystem': '文件系统'
    },
    'callrecord': {
        'requestNo': '请求号',
        'servName': '服务',
        'servVersion': '版本',
        'reqUser': '调用方',
        'reqTime': '请求时间',
        'useTime': '用时(毫秒)',
        'isSuccess': '结果状态',
        'respContentLength': '返回字节数',
        'toBeCalled': '待调用',
        'startTime': '请求时间(起)',
        'endTime': '请求时间(止)',
        'viewCall': '调用详情',
        'reqUseTime': '请求用时(毫秒)',
        'returnCall': '转发记录',
        'returnParam': '返回参数',
        'reqUrl': '请求URL',
        'reqWay': '请求方法',
        'reqPakSize': '请求报文大小',
        'targetUrl': '目标地址',
        'transmitReqTime': '转发时间',
        'transmitReqHeaders': '转发请求头',
        'transmitReqBody': '转发请求内容',
        'transmitRespTime': '转发返回时间',
        'transmitRespHeaders': '转发返回头',
        'transmitRespContent': '转发返回内容',
        'respTime': '返回时间',
        'respCode': '返回代码',
        'respHeaders': '返回头',
        'failedType': '失败类型',
        'creator': '能力提供方',
        'servType': '能力类型',
        'respCodeType': '异常类型',
        'respContent': '返回内容'
    },
    'monitorCenter': {
        'requestNum': '调用次数',
        'lastWeek': '同比上周',
        'successtNum': '成功次数',
        'failNum': '失败次数',
        'calltatistics': '调用监控',
        'day': '日',
        'week': '周',
        'month': '月',
        'startDate': '开始日期',
        'endDate': '结束日期',
        'nodeMonitoring': '节点监控',
        'more': '更多',
        'busAvailabilityMonitoring': '总线可用性监控',
        'registrationStatistics': '注册量统计',
        'service': '服务',
        'abnormal': '异常',
        'individual': '个',
        'serviceStatus': '服务状态',
        'capabilityProvider': '能力提供方',
        'capabilityType': '能力类型',
        'callRelationship': '调用关系',
        'abilityRanking': '能力授权数排名',
        'userAuthorizationRanking': '用户授权数排名',
        'greenBarValue': '成功次数',
        'redBarValue': '失败次数',
        'totalnodes': '节点总数',
        'normal': '正常'
    },
    'operationanalysis': {
        'todayRequestNum': '今日调用次数',
        'lastWeek': '同比上周',
        'successtNum': '成功次数',
        'failNum': '失败次数',
        'nodenumber': '节点总数',
        'normal': '正常',
        'calltatistics': '调用统计',
        'caller': '调用方',
        'capabilityProvider': '能力提供方',
        'capabilityClassification': '能力分类',
        'startDate': '开始日期',
        'endDate': '结束日期',
        'transfer': '调用',
        'times': '次',
        'performanceAnalysis': '性能分析',
        'requests': '请求次数',
        'averageDuration': '平均时长',
        'caller1': '调用方1',
        'caller2': '调用方2',
        'caller3': '调用方3',
        'caller4': '调用方4',
        'caller5': '调用方5',
        'caller6': '调用方6',
        'other': '其他',
        'nodeMonitoring': '节点监控',
        'more': '更多',
        'choose': '请选择',
        'registrationStatistics': '注册量统计',
        'service': '服务',
        'abnormal': '异常',
        'individual': '个',
        'callRelationship': '调用关系',
        'abilityRanking': '能力授权数排名',
        'userAuthorizationRanking': '用户授权数排名',
        'anomalyAnalysis': '异常分析',
        'exceptionType': '异常类型',
        'serviceStatus': '服务状态',
        'capabilityType': '能力类型',
        'greenBarValue': '成功次数',
        'redBarValue': '失败次数',
        'packetize': '报文大小',
        'responsetime': '响应时长',
        'anomalyanalysis': '异常分析',
        'notFindServ': '查无此服务'
    },
    'servicemonitormgr': {
        'serviceNo': '服务编码',
        'updatedTime': '状态变更时间',
        'recoveryTime': '预计恢复时间',
        'suspended': '暂停中',
        'published': '已发布',
        'senderCode': '调用方',
        'authTime': '授权时间',
        'expiredTime': '过期时间'
    },
    'serviceMonitorIndex': {
        'zxkyxjk': '总线可用性监控',
        'fbfws': '发布服务数',
        'ztfws': '暂停服务数',
        'xxfws': '下线服务数',
        'sqfws': '授权服务数',
        'dygx': '调用关系',
        'fwmc': '服务名称',
        'dxsc': '掉线时长(h)',
        'zxsc': '在线时长(h)',
        'dxbl': '掉线比率',
        'xnjk': '性能监控',
        'qqqcss': '请求次数',
        'times': '(次)',
        'xysc': '响应时长',
        'zxfws': '在线服务数',
        'jdjk': '节点监控',
        'mores': '更多',
        'rzjk': '日志监控',
        'rzzs': '日志总数',
        'bwzrz': '不完整日志',
        'wzrz': '完整日志',
        'serviceMonitor': '调用监控',
        'ycfwpm': '异常服务排名',
        'ycyypm': '异常原因排名',
        'jdzs': '节点总数',
        'pjxysc': '平均响应时长',
        'ycfw': '异常服务',
        'bkjkfw': '不可监控服务',
        'zcfw': '正常服务',
        'fwzs': '服务总数',
        'yyc': '异常',
        'zzc': '正常',
        'bbk': '白框',
        'bwk': '外框',
        'nums': '条',
        'isDrop': '掉线',
        'serviceDetail': '服务信息',
        'authServiceDetail': '授权服务详情',
        'fwzxjk': '服务在线监控',
        'fwchsc': '服务存活时长',
        'yysj': '运营数据'
    },
    'businesstypemgr': {
        'flowCode': '流程编码',
        'updateTime': '更新日期',
        'description': '说明',
        'addBusinessType': '新增业务类型',
        'editBusinessType': '编辑业务类型',
        'showBusinessType': '查看业务类型',
        'delDesc': '确认删除当前记录?',
        'businessTypeName': '业务名称',
        'operSet': '操作信息',
        'operinfoIsNotNull': '操作信息列表不允许为空',
        'addOper': '添加操作',
        'operName': '操作名称',
        'operCode': '操作编码',
        'enableSuccess': '启用成功',
        'prohibitSuccess': '禁用成功',
        'businessTypeNameRequired': '业务名称必填',
        'businessTypeNameIsExit': '业务名称已存在',
        'nameRequired': '操作名称必填',
        'nameIsExit': '操作名称已存在',
        'notDelOpertar': '已关联虚指令编码，不可删除',
        'notDelname': '已关联业务场景，不可删除',
        'nameDelDesc': '确认删除当前操作信息?',
        'flowVersion': '流程版本',
        'flowToolTip': '流程信息配置提交失败，是否继续完善配置信息？'
    },
    'scenarioMgr': {
        'sceneName': '场景名称',
        'businessTypeName': '业务类型名称',
        'description': '描述',
        'enabled': '启用状态',
        'updateTime': '修改时间',
        'showScenario': '查看场景',
        'editScenario': '编辑场景',
        'bureauInfo': '局向信息',
        'itemList': '指令编排',
        'vendorName': '厂家',
        'netType': '网络类型',
        'neTypeName': '设备类型',
        'neName': '网元名称',
        'selectNe': '选择网元',
        'pleaSelectNe': '请选择网元！',
        'showCmd': '查看虚指令',
        'vCmdName': '虚指令名称',
        'vCmdCode': '虚指令编码',
        'coreNoFind': '专业编码CORE未找到',
        'romveCmd': '是否要移除所选的指令',
        'sceneNameRequired': '场景名称必填',
        'businessTypeIdRequired': '业务类型名称必填',
        'moveSuccess': '移动成功',
        'moveFailure': '移动失败',
        'netCode': '网元编码',
        'neNameisQ': '网元名称必填',
        'networkInfo': '网元信息'
    },
    'categorymanagementmgr': {
        'typeName': '类别名称',
        'typeNameIsExist': '该类别名称已经存在！',
        'typeCode': '类别编码',
        'newTypeDialog': '新增类别',
        'editTypeDialog': '编辑类别',
        'delTypeDialog': '删除类别',
        'delConfirm': '确认删除',
        'delConfirm2': '及其所有子节点？',
        'hasCantDelete': '有下挂参数类型，无法删除！',
        'parentTypeName': '父级类别',
        'TypeDesc': '类别说明',
        'typeNameRequire': '类别名称必填！',
        'exportParamType': '类别管理',
        'editParamType': '编辑类型',
        'showParamType': '查看类型',
        'addParamType': '新增类型'
    },
    'parameterMaintain': {
        'exportParamType': '参数管理',
        'enabled': '启用状态',
        'edit': '编辑',
        'add': '新增',
        'regionIdRequired': '区域必填',
        'netWordRequired': '网元必填',
        'hanndleError': '处理错误',
        'excelFileLimit': '只能上传xls/xlsx格式文件',
        'excelSizeLimit': '上传excel大小不能超过',
        'excelError': '上传excel处理错误',
        'excelSuccess': '上传成功',
        'importTemplateName': '参数管理导入',
        'exportTemplate': '模板导出'
    },
    'parameterManagement': {
        'serverDataNoNull': '服务数据出入参为空,请重新选择',
        'version': '版本号',
        'dataPercision': '精度',
        'serverDataRequired': '服务数据存在非法内容',
        'serverRequired': '数据来源必选',
        'paramTypeNameRequire': '参数类型名称为必填',
        'categoryParamRequire': '所属参数类别为必填',
        'fieldNameRequire': '字段名称为必填',
        'paramTypeCodingRequire': '字段编码为必填',
        'dataTypeRequire': '字段类型为必填',
        'dataLengthRequire': '数据长度为必填',
        'paramFieldContentRequire': '参数字段备注为必填',
        'controlTypesRequire': '控件类型为必填',
        'paramTypeName': '参数类型名称',
        'categoryName': '所属类别',
        'granularity': '粒度',
        'createUserName': '创建人',
        'createTime': '创建日期',
        'updateUserName': '更新人',
        'enabled': '启用状态',
        'fieldManagement': '字段管理',
        'categoryParam': '所属参数类别',
        'paramTypeCoding': '字段编码',
        'paramDataGranularity': '参数数据粒度',
        'fieldName': '字段名称',
        'fieldEncoding': '字段编码',
        'controlTypes': '控件类型',
        'isShow': '是否展示',
        'addField': '添加字段',
        'editField': '编辑字段',
        'viewField': '查看字段',
        'dataLength': '数据长度',
        'dataType': '数据类型',
        'templateField': '是否为导入列',
        'templateSearch': '是否为查询条件',
        'inputParameter': '输入参数',
        'outputParameter': '输出参数',
        'isRequired': '是否必填',
        'sourceType': '来源类型',
        'targetType': '输出目标',
        'selectServe': '选择服务',
        'exportParamType': '类型管理',
        'templateChange': '是否变更参数字段',
        'isFieldCRepeat': '新增参数字段名称已经存在',
        'onChange': '不允许变更参数字段',
        'ptCode': '参数类型编码',
        'pFCode': '参数字段编码',
        'ptCodeRequire': '参数类型编码为必填'
    },
    'uamoitor': {
        'normalService': '服务监控',
        'totalServiceNum': '服务总数',
        'normalServiceNum': '正常服务数',
        'errorServiceNum': '异常服务数',
        'normalNode': '节点监控',
        'totalNodeNum': '节点总数',
        'normalNodeNum': '正常节点数',
        'errorNodeNum': '异常节点数',
        'normalCall': '调用监控',
        'totalCallNum': '调用总次数',
        'normalCallNum': '正常次数',
        'errorCallNum': '异常次数',
        'error': '异常',
        'normal': '正常',
        'taskNum': '任务监控',
        'totalTaskNum': '任务总数',
        'normalTaskNum': '正常任务数',
        'errorTaskNum': '异常任务数',
        'endTime': '结束日期',
        'startTime': '开始日期',
        'totalExeCmd': '指令监控',
        'totalExeCmdNum': '执行指令总数',
        'totalExeDangerCmdNum': '高危指令总数',
        'exeSuccessCmdNum': '成功数',
        'exeFailCmdNum': '失败数',
        'exeFailDangerCmdNum': '失败高危指令',
        'componentMonitoring': '组件监控',
        'database': '数据库',
        'fileServices': '文件服务',
        'messageMiddleware': '消息中间件',
        'componentOperation': '组件运行情况',
        'caching': '缓存',
        'dataQuality': '采集数据质量监控',
        'taskTimeRate': '任务及时性',
        'dataCompleteRate': '数据完整性',
        'dataVolatility': '数据波动性',
        'warn': '预警监控',
        'criticaAlarmlNum': '紧急',
        'majorAlarmNum': '重要',
        'minorAlarmNum': '次要',
        'warningAlarmNum': '告警',
        'select': '请选择',
        'network': '网元监控',
        'totalNeNum': '网元总数',
        'connNeRate': '网元接通率',
        'errorNeNum': '异常网元数',
        'normalNeNum': '正常数',
        'connNeRate1': '接通率',
        'errorNeNum1': '异常数'
    },
    'nodata': '暂无数据',
    'add': '新增',
    'delete': '删除',
    'modify': '修改',
    'collectionTask': '采集任务',
    'fileCollection': '文件采集',
    'alarmCollection': '告警采集',
    'taskClassification': '任务分类',
    'specialty': '所属专业',
    'batchNumber': '批次号',
    'cycleTime': '周期时间',
    'startTime': '开始时间',
    'endTime': '结束时间',
    'servingObject': '发起对象',
    'implementationSituation': '执行情况',
    'searchPlaceholder': '请输入关键字',
    'taskDescription': '任务描述',
    'taskInstance': '任务实例',
    'taskLog': '任务日志',
    'taskName': '任务名称',
    'executionState': '执行状态',
    'major': '专业',
    'taskModel': '任务模型 ',
    'processTaskOrNot': '是否流程任务',
    'sponsorObject': '发起对象 ',
    'describe': '描述',
    'businessTargetMonitoringDetails': '业务目标监控详情',
    'dataTypeName': '数据类型名称',
    'manufacturer': '厂商',
    'status': '状态',
    'numberOfCollectedFiles': '采集文件数',
    'totalCollectionFileSize': '采集文件总大小',
    'acquisitionTime': '采集用时',
    'analysisNumberOfFiles': '解析文件数',
    'analysisTime': '解析用时',
    'mergeNumberOfFiles': '合并文件数',
    'mergeSizeOfFiles': '合并文件大小',
    'mergeTime': '合并用时',
    'fileAnalysis': '文件解析',
    'fileMerge': '文件合并',
    'linkState': '环节状态',
    'executed': '已执行 ',
    'inExecution': '执行中',
    'unexecuted': '未执行',
    'successfulImplementation': '执行成功',
    'executionFailure': '执行失败',
    'fileCollectionDetail': '文件采集明细',
    'connectId': '连接id',
    'sourceAddressIP': '源地址ip',
    'sourceFilePath': '源文件路径',
    'destinationIpAddress': '目标ip地址',
    'destinationFilePath': '目标文件路径',
    'fileSize': '文件大小',
    'synchronizationStartTime': '同步开始时间',
    'synchronizationEndTime': '同步结束时间',
    'synchronizationSuccessfulOrNot': '是否同步成功',
    'area': '所属区域',
    'netType': '网元类型',
    'netModel': '网元型号',
    'netName': '网元名称',
    'IpAddress': 'Ip地址',
    'hdVersion': '硬件版本',
    'sncName': 'SNC名称',
    'aPortRegion': 'A端区域',
    'aPortEquip': 'A端设备',
    'hierarchy': '层级',
    'zPortRegion': 'Z端区域',
    'zPortEquip': 'Z端设备',
    'aPortSite': 'A端站点',
    'zPortSite': 'Z端站点',
    'aPort': 'A端端口',
    'zPort': 'Z端端口',
    'occupyRate': '占用率',
    'circuitName': '电路名称',
    'circuitNumber': '电路编号',
    'circuitType': '电路类型',
    'customer': '所属客户',
    'netLevel': '网络层次',
    'rate': '速率',
    'type': '类型',
    'cableNumber': '编号',
    'businessState': '业务状态',
    'name': '名称',
    'query': '查询',
    'reset': '重置',
    'highSearch': '高级搜索',
    'export': '导出',
    'import': '导入',
    'choosePlaceholder': '请选择',
    'edit': '编辑',
    'more': '更多',
    'copy': '复制',
    'copyTip': '复制成功',
    'attribute': '属性',
    'enable': '启用',
    'disable': '禁用',
    'cancel': '取消',
    'config': '确认',
    'sava': '保存',
    'cut': '剪切',
    'cutTip': '剪切成功',
    'paste': '粘贴',
    'pasteTip': '粘贴成功',
    'up': '上移',
    'validation': '验证',
    'yes': '是',
    'no': '否',
    'article': '条',
    'alarm': '告警',
    'criticalBoardCardFailure': '关键板卡故障',
    'cableInterruptionFault': '光缆中断故障',
    'monitorSignalInterruption': '监控信号中断',
    'alarmNum': '告警数',
    'network': '网元',
    'circuit': '电路',
    'fiberOpticCable': '光缆',
    'resources': '资源',
    'networkNumber': '网元数',
    'channelNumber': '通道数',
    'circuitNo': '电路数',
    'cableNo': '光缆数',
    'SubmarineCableNo': '海缆数',
    'perIns': '性能巡检',
    'health': '健康度',
    'abINs': '巡检异常',
    'abCir': '电路异常',
    'abNet': '网元异常',
    'insNum': '巡检总数',
    'netIns': '网元巡检',
    'open': '开通',
    'adjust': '调整',
    'demolition': '拆除',
    'cutOver': '割接',
    'abnormal': '异常',
    'cutOverWait': '待割接',
    'cutOvering': '割接中',
    'cutOvered': '已完成',
    'netCutOver': '网元割接',
    'circuitCutOver': '电路割接',
    'FOCcutOver': '光缆割接',
    'NOSCcutOver': '海缆割接',
    'dataUser': '总计',
    'alarmFault': '告警故障工单',
    'timeOutThan': '超时比',
    'overWorkOrder': '超时工单',
    'overAlarm': '超时预警工单',
    'onWork': '在途工单',
    'hangWork': '挂起工单',
    'contentPlaceholder': '请输入内容',
    'length': '长度',
    'coverageSpotPre': '预测覆盖点',
    'signIn': '签到',
    'down': '下移',
    'viewTopo': '查看拓扑',
    'linkName': '环节名称',
    'executionProcedure': '执行程序',
    'clickSearch': '点此搜索',
    'keyIndex': '关键指标',
    'wireless': '4G无线连接成功率',
    'userExperience': '用户体验下行平均速率',
    'ERab': 'E-RAB利用率',
    'Prb': '下行PRB利用率',
    'Gnb': '终端在gNB的E-RAB利用率',
    'inspection': '巡检正常率',
    'remote': '远程验收通过数',
    'mobile': '移动业务',
    'volteVoice': 'Volte语音时长',
    'fiveTraffic': '5G流量',
    'fourTraffic': '4G流量',
    'volteUser': 'Volte开户用户数',
    'volteRegistered': 'Volte开户注册数',
    'fourUser': '4G耳机如用户数',
    'unit': '万人',
    'netSize': '网络规模',
    'LteNum': 'LTE数量',
    'fiveNum': '5G数量',
    'addressNum': '站址数量',
    'netFault': '网络故障',
    'LieAlarm': 'LTE告警数',
    'fiveAlarm': '5G告警数',
    'alarmFailureOrders': '告警故障单数量',
    'netProfile': '网络概况',
    'fiveBusy': '5G超忙小区',
    'fourBusy': '4G超忙小区',
    'regionName': '区域名称',
    'dataTraffic': '数据流量(GB)',
    'dataUserNum': '数据用户数(万人)',
    'applicationDesc': '根据全省规则配置管理办法，变更操作须通过【服保系统】发起申请、报备。（功能入口：专项管理-告警变更管理-告警变更管理（规则）-变更申请）',
    'delDesc': '是否要删除所选对象',
    'enableDesc': '是否启用所选对象',
    'disableDesc': '是否禁用所选对象',
    'saveDesc': '取消将不保存当前所编辑内容，确定取消',
    'setting': '高级设置',
    'tooltip': '提示',
    'application': '操作申请',
    'note': '备注',
    'applyOrder': '变更申请号',
    'editDesc': '请选择一条规则进行编辑',
    'copyDesc': '请选择一条规则进行复制',
    'viewDesc': '请选择一条规则进行查看',
    'enableTip': '请选择要启用的规则',
    'disableTip': '请选择要禁用的规则',
    'requestInterfaceException': '请求接口异常',
    'applicationTip': '请先填写变更申请号',
    'noApplication': '变更申请无效',
    'missApplication': '变更申请缺失',
    'isDel': '请选择要删除的规则',
    'delTip': '删除成功',
    'successTip': '操作成功',
    'ruleNametip': '请填写规则名称,建议格式:区域(AH)-专业(LTE)-设备类型(BBU)-规则名',
    'ruleDescTip': '请输入规则描述信息',
    'remarkDescTip': '请输入备注信息',
    'moduleName': '模块类型',
    'currentUsername': '操作账号',
    'operType': '操作类型',
    'operTime': '操作时间',
    'operContent': '操作内容',
    'hostIp': '主机IP',
    'base': '基本信息',
    'rootConfig': '根告警配置',
    'ruleConfig': '规则表达配置',
    'operationLog': '操作日志',
    'rootAlarmDesc': '规则配置里未设置根告警',
    'checkTip': '检查通过',
    'saveTip': '保存成功',
    'saveRule': '保存基本信息成功,保存规则失败',
    'configuration': '未配置条件项，无法生成有效表达式，是否继续保存？',
    'addEntity': '添加实体',
    'editEntity': '编辑实体',
    'entityDesc': '请先选择实体',
    'entityNameDesc': '请输入实体名称',
    'setAlarm': '设置根告警',
    'addSonterm': '添加子条件',
    'editSonterm': '编辑子条件',
    'sonTermDesc': '请选择一个子条件',
    'sonTermNameDesc': '请输入子条件名称',
    'addTerm': '添加条件项',
    'editTerm': '编辑条件项',
    'entityOrSonDesc': '请选择一个设备类型或者子条件',
    'termDesc': '请先选择条件项',
    'rootAlarm': '根告警',
    'operator': '操作符',
    'value': '值',
    'entityName': '实体名称',
    'sonTermName': '子条件名称',
    'timeFormat': '时间格式',
    'dataTime': '日期时间',
    'data': '日期',
    'time': '时间',
    'dataTimeDesc': '选择日期时间',
    'dataDesc': '选择日期',
    'timeDesc': '选择时间',
    'start': '开始',
    'end': '结束',
    'internalLogic': '内部逻辑',
    'internalLogicDesc': '输入结果为多行，请选择内部关系',
    'isInternalLogic': '验证不通过,请选择操作符',
    'isValue': '验证不通过，值错误',
    'between': '介于',
    'noBetween': '不介于',
    'upDesc': '请先选择一项进行上移操作',
    'upWarning': '不能再往上移动',
    'downDesc': '请先选择一项进行下移操作',
    'downWarning': '不能再往下移动',
    'chooseItem': '请选择一条数据',
    'chooseSon': '请先选择子条件或者条件项',
    'copyOrCut': '请先复制或剪切一项子条件或条件项',
    'sonOrEntity': '请选择一个子条件或者实体',
    'chooseAttribute': '请选择一个属性',
    'firstRelation': '首个不能设置外部关系',
    'termAddSon': '条件项无法添加子条件',
    'equal': '等于',
    'noEqual': '不等于',
    'hintAlertStr': '由于您选择“',
    'hintAlertStr1': '”，且值的内部逻辑判断是',
    'hintAlertStr2': '，此时逻辑为：',
    'hintAlertStr3': '，此时，则判断无效，添加此条件项也无效果，建议重新修改!逻辑为：',
    'inputValue': '请输入一个值',
    'ruleName': '规则名称',
    'netTopo': '网络拓扑场景',
    'alarmType': '根告警关联类型',
    'founder': '创建人',
    'founderTime': '创建时间',
    'update': '更新人',
    'updateTime': '更新时间',
    'entityNum': '实体个数',
    'lowRuleNum': '最低需要满足实体数',
    'waitTime': '关联等待时间(分)',
    'isOrderRule': '顺序匹配告警规则',
    'isConvergence': '收敛关联告警',
    'rootMoreTickets': '根告警并派网格',
    'relatedTicketType': '衍生告警派单类型',
    'cableAnalysis': '衍生告警是否参与光缆计算',
    'isChildSms': '衍生告警是否发送短信',
    'ruleDesc': '规则描述',
    'remark': '备注信息',
    'relatedType': '根告警关联类型',
    'perceivedSeverity': '根告警等级',
    'rootAlarmIdentify': '根告警告警标识',
    'deriveAlarmfiled': '提取到根告警字段',
    'rootEditType': '根告警字段定义',
    'rootClearType': '根告警恢复模式(由子恢复根)',
    'rootTermType': '根告警终止模式',
    'childTermType': '子告警终止模式',
    'noConfig': '尚未进行配置',
    'click': '请点击',
    'addAssType': '新增关联规则',
    'editAssType': '编辑关联规则',
    'copyAssType': '复制关联规则',
    'viewAssType': '查看关联规则',
    'first': '第',
    'rootAlarmFiledTip': '行，根告警字段定义的所有列皆不能为空',
    'isRuleName': '请填写规则名称',
    'isRegionId': '请选择所属区域',
    'isSpeciality': '请选择所属专业',
    'isAlarmNum': '请填写必须告警条数',
    'isWaitTime': '请填写关联等待时间',
    'isRelatedType': '请选择根告警关联类型',
    'isRootAlarmIdentifyOne': '若告警关联类型【生成根告警】，则【根告警告警标识】必填',
    'isRootAlarmIdentifyTwo': '若告警关联类型【生成根告警】，则【提取到根告警字段】必填',
    'isLowRuleNum': '最低满足实体数应大于0',
    'isEntityNum': '已配置实体个数[规则配置]不能大于实体个数[基本信息]',
    'isAlarmNumTip': '必须告警条数不能小于1',
    'isWaitTimeTip': '关联等待时间应小于9999',
    'mustAlarmNumber': '必须告警条数',
    'resultsOfEnforcement': '执行结果',
    'isDelName': '是否删除',
    'addFilter': '新增过滤规则',
    'editFilter': '编辑过滤规则',
    'copyFilter': '复制过滤规则',
    'viewFilter': '查看过滤规则',
    'executiveDetails': '执行明细',
    'managerName': '规则管理员',
    'isRepeateDesc': '是否多次通知',
    'interval': '间隔时间(分钟)',
    'phoneNumList': '指定号码',
    'phoneNumListDesc': '多个手机号请用;分隔',
    'isImmediateSend': '是否立即派发',
    'sendStartTime': '告警发送开始时间',
    'sendEndTime': '告警发送结束时间',
    'delayTime': '延迟时间',
    'messageModel': '短信定制',
    'messageAddContent': '短信附加内容',
    'isSendToPhoneNum': '是否直接发往指定号码',
    'isSendDefault': '匹配不到接收人是否发往默认号码',
    'keyName': '接收人匹配字段',
    'addAutoMsg': '新增自动短信规则',
    'editAutoMsg': '编辑自动短信规则',
    'copyAutoMsg': '复制自动短信规则',
    'viewAutoMsg': '查看自动短信规则',
    'isIsImmediate': '请选择是否立即派发',
    'isDelayTime': '请填写延迟时间(分钟)',
    'isSendStartTime': '请选择告警发送开始时间',
    'isSendEndTime': '请选择告警发送结束时间',
    'sendTimeDesc': '告警发送结束时间不能小于告警发送开始时间',
    'isMessageModel': '请选择短信定制内容',
    'isIsRepeate': '请选择是否多次通知',
    'isIsRepeateDesc': '若多次通知，则间隔时间必填，并且必须大于0',
    'isIsSendToPhoneNum': '请选择是否直接发往指定号码',
    'iskeyName': '请选择接收人匹配字段',
    'isIsSendDefault': '请选择匹配不到接收人是否发往默认号码',
    'isPhoneNum': '请输入指定号码',
    'invalidNum': '无效号码：',
    'refill': ',请重填',
    'isPhoneNumDesc': '指定号码无效，请重新输入',
    'addTicket': '新增自动派单规则',
    'editTicket': '编辑自动派单规则',
    'copyTicket': '复制自动派单规则',
    'viewTicket': '查看自动派单规则',
    'isBtypeDltIdObj': '请选择具体业务',
    'isBtypeMstIdObj': '请选择业务类型',
    'isbfaultTypeIdObj': '请选择申告项目',
    'isfaultphenoid': '请选择故障现象',
    'isalarmseverity': '请选择故障等级',
    'isentityNum': '请选择实体个数',
    'istktsbpflag': '请选择受单对象',
    'btypeMstIdObj': '业务类型',
    'btypeDltIdObj': '具体业务',
    'bfaultTypeIdObj': '申告项目',
    'faultPhenoId': '故障现象',
    'alarmSeverity': '故障等级',
    'isImport': '是否重保规则',
    'isIsImport': '请选择是否重保规则',
    'isAdditional': '是否加派',
    'isDifMntlevel': '是否区分维护等级',
    'isIsDifMntlevel': '请选择是否区分维护等级',
    'isDifMntlevelDesc': '不区分维护等级时，请填写障碍时限和处理时限',
    'isDifMntlevelDescTwo': '不区分维护等级时，有且仅能配置一条派单时间',
    'haveDifMntlevel': '维护等级已经存在，请重新选择维护等级',
    'isImmediate': '立即派单',
    'isdelayTime': '请填写延迟时间(>=0)',
    'tktBeginTime': '派单开始时间',
    'istktBeginTime': '请选择派单开始时间',
    'tktEndTime': '派单结束时间',
    'istktEndTime': '请选择派单结束时间',
    'tktsbpflag': '受单对象',
    'keyfiledname': '区域匹配字段',
    'iskeyfiledname': '请选择区域匹配字段',
    'tktsbnflag': '默认派单',
    'istktsbnflag': '请选择是否默认派单',
    'isRootTicket': '有衍生告警才派单',
    'isIsRootTicket': '请选择是否有衍生告警才派单',
    'isRootImmediate': '有衍生告警立即派单',
    'isIsRootImmediate': '请选择是否有衍生告警立即派单',
    'isAddFault': '是否加派',
    'isIsAddFault': '请选择是否加派',
    'faultlimit': '障碍时限(分)',
    'processlimit': '处理时限(分)',
    'maintenanceLevelDesc': '维护等级',
    'isMaintenanceLevel': '请选择维护等级',
    'isEffectBusinessDesc': '是否影响业务',
    'isIsEffectBusiness': '请选择是否影响业务',
    'isImmediateDesc': '是否立即派单',
    'isIsImmediateDesc': '请选择是否立即派单',
    'isRegionName': '请选择区域',
    'haveRegionName': '区域已经存在，请重新选择区域',
    'suggestion': '预处理意见',
    'isSuggestion': '请选择预处理意见',
    'problemCount': '告警累计次数',
    'isproblemCount': '请填写告警累计次数(>=0)',
    'addSendTime': '添加派单时间配置',
    'editSendTime': '修改派单时间配置',
    'haveSendTime': '请选择一条派单时间配置进行编辑',
    'delSendTime': '请选择要删除的派单时间配置',
    'addSuggestion': '添加预处理意见',
    'editSuggestion': '修改预处理意见',
    'haveSuggestion': '请选择一条预处理意见进行编辑',
    'delSuggestion': '请选择要删除预处理意见',
    'sendTimeConfig': '派单时间配置',
    'isSendTimelist': '派单时间配置不能为空',
    'checkTimeBasicInfo': '派单时间配置，第【',
    'checkTimeBasicInfoTwo': '】条,',
    'alarmStatistics': '告警统计',
    'untreated': '未处理',
    'NotClear': '未清除',
    'NotSingle': '未派单',
    'alarmList': '告警列表',
    'batchOperation': '批量操作',
    'batchConfirmation': '批量确认',
    'terminateBatch': '批量终止',
    'batchComments': '批量注释',
    'region': '区域',
    'labelState': '标签状态',
    'alarmTime': '告警时间',
    'removeTime': '清除时间',
    'deviceName': '设备名称',
    'IPAddress': 'IP地址',
    'alarmName': '告警名称',
    'alarmLogo': '告警标识',
    'locationInfo': '定位信息',
    'keyFields': '关键字段',
    'fault': '故障',
    'similar': '相似',
    'after': '历时',
    'operation': '操作',
    'day': '天',
    'hours': '小时',
    'points': '分',
    'detailedInfo': '详细信息',
    'terminationOf': '终止',
    'sendSingle': '派单',
    'payAttentionField': '关注字段',
    'basicInformation': '基础信息',
    'extendedInfo': '扩展信息',
    'alarmLable': '告警标签',
    'networkLable': '网元标签',
    'originalAcquisition': '原始采集',
    'alarmLevel': '告警等级',
    'serious': '严重',
    'important': '重要',
    'secondary': '次要',
    'warning': '警告',
    'remove': '清除',
    'noSure': '不确定',
    'unconfirmed': '未确认',
    'sentSingled': '已派单',
    'belongsType': '所属机型',
    'alarmID': '告警ID',
    'segmentationTip': '多值请用半角分号(;)分割',
    'workOrderID': '工单ID',
    'alarmStatus': '告警状态',
    'isSendSingle': '是否派单',
    'isClear': '是否清除',
    'isCutOver': '是否割接',
    'startDate': '开始日期',
    'endData': '结束日期',
    'to': '至',
    'historyQuery': '历史查询',
    'emptyHistory': '清空历史',
    'emptyCondition': '清空条件',
    'annotationPlaceholder': '请输入注释',
    'determine': '确定',
    'correlation': '关联关系',
    'pathCode': '光路编码',
    'originalAlarm': '原始告警',
    'similarAlarm': '相似告警',
    'rulesMatch': '规则匹配',
    'moreOperations': '更多操作',
    'menu': '菜单',
    'OperatingLable': '操作标签',
    'alarmCollector': '告警收集器',
    'sonMajor': '子专业',
    'deviceType': '设备类型',
    'alarmRank': '告警级别',
    'singleState': '派单状态',
    'failureFrequency': '故障次数',
    'similarAlarmNumber': '相似告警数',
    'InitialAlarmTime': '初次告警时间',
    'latestAlarmTime': '最新告警时间',
    'cutOverIdentity': '割接标识',
    'clearlogo': '清除标识',
    'endUser': '终止用户',
    'terminationTime': '终止时间',
    'finalFailureTime': '最后故障时间',
    'confirmUser': '确认用户',
    'confirmTime': '确认时间',
    'alarmObject': '告警对象',
    'operatingAnnotation': '操作注释',
    'originalWarningName': '原始告警名称',
    'originalWarningLabel': '原始告警标识',
    'boardPosition': '板卡定位',
    'portLocation': '端口定位',
    'additionalFieldOne': '附加字段1',
    'additionalFieldTwo': '附加字段2',
    'additionalFieldThree': '附加字段3',
    'additionalFieldFour': '附加字段4',
    'additionalFieldFive': '附加字段5',
    'alarmDescription': '告警描述',
    'refresh': '刷新',
    'view': '查看',
    'regularClassName': '规则分类名',
    'isMatchRule': '是否匹配规则',
    'matchRuleID': '匹配规则ID',
    'matchRuleName': '匹配规则名称',
    'isEmpty': '不能为空',
    'periodicRecording': '周期记录',
    'dataTypeManagement': '数据类型管理',
    'indexGroupManagement': '指标组管理',
    'consolidatedIndicatorTypeManagement': '合并指标类型管理 ',
    'businessObjectiveManagement': '业务目标管理',
    'cirIns': '电路巡检',
    'dataClassification': '数据分类',
    'dataType': '数据类型',
    'basicProcess': '基本流程',
    'businessObjectives': '业务目标',
    'resultConnectionInterface': '结果连接接口',
    'indexGroupName': '指标组名称',
    'targetGroupName': '目标组名称',
    'cycle': '周期',
    'minute': '分钟',
    'cycleDelayTime': '周期延时时间',
    'consolidatedIndicatorTypeName': '合并指标类型名称',
    'legalFileSize': '合法文件大小',
    'legalFileNum': '合法文件数',
    'numberOfLegalRecords': '合法文件记录数',
    'cycleTimeExtractionExpression': '周期时间提取表达式',
    'fileTimeFormatExpression': '文件时间格式化表达式',
    'submit': '提交',
    'regularExpression': '正则表达式',
    'indicatorItemConfiguration': '指标项配置',
    'indexItemName': '指标项名称',
    'columnName': '列名称',
    'columnOrder': '列顺序',
    'fieldType': '字段类型',
    'addIndexItem': '添加指标项',
    'batchDelete': '批量删除',
    'mergerIndex': '合并指标',
    'mergeTypeName': '合并类型名称',
    'indexGroupToBeSelected': '待选指标组',
    'orderNumber': '序号',
    'indexGroupOrName': '指标组/指标项名称',
    'indexNumberOrType': '指标数/合并项类型',
    'submarineCable': '海缆',
    'notTimeOutThan': '未超时比',
    'netVersion': '网元版本',
    'criticalAlarmTotal': '严重告警的总数',
    'majorAlarmTotal': '重要告警的总数',
    'activityAlarm': '活动告警',
    'queryCondition': '查询条件',
    'alarmConfirmRepeatTooltip': '告警已确认，无需重复确认操作！',
    'alarmTerminationRepeatTooltip': '告警已终止，无法继续确认告警！',
    'operationFailure': '操作失败',
    'equipmentBoardDiagram': '设备板卡图',
    'alarmInfo': '告警信息',
    'alarmTotal': '告警总数',
    'level': '等级',
    'workNumber': '工单号',
    'numberOfPatrolPorts': '巡检端口数',
    'patrolInformation': '巡检信息',
    'numberOfPatrolExceptionPorts': '巡检异常端口数',
    'jobName': '作业名称',
    'entryIntoForceTime': '生效时间',
    'operationArea': '作业区域',
    'implementationState': '实施状态',
    'networkElementHealth': '网元健康度',
    'bearerInformation': '承载信息',
    'totalNumberOfCuts': '割接总数',
    'nameOfCutOff': '割接名称',
    'jobNumber': '作业编号',
    'cutOffInformation': '割接信息',
    'enterKeywords': '输入关键字',
    'configurationInformation': '配置信息',
    'calendarInformation': '机历信息',
    'machineCalendarCardNo': '机历卡编号',
    'documentNumber': '单据编号',
    'eventType': '事件类型',
    'Dealer': '处理人',
    'processingTime': '处理时间',
    'diachronicDuration': '历时时长',
    'processingResult': '处理结果',
    'contactNumber': '联系电话',
    'softwareVersion': '软件版本',
    'remarksDescription': '备注描述',
    'affiliatedComputerRoom': '所属机房',
    'networkElementIP': '网元IP',
    'affiliatedSite': '所属站点',
    'equipmentManufacturer': '设备厂商',
    'detailAddress': '详细地址',
    'admissionTime': '入网时间',
    'maintenanceAttribute': '维护属性',
    'usageState': '使用状态',
    'networkLevel': '网络级别',
    'totalFrameNumber': '机框总数',
    'channelTotal': '槽道总数',
    'totalNumberOfCards': '板卡总数',
    'totalNumberOfPhysicalTerminals': '物理端总数',
    'totalLogicalTerminals': '逻辑端总数',
    'cabinetInformation': '机柜信息',
    'channelInformation': '槽道信息',
    'cardInformation': '板卡信息',
    'physicalPortInformation': '物理端口信息',
    'logicalPortInformation': '逻辑端口信息',
    'frameNumber': '机框编号',
    'frameName': '机框名称',
    'frameTypeName': '机框类型名称',
    'isDoubleSided': '是否双面',
    'height': '高度',
    'thickness': '厚度',
    'channelNo': '槽道编号',
    'channelName': '槽道名称',
    'boardCode': '板卡编码',
    'boardName': '板卡名称',
    'boardType': '板卡类型',
    'portNumber': '端口编号',
    'portName': '端口名称',
    'portType': '端口类型',
    'loopbackState': '环回状态',
    'inputOpticalPower': '输入光功率',
    'outputOpticalPower': '输出光功率',
    'laserState': '激光状态',
    'portDirection': '端口方向',
    'opticalPowerType': '光功率类型',
    'opticalPower': '光功率',
    'relatedInformation': '关联信息',
    'moveRingInformation': '动环信息',
    'electricityChargeManagement': '电费管理',
    'detail': '详情',
    'additionalInfo': '附加信息',
    'cleared': '已清除',
    'stationCode': '局站编码',
    'longitude': '经度',
    'stationName': '局站名称',
    'latitude': '纬度',
    'stationType': '局站类型',
    'village': '小区',
    'chooseAlarmTip': '请选择告警',
    'paymentStartDate': '支付起始日期',
    'paymentTerminationDate': '支付终止日期',
    'electricMeterNumber': '电表编号',
    'dateOfRead': '抄表日期',
    'allCategories': '所有分类',
    'wattHourMeter': '电表度数',
    'netConfigData': '网络配置数据',
    'networkWarn': '网络警告',
    'electricQuantity': '电量（度）',
    'networkPerformance': '网络性能',
    'startDegree': '起始度数',
    'billRate': '开票税率',
    'monthOfEntry': '入账月份',
    'settlementAmount': '结算金额',
    'settlementStatus': '结算状态',
    'settlementDate': '结算日期',
    'powerSupplyMode': '供电方式',
    'moveRingID': '动环ID',
    'moveRingCode': '动环编码',
    'moveRingName': '动环名称',
    'fourAccessUsers': '4G接入用户数',
    'moveRingSystem': '所属动环系统',
    'ratedInputVoltage': '额定输入电压（V）',
    'ratedInputCurrent': '额定输入电流（A）',
    'upboundEquipmentCodeOne': '上联设备编码1',
    'upboundEquipmentCodeTwo': '上联设备编码2',
    'uplinkDevicePortOne': '上联设备端口1',
    'uplinkDevicePortTwo': '上联设备端口2',
    'numberOfInputCircuits': '输入回路数',
    'numberOfOutputCircuits': '输出回路数',
    'alarmDetail': '告警详情',
    'newCheckItem': '新增检查项',
    'belongsCategory': '所属类别',
    'detectionObject': '检测对象',
    'disposalAdvice': '处置建议',
    'testingWay': '检测方式',
    'patrolItemConfig': ' 巡检项配置',
    'basicInfo': ' 基本信息',
    'objectSet': ' 对象设置',
    'methodsSet': ' 方法设置',
    'abnormalDisposal': ' 异常处置',
    'inspectionName': '巡检项名称',
    'category': '所属分类',
    'objectType': '对象类型',
    'vender': '厂家',
    'equipmentModel': '设备型号',
    'boardModel': '板卡型号',
    'termsOfInspection': '检验方式',
    'accessMethod': '取数方法',
    'JudgmentMethod': '判断方法',
    'serviceName': '服务名',
    'path': '路径',
    'paramsList': '参数列表',
    'source': '来源',
    'alias': '别名',
    'isAllowedEdit': '是否允许编辑',
    'InformWay': '通知方式',
    'allInspectionObjects': '所有巡检对象',
    'equipment': '设备',
    'board': '板卡',
    'port': '端口',
    'inspectionObject': '巡检对象',
    'InspectionItem': ' 巡检项',
    'templateName': '模板名称',
    'whetherEnable': '是否启用',
    'selectedPatrol': '已选巡检项',
    'alternativeInspection': '备选巡检项',
    'inspectionCategory': '巡检类别',
    'inspectionItem': '巡检项',
    'parameter': '参数',
    'toBeImplemented': '待实施',
    'inImplementation': '实施中',
    'operateState': '作业状态',
    'focusOn': '重点关注',
    'jobStartTime': '作业开始时间',
    'jobEndTime': '作业结束时间',
    'GISlocation': 'GIS定位',
    'energyConsumption': '能耗',
    'BBU_name': 'BBU名称',
    'diagnosis': '诊断',
    'networkElementHealthScore': '网元健康度得分',
    '5GCellList': '5G小区清单',
    'fourCellList': '4G小区清单',
    'baseStationID': '基站ID',
    'standardSystem': '制式',
    'baseStationName': '基站名称',
    'subnet': '子网',
    'equipmentStatus': '设备状态',
    'lastYear': '去年',
    'lastMonth': '上月',
    'current': '当前',
    '4GMainUsesIP': '4G主用IP',
    '4GMainUsesPort': '4G主用端口',
    '5GMainUsesIP': '5G主用IP',
    '5GMainUsesPort': '5G主用端口',
    '4GstandbyIP': '4G备用IP',
    '4GstandUsesPort': '4G备用端口',
    '5GstandUsesIP': '5G备用IP',
    '5GstandUsesPort': '5G备用端口',
    'deviceIcon': '设备图示',
    'back': '返回',
    'user': '用户',
    'traffic': '流量',
    'framePanelDraw': '机框面板图',
    'high': '高',
    'low': '低',
    'distributionUsersAndTraffic': '用户与流量分布',
    'RRCMaximumConnection': 'RRC最大连接数',
    'vallageLogicCode': '小区逻辑编码',
    'vallagePhysicalCode': '小区物理编码',
    'vallageID': '小区ID',
    'coverageType': '覆盖类型',
    'centerFrequencyPoint': '中心频点',
    'centralBroadband': '中心宽带',
    'cellReselectionPriorityInFrequency': '频内小区重选优先级',
    'uplinkFrequencyPoint': '上行频点',
    'uplinkBroadband': '上行宽带',
    'penMROrNot': '是否开通MR',
    'downlinkFrequency': '下行频点',
    'downlinkBroadband': '下行宽带',
    'sectorApplicationType': '扇区应用类型',
    'performanceInformation': '性能信息',
    'performanceIndexTime': '性能指标时间',
    'RRCConnectionSuccessRate': 'RRC连接成功率',
    'ERABDropRate': 'E-RAB掉线率',
    'flow': '流量（GB）',
    'uplinkPRBUtilization': '上行PRB利用率',
    'RRU_name': 'RRU名称',
    'radioFrequency': '射频',
    'antennaInformation': '天线信息',
    'equipmentCode': '设备编码',
    'installWidth': '安装宽度',
    'installationWidth': '安装宽度（m）',
    'antennaGain': '天线增益',
    'totalAntennaPitch': '天线总仰俯角',
    'builtInPitchAngle': '内置仰俯角',
    'mechanicalPitchAngle': '机械仰俯角',
    'electricPitch': '电调仰俯角',
    'azimuth': '方位角',
    'feederSpecification': '馈线规格',
    'feederLength': '馈线长度（m）',
    'coverageArea': '覆盖范围（m）',
    'antennaBand': '天线频段',
    'electricAdjustmentAzimuth': '电调方位角',
    'horizontalLobe': '水平波瓣',
    'RRU_logo': 'RRU标识',
    'RRU_type': 'RRU类型',
    'gnbDrops': '终端在gNB的E-RAB掉线率',
    'normalNumberOfPatrolInspection': '巡检正常数',
    'patrolVariable': '巡检异常数',
    'totalInspectionItems': '巡检项总数',
    'dispatchNumber': '派单数',
    'receiptNumber': '回单数',
    'baseStationGrade': '基站等级',
    'cellPhysicalID': '小区物理ID',
    'cellLogicalID': '小区逻辑ID',
    'villageName': '小区名称',
    'station': '局站',
    'welcomeLoginIn': '欢迎登录',
    'pleaseInputUserName': '请输入用户名',
    'pleaseInputPwd': '请输入密码',
    'loginIn': '登录',
    'accountNotEmpty': '账号不能为空',
    'pwdNotEmpty': '密码不能为空',
    'topologicalLocation': '拓扑定位',
    'subdomain': '子区域',
    'sectorName': '扇区名称',
    'AverageNumRRC': '平均RRC连接用户数',
    'averageRRC': '上月同期平均RRC连接用户数',
    'PrbUtilization': '上月同期下行PRB利用率',
    'rank': '排名',
    'reduction': '还原',
    'savePic': '保存为图片',
    'numberOfLoadCircuits': '承载电路数',
    'importantCustomers': '所属重要客户',
    'generalCustomers': '所属一般客户',
    'width': '宽度',
    'grid': '栅格',
    'uptodateTime': '数据最新时间',
    'statistics': '统计',
    'siteNumber': '站址数',
    'gridNumber': '栅格数',
    'RRUNumber': 'RRU数',
    'BBUNumber': 'BBU数',
    'coverage': '覆盖情况',
    'GoodSINR': 'SINR优良化',
    'RSRPcoverage': 'RSRP覆盖率',
    'ComprehensiveCoverage': '综合覆盖率',
    'OverlappingCoverage': '重叠覆盖情况',
    'Modular3Coverage': '模三覆盖率',
    'Overlappingcoverage': '重叠覆盖率',
    'DifferentNetworkForStandard': '异网对标',
    'cPortRegion': 'C端区域',
    'timeDelay': '时延',
    'DITO': 'DITO',
    'PLDT': 'PLDT',
    'GLODE': 'GLODE',
    'regional': '大区',
    'totalGrid': '总栅格',
    'SINRInferior': 'SINR质差',
    'WeakCoverage': '弱覆盖',
    '3Interference': '模3干扰',
    'overlappingCoverage': '重叠覆盖',
    'differentNetwork': '劣于异网',
    'gridDistribution': '栅格动态分布',
    'fullRoute': '全程路由',
    'legendIllustration': '图例说明',
    'REGION': 'REGION',
    'GoodSINRthan': 'SINR优良比',
    'thePhilippines': '菲律宾',
    'CQIgoodThan': 'CQI优良比',
    'netAlarm': '网络告警',
    'addInspection': '新增巡检项',
    'patrolConfiguration': '巡检项配置',
    'objectConfig': '对象设置',
    'methodConfig': '方法设置',
    'errorConfig': '异常设置',
    'addConditions': '新增条件',
    'disposalAdviceDec': '请检查传输光路和光模块、珐琅盘等器件',
    'aisleName': '通道名称',
    'fiberOpticCableName': '光缆名称',
    'fiberOpticCableNumber': '光缆编号',
    'fiberOpticCableType': '光缆类型',
    'hierarchicalRoutingList': '分层路由列表',
    'totalNumberOfSNC': 'SNC总数',
    'totalNumberOfCrossConnections': '交叉连接总数',
    'totalTopologyConnections': '拓扑连接总数',
    'objectName': '对象名称',
    'protectionState': '保护状态',
    'SNC_level': 'SNC层级',
    'rsrpPoorCnt': 'RSRP质差栅格数',
    'addTemplate': '新增模板',
    'patrolJobTemplateConfig': '巡检作业模板配置',
    'resourceData': '资源数据',
    'paramConfig': '参数配置',
    'performComp': '性能达标',
    'IntactEquip': '设备完好率',
    'equipDiag': '设备诊断',
    'isHidden': '是否隐患',
    'choose': '选择',
    'patrolOperationPlanConfig': '巡检作业计划配置',
    'patrolJobTemplate': '巡检作业模板',
    'inspectionObjectSelection': '巡检对象选择',
    'patrolItemSet': '巡检项设置',
    'cycleSet': '周期设置',
    'threshold': '阀值',
    'disposalWay': '处置方式',
    'lifecycle': '执行周期',
    'verificationCycle': '验证周期',
    'hour': '时',
    'sun': '日',
    'weeks': '周',
    'month': '月',
    'pleasePatrolKeyWord': '请输入巡检项关键字',
    'gridSize': '栅格规模',
    'selAlarmHasNoMeetRecords': '已选择告警中无满足操作条件的记录！',
    'successful': '成功',
    'unsuccessful': '失败',
    'Monday': '星期一',
    'Tuesday': '星期二',
    'skip': '跳过',
    'Wednesday': '星期三',
    'Thursday': '星期四',
    'Friday': '星期五',
    'Saturday': '星期六',
    'Sunday': '星期日',
    'enterTheQueryCriteriaName': '请输入查询条件名称',
    'forecastCovering': '预测覆盖',
    'allAssignments': '所有作业',
    'transmissionNetwork': '传输网',
    'ipNetwork': 'Ip网',
    'coreNetwork': '核心网',
    'wirelessNetwork': '无线',
    'operationMonitoringDetails': '作业监视详情',
    'totalNumber': '总数',
    'normal': '正常',
    'hierarchicalRoute': '分层路由',
    'totalOf': ' 共',
    'toPerform': '待执行',
    'normalExecution': '正常执行',
    'completes': '执行完成',
    'receipt': '回单',
    'currentState': '当前状态',
    'recently': '最近执行',
    'completionTime': '完成时间',
    'diagramDescription': '图列说明',
    'patrolAbnormalRanking': '巡检异常排名TOP10',
    'abnormalDevices': '异常设备数',
    'inspectionTime': '巡检时间',
    'portAlarm': '端口告警',
    'performanceQuery': '性能查询',
    'bearerCircuit': '承载电路',
    'routeList': '路由列表',
    'occurrenceTime': '发生时间',
    'clearState': '清除状态',
    'keyField1': '关键字段1',
    'workOrderNumber': '工单编号',
    'lightHarvestePower': '收光功率',
    'luminousPower': '发光功率',
    'unavailableSeconds': '不可用秒',
    'seriousBitErrorSeconds': '严重误码秒',
    'bitErrorSeconds': '误码秒',
    'performing': '正在执行',
    'performError': '执行异常',
    'continuousAbnormal': '持续异常',
    'clearAllElements': '清除所有要素',
    'sector': '扇区',
    'testInfo': '检测信息',
    'layerFilter': '图层过滤',
    'areaLocation': '区域定位',
    'testResults': '检测结果',
    'inspectionOperationPlan': '巡检作业计划',
    'checkItemConfig': '检查项配置',
    'testMethod': '检验方法',
    'checkItemName': '检查项名称',
    'coverage_area': '覆盖范围',
    'crossover': '频点',
    'directionAngle': '方向角',
    'downtilt': '下倾角',
    'configurationMethod': '配置方法',
    'associatedGridNumber': '关联栅格数',
    'incident': '事件',
    'customizationMethod': '定制方法',
    'geographicGridnumber': '地理栅格数',
    'additionalCoveragepoint': '新增覆盖点',
    'constructionDemandbank': '建设需求库',
    'profileInfo': '概要信息',
    'allObjectsOfAcceptance': '所有验收对象',
    'adjustPriority': '调整优先级',
    'objectOfAcceptance': '验收对象',
    'planning': '规划',
    'originalCollectedInfo': '原始采集信息',
    'patrolItemsClassification': '巡检项分类',
    'priority': '优先级',
    'TestingTime': '检测时间',
    'workOrderStatus': '工单状态',
    'persistentAnomalies': '持续异常次数',
    'actualValue': '实际值',
    'results': '结果',
    'provinceLayer': 'province图层',
    'regionLayer': 'region图层',
    'sectorLayer': '扇区图层',
    'stationLayer': '局站图层',
    'gridLayer': '栅格图层',
    'networkCoverageQuality': '本网覆盖质量',
    'coverageRate': '覆盖率',
    'deviceSearchLocation': '设备检索定位',
    'toolBox': '工具栏',
    'frameSelection': '框选',
    'ranging': '测距',
    'alarmLocation': '告警定位',
    'alarmTitle': '告警标题',
    'faultyWorkOrderNo': '故障工单号',
    'powerDownNumber': '掉电数',
    'powerDownRate': '掉电比例',
    'stationBreakNumber': '断站数',
    'stationBreakRate': '断站比例',
    'linkBreakage': '链路断数',
    'otherStationBreakNumber': '其他类断站数',
    'alarmFilter': '告警过滤',
    'stationBreak': '断站',
    'powerDown': '掉电',
    'linkBreak': '链路中断',
    'other': '其他',
    'deviceFilter': '设备过滤',
    'LTEStation': 'LTE基站',
    'LTESector': 'LTE小区',
    '5GStation': '5G基站',
    '5Gsector': '5G小区',
    'retirementState': '退服状态',
    'sectorDetailsPage': '扇区详情页面',
    'RRCConnectionAttempts': 'RRC连接尝试次数',
    'RRCConnectionSuccess': 'RRC连接成功数',
    'maximumNumberOfRRCConnectedUsers': '最大RRC连接用户数',
    'cellUplinkTraffic': '小区上行流量',
    'cellDownlinkTraffic': '小区下行流量',
    'PRBDownlinkAverageUtilization': 'PRB下行平均利用率',
    'userDownlinkExperienceRate': '用户下行体验速率',
    'break': '中断',
    'performanceStatistics': '性能统计',
    'AcceptanceSchemeTemplateConfiguration': ' 验收方案模板配置',
    'InspectionItemConfiguration': '检验项配置',
    'SchemeName': '方案名称',
    'PeriodicExecutionOrNot': '是否周期执行',
    'DaysOfDuration': '持续天数',
    'TestTimePoint': '检测时间点',
    'Checklist': '检查项清单',
    'ProgramTemplate': '方案模板',
    'CheckItem': '检查项',
    'ThresholdSetting': '阈值设置',
    'referenceValue': '基准值',
    'direction': '方向',
    'ResultSet': '结果设置',
    'SourceAttribute': '来源属性',
    'numberOfGeneralCustomers': '所属一般客户数',
    'numberOfImportantCustomers': '所属重要客户数',
    'stockForecastcoveragePoint': '存量预测覆盖点',
    'rural': '农村',
    'township': '乡镇',
    'city': '城市',
    'outdoorAcerstation': '室外宏基站',
    'smallStation': '小微站',
    'chamberSubsystem': '室分系统',
    'coveredAreatype': '覆盖区域类型',
    'installHeight': '安装高度',
    'terminationDegree': '终止度数',
    'HTable': 'H码列表',
    'officeInfo': '局向信息',
    'instructionList': '指令列表',
    'downScene': '下发场景',
    'operators': '运营商',
    'Hcode': 'H码',
    'areaCode': '区域码',
    'HCodeFile': 'H码文件',
    'updTemplate': '修改模板',
    'workArea': '工作区域',
    'operaeGrade': '作业等级',
    'InstructionName': '指令名称',
    'instructions': '指令',
    'please': '请输入',
    'generatingCommand': '生成指令',
    'derivedInstruction': '导出指令',
    'comfirmSubmit': '确认提交',
    'majorChoose': '请选择专业',
    'frame': '机框',
    'channel': '槽道',
    'physicalPort': '物理端口',
    'logicalPort': '逻辑端口',
    'typeName': '类型名称',
    'code': '编码',
    'all': '全部',
    'existsChoose': '已有存在数据，请重新选择',
    'businessScale': '业务规模',
    'pass': '通过',
    'noPass': '不通过',
    'runIndicator': '运行指标',
    'exeCommand': '执行指令',
    'editForecastcoveragePoints': '编辑预测覆盖点',
    'reExeCommand': '重新执行',
    'showInsFail': '显示失败指令',
    'showInsAll': '显示所有指令',
    'taskNo': '任务编号',
    'business': '所属业务',
    'taskScene': '任务场景',
    'executionList': '执行列表',
    'exeLog': '执行日志',
    'bureauStationnumber': '局站数',
    'model': '型号',
    'totalNumberofGrid': '总栅格数',
    'SINRqualityDifferencegridNumber': 'SINR质差栅格数',
    'weakCovergridNumber': '弱覆盖栅格数',
    'NumberOfinferiorAnddissimilarGrids': '劣与异网栅格数',
    'physicalCode': '物理编码',
    'logicCode': '逻辑编码',
    'thousandPeople': '千人',
    'dataUserNumThousand': '数据用户数(千人)',
    'fourGigabytes': '4G流量(GB)',
    'insReport': '执行报告',
    'yesOrNo': '是否',
    'comfirPass': '是否确认通过',
    'comfirmNoPass': ' 是否确认不通过',
    'insTatal': '指令总数',
    'queryMsg': '查询信息',
    'geometry': '经纬度',
    'address': '地址',
    'clearMap': '清除地图',
    'siteAlarm': '基站告警',
    'sectorAlarm': '扇区告警',
    'beforeRegionName': '调整前区域名称',
    'beforeRegionNum': '调整前区号',
    'exportSuccess': '导出成功',
    'exportAddSuccess': '导出新增模板成功',
    'exportEditSuccess': '导出修改模板成功',
    'exportInsSuccess': '导出指令成功',
    'tendencyChart': '趋势图',
    'detailsResources': '资源详情',
    'sectorLongitude': '扇区经度',
    'networkType': '网络类型',
    'inAndout': '室内外',
    'sectorLatitude': '扇区纬度',
    'problemSituation': '问题情况',
    'wirelessParameters': '无线参数',
    'activityInformation': '领区信息',
    'KPItarget': 'KPI指标',
    'directionalRuleTable': '定向规则列表',
    'directionalRuleFile': '定向规则文件',
    'productId': '销售品ID',
    'productSaleName': '销售品名称',
    'rateGroup': '费率组',
    'regularIndex': '规则索引',
    'protocolType': '协议类型',
    'iPMask': 'IP/掩码',
    'startingPort': '起始端口',
    'homeNetwork': '本网',
    'SINRcoverage': 'SINR覆盖率',
    'recentTime': '最近执行完成时间',
    'rasterData': '栅格数据',
    'addedForecastcoveragePoints': '新增预测覆盖点',
    'selectResult': '选择结果',
    'selectNetworkInfo': '请选择网元信息',
    'importHCode': '请导入号码数据',
    'timeJudge': '结束时间必须大于开始时间',
    'CheckItemEdit': '检查项编辑',
    'editAcceptanceSchemeTemplate': '验收方案模板编辑',
    'SelectPointInTime': '选择时间点',
    'gridIdentifier': '栅格编号',
    'bureauStanddetails': '局站详情',
    'generateCommand': '请生成指令',
    'NetworkRate': '网络速率',
    'BarangayQuantity': 'Barangay数量',
    'PopulationCoverage': '人口覆盖率',
    '4GUserNumber': '4G用户数',
    'RoamingUser': '漫游用户',
    'RoamingTrafficIndex': '漫游流量指标',
    '4GUser': '4G用户',
    '5Guser': '5G用户',
    'BroadbandUser': '宽带用户',
    'alternative': '备选',
    'selected': '已选',
    'originalReturnedResult': '原始返回结果',
    'detectionConclusion': '检测结论',
    'NeedToFillIn': '需填写',
    'exportTemplate': '导出模板',
    'ruleType': '规则类型',
    'endPort': '结束端口',
    'numberOfbusinessGrids': '业务栅格数',
    'businessIndicators': '业务指标',
    'predictThenumberOfgrids': '预测栅格数',
    'jobDetail': '作业详情',
    'migrationObject': '割接对象',
    'migrationAlarm': '割接告警',
    'migrationPerformance': '割接性能',
    'migrationBusiness': '割接业务',
    'jobSource': '作业来源',
    'jobSpeciality': '作业专业',
    'jobType': '作业类型',
    'concernedLevel': '关注级别',
    'notificationEndTime': '通知结束时间',
    'notificationStartTime': '通知开始时间',
    'transmission': '传输',
    'access': '接入',
    'core': '核心',
    'archived': '已归档',
    'datas': '数据',
    'resourceInformation': '资源信息',
    'migrationKeyword': '割接关键字',
    'totalNumberOfItems': '项目总数',
    'abnormalNumbers': '异常数',
    'confirmor': '确认人',
    'compareResult': '对比结果',
    'detectionItem': '检测项目',
    'initialTime': '首次时间',
    'lastTime': '末次时间',
    'initialDetectionValue': '首次检验值',
    'initialResult': '首次结果',
    'lastResult': '末次结果',
    'lastDetectionValue': '末次检验值',
    'alignmentResult': '比对结果',
    'archivingPersonnel': '归档人',
    'archivingTime': '归档时间',
    'archivingType': '归档类型',
    'archivingRemark': '归档备注',
    'maskWarningOrNot': '是否屏蔽警告',
    'maskAlarmOrNot': '是否屏蔽告警',
    'objectFiltering': '对象过滤',
    'jobLevel': '作业层级',
    'alarmAttribut': '告警属性',
    'jobCycle': '作业周期',
    'manualConfirmation': '人工确认',
    'businessAcquisition': '业务采集',
    'locationObject': '定位对象',
    'locationAlarm': '定位告警',
    'performanceAcquisition': '性能采集',
    'alarmSynchronized': '告警同步',
    'alarmConfirm': '告警确认',
    'alarmTerminated': '告警终止',
    'expandAll': '全部展开',
    'packUpAll': '全部收起',
    'advancedOperations': '高级操作',
    'networkManagementID': '网管标识',
    'opticalCableSegmentName': '光缆段名称',
    'numberOfMaskedAlarms': '屏蔽告警数',
    'stateRemark': '状态备注',
    'fiberCoreNumber': '纤芯号',
    'DITORSRPCoverage': 'DITORSRP覆盖率',
    'PLDTRSRPCoverage': 'PLDTRSRP覆盖率',
    'GLOBERSRPCoverage': 'GLOBERSRP覆盖率',
    'ThisFunctionIsNotAvailable': '暂无此功能',
    'MethodDescription': '方法说明',
    'MethodDescriptionDesc': '该方法用于比较两个值的大小，参数有实际值，阈值，以及通过标准（1 大的通过 2小的通过）',
    'lastData': '这是最后一条数据，无法下移',
    'firstData': '这是第一条数据，无法上移',
    'nameInput': '请输入名称',
    'categoryInput': '请选择所属类别',
    'adjustSuccess': '调整优先级成功！',
    'ruleDescrInput': '请输入规则描述',
    'checkObjTypeInput': '请选择对象类型',
    'checkWayInput': '请选择检验方式',
    'dealSuggestInput': '请输入处置建议',
    'vendorIdInput': '请选择厂家',
    'neTypeIdInput': '请选择设备类型',
    'neModelIdInput': '请选择设备型号',
    'cardTypeIdInput': '请选择板卡类型',
    'cardModelIdInput': '请选择板卡型号',
    'portTypeIdInput': '请选择端口类型',
    'checkProErrorDealListInput': '请选择通知方式',
    'switchProfessional': '若重新选择专业，会清空已配置的条件，是否继续',
    'crossAreaCoverage': '越区覆盖',
    'customerComplaint': '用户投诉',
    'networkQuality': '网络质量',
    'crossAreaCoverageRate': '越区覆盖率',
    'mode3': '模三占比',
    'wirelessVolteVoiceQuality': '无线VOLTE语音质量',
    'objectSetInput': '对象设置调整，将清空备选巡检项和已选巡检项，是否继续',
    'objectSetInputPlease': '对象设置不能为空，请至少配置一条',
    'fluctuationRatio': '波动率（%）',
    'cellNum': '小区数',
    'bussinessMaintenir': '业务保持',
    'overlayGridAnalysis': '覆盖栅格分析',
    'problemSolveStatistics': '问题解决统计',
    'bussinessAccessExperience': '业务接入体验',
    'rrcSuccessRate': 'RRC成功率',
    '700MExperienceRate': '700M体验速率',
    'ERABDrops': 'ERAB掉线率',
    'secondExperienceRate': '2.1G体验速率',
    'wirelessVolteConnRate': '无线VOLTE接通率',
    'wirelessVolteDropRate': '无线VOLTE掉话率',
    'mode3Inter': '模三干扰',
    'flowGB': '流量(GB)',
    'roadTestDropRate': '路测掉话率',
    'roadTestSwitchingSuccessRate': '路测切换成功率',
    'userNumber': '用户数',
    'networkCoverage': '网络覆盖率',
    'migrationKeyValue': '割接关键值',
    'equipmentIPAddress': '设备IP地址',
    'alarmRecoverTime': '告警恢复时间',
    'dataMsgInput': '请选择一条以上的数据信息',
    'seleted': '已选择',
    'enableInput': '请选择是否启用',
    'specialtyName': '专业名称',
    'faultTitle': '故障标题',
    'deviceInformation': '设备信息',
    'repairAdvice': '修复建议',
    'everyMinute': '每分钟',
    'every': '每隔',
    'specifiedRange': '指定范围',
    'specifyValue': '指定值',
    'perHour': '每小时',
    'everyDay': '每天',
    'lastDayMonth': '当月最后一天',
    'workDay': '离指定日期最近的工作日',
    'notSpecify': '不指定',
    'everyWeek': '每周',
    'lastWeek': '当月最后一周',
    'firstWeek': '第一周',
    'secondWeek': '第二周',
    'thirdWeek': '第三周',
    'fourthWeek': '第四周',
    'fiveWeek': '第五周',
    'everyMonth': '每月',
    'taskPlanModelInput': '请选择巡检作业模板',
    'cronExpSet': '请配置执行周期',
    'takeEffectTime': '生效结束时间必须大于开始时间',
    'thresholdValue': '阈值',
    'dealWayChoose': '请至少选择一种处置方式',
    'dealWayInput': '请至少选择一个处置方式',
    'patrolItemInput': '至少选择一条巡检项设置',
    'taskPhaseName': '任务环节名称',
    'numberOfCollecteRecords': '采集记录数',
    'acquisitionSize': '采集大小',
    'collecteStatus': '采集状态',
    'fileResoluteDetail': '文件解析明细',
    'fileMergedetail': '文件合并明细',
    'signInSuccess': '恭喜你，签到成功！',
    'BearerNetwork': '承载网络',
    'ThousandKmOptacalCable': '千米光电电缆',
    'IPCoreNode': 'IP核心',
    'OTNCoreNode': 'OTN核心',
    'OTNBackBoneRing': 'OTN骨干',
    'WirelessNetwork': '无线网络',
    '4GBaseStationNumber': '4G基站数',
    '5GBaseStationNumber': '5G基站数',
    'CloudInfrastructure': '云基础设施',
    'VirtualMachine': '虚拟机',
    'CPUCore': 'CPU核心',
    'Memory': '内存',
    'Storage': '硬盘',
    'Bandwidth': '带宽',
    'OperationAndMaintenanceIndex': '运维指标',
    'IPTraffic': 'IP流量',
    'VoLTETraffic': 'VoLTE流量',
    'MMEAttachedUsers': 'MME附加用户',
    'PagingSuccessRate': '寻呼成功率',
    'HealthDegree': '健康程度',
    'TotalNumberOfWorlOrders': '工程订单总数',
    'NumberOfOpeningOrders': '期初订单数',
    'zoomIn': '缩小',
    'zoomOut': '放大',
    'returnScreen': '当前屏幕',
    'fullScreen': '满屏',
    'fullScreen2': '全屏',
    'search': '搜索',
    'move': '拖拽',
    'tab': '表格',
    'element': '元素',
    'default': '默认',
    'upperAndLowerLayout': '上下布局',
    'leftAndRightLayout': '左右布局',
    'circularLayout': '环形布局',
    'orthogonalLayout': '正交布局',
    'sectorLayout': '扇形布局',
    'treeLayout': '树形布局',
    'classHierarchy': '分类层级',
    'loopLayout': '圆形布局',
    'networkAtta': '属性信息',
    'topologyInfo': '附属信息',
    'modelName': '模型名称',
    'recoverTime': '恢复时间',
    'faultNumber': '故障单号',
    'ocName': 'OC名称',
    'goOnDispatch': '继续派单',
    'exit': '退出',
    'optimizationDemand': '优化需求',
    'displayMode': '显示方式',
    'tabulation': '列表',
    'MRCoverage': 'MR覆盖率',
    'mainPlot': '主小区',
    'optimizeLibrary': '优化库',
    'mainPlotname': '主小区名',
    'PLDTCoverage': 'PLDT覆盖率',
    'GLOBECoverage': 'GLOBE覆盖率',
    'selectOptimizationscheme': '选择优化方案',
    'OptimizationType': '优化类型',
    'preOptimizationcoverage': '优化前覆盖率',
    'requirementTypes': '需求类型',
    'optimizedCoverage': '优化后覆盖率',
    'optimizationScheme': '优化方案',
    'optimizationResults': '优化结果',
    'dataSource': '数据来源',
    'GLODECoverage': 'GLODE覆盖率',
    'ParamSet': '参数设置',
    'AdjustOrder': '调整顺序',
    'GLOBE': 'GLOBE',
    'runStatus': '运行状态',
    'principal': '负责人',
    'test': '测试',
    'stopService': '暂停服务',
    'recoverService': '恢复服务',
    'offlineService': '下线服务',
    'onlineService': '上线服务',
    'attributionInfo': '归属信息',
    'labelInfo': '标签信息',
    'reqHeader': '请求头',
    'reqParams': '请求参数',
    'respResult': '返回结果',
    'testConfig': '测试配置',
    'statusCode': '状态码',
    'accessDenied': '访问限制',
    'servName': '服务名称',
    'version': '版本号',
    'servURL': '服务URL',
    'reqMethodName': '请求方法名称',
    'heartbeatUrl': '心脏探测URL',
    'timeout': '响应超时',
    'apiProtooc': '接口协议',
    'apiType': 'API类型',
    'invokeType': '调用方式',
    'isRequired': '必填',
    'description': '说明',
    'paramType': '参数类型',
    'location': '位置',
    'defaultValue': '默认值',
    'reqDemo': '请求示例',
    'respDemo': '返回示例',
    'frequencylimit': '频率限制',
    'trafficLimit': '流量限制',
    'second': '秒',
    'capabilitySafetyLevel': '能力安全等级',
    'singleAccessMax': '单次访问流量阈值',
    'serviceTest': '服务测试',
    'deadline': '截止时间',
    'addService': '添加服务',
    'assignPrincipal': '指定负责人',
    'spectacular': '看板',
    'selectAll': '全选',
    'collect': '收藏',
    'cancelCollect': '取消收藏',
    'applicationSharing': '申请共享',
    'addCart': '加入服务车',
    'reqContent': '请求内容',
    'respCode': '响应码',
    'respContnet': '响应内容',
    'respHeader': '响应头',
    'resultCheck': '结果验证',
    'editService': '编辑服务',
    'viewService': '查看服务',
    'selectAssignPrincipalTip': '请选择需要指定负责人的数据',
    'systemHint': '系统提示',
    'confirmCoverServOrNot': '是否确认恢复该服务',
    'confirmOfflineServOrNot': '是否确认下线该服务？',
    'confirmOnlineServOrNot': '是否确认上线该服务？',
    'checkThedetails': '查看详情',
    'fullLifecycle': '全生命周期',
    'ruleRelateAlarm': '告警关联规则',
    'ruleRelateFilter': '告警过滤规则',
    'ruleRelateAutoMsg': '自动短信规则',
    'ruleAutoTicket': '自动派单规则',
    'tabOptions': '标签选项',
    'closeAll': '关闭所有',
    'closeOther': '关闭其它',
    'logout': '退出登录',
    'NetworkIndicators': '网络指标',
    'CutNumber': '割接数',
    'FailureRate': '失败率',
    'recommendedPlan': '推荐方案',
    'generateSolution': '生成方案',
    'autoOptimization': '自动优化',
    'beforeOptgridCoverage': '优化前栅格覆盖率',
    'dipangleCfmval': '下倾角确认值',
    'dipangleRecval': '下倾角推荐值',
    'dipangleOrival': '下倾角原始值',
    'artificialOptimization': '人工优化',
    'communityInfo': '小区信息',
    'parameterInfo': '参数信息',
    'mainCellCurrVal': '主小区(目前值)',
    'mainCellReferVal': '主小区(参考值)',
    'firstPowerCellCurrVal': '主强邻区(目前值)',
    'firstPowerCellReferVal': '主强邻区(参考值)',
    'secPowerCellCurrVal': '次强邻区(目前值)',
    'secPowerCellReferVal': '次强邻区(参考值)',
    'rsSendPower': 'RS发射功率',
    'miniReceptionLevel': '最小接收电平',
    'direcAngleOriginalVal': '方向角原始值',
    'direcAngleReferVal': '方向角推荐值',
    'direcAngleAffirmVal': '方向角确认值',
    'adjacentRegionIsMatch': '邻区是否匹配',
    'top1AdjacentRegion': 'top1邻区',
    'top2AdjacentRegion': 'top2邻区',
    'constructionRequirement': '建设需求',
    'coverageDistrict': '覆盖区域',
    'optimizationEffectEvaluation': '优化效果评估',
    'connectSuccessRate': '连接成功率',
    'contextDropRate': '上下文掉线率',
    'userFlow': '用户面流量',
    'top3AdjacentRegionCoverRate': 'Top3邻区覆盖率',
    'top3AdjacentRegionName': 'Top3邻区名称',
    'top2AdjacentRegionCoverRate': 'Top2邻区覆盖率',
    'top2AdjacentRegionName': 'Top2邻区名称',
    'top1AdjacentRegionCoverRate': 'Top1邻区覆盖率',
    'top1AdjacentRegionName': 'Top1邻区名称',
    'power': '功率',
    'confirmSave': '确认保存',
    'flowType': '预处理类型',
    'alarmTypeDesc': '告警类型',
    'addPretreatConfig': '新增预处理规则配置',
    'editPretreatConfig': '编辑预处理规则配置',
    'copyPretreatConfig': '复制预处理规则配置',
    'viewPretreatConfig': '查看预处理规则配置',
    'timeLimit': '超限频次',
    'avoidTime': '回避时段',
    'flowName': '流程名称',
    'flowId': '流程标识',
    'requestTime': '请求耗时限制',
    'timeSpan': '请求间隔',
    'clientKey': '客户端秘钥',
    'flowVersion': '流程版本',
    'inputParams': '请求参数配置',
    'serCode': '服务编码',
    'resultCode': '结果编码',
    'resultDesc': '结果描述',
    'resultType': '结果类型',
    'resultInfo': '附属信息配置',
    'outputInfo': '输出属性配置',
    'resultConfig': '结果配置',
    'outputInfoConfig': '输出信息配置',
    'processConfig': '流程配置',
    'beforeAdjustment': '调整前',
    'primaryCellCoverage': '主小区覆盖率',
    'gridCoverage': '栅格覆盖率',
    'afterAdjustment': '调整后',
    'confirmFileFormat': '只允许格式为.xlsx的文件上传',
    'afterAdjGridCoverRate': '优化后栅格覆盖率',
    'assessmentResult': '评估结果',
    'assessEndTime': '评估结束时间',
    'assessBeginTime': '评估开始时间',
    'CLOBE': 'CLOBE',
    'inferiorToglobe': '劣于GLOBE',
    'inferiorTopldt': '劣于PLDT',
    'AREA': 'AREA',
    'flowNameInput': '请输入流程名称',
    'flowIdInput': '请输入流程标识',
    'serCodeInput': '请输入服务编码',
    'serNameInput': '请输入服务名称',
    'resultCodeInput': '请输入结果编码',
    'resultDescInput': '请输入结果描述',
    'resultTypeInput': '请选择结果类型',
    'priorityLvlInput': '请输入优先级',
    'alarmTypeInput': '请选择告警类型',
    'flowTypeInput': '请选择预处理类型',
    'waitTimeInput': '请输入延迟时间',
    'limitNumInput': '请输入超限频次',
    'timeLimitInput': '请输入求耗时限制',
    'flowVersionInput': '请输入流程版本',
    'inputParamsInput': '请输入请求参数配置',
    'resultConfigOper': '请选择一条结果配置操作',
    'onlyResultConfig': '请至少配置一条结果配置',
    'calculatingThegrid': '正在计算栅格',
    'pretreatment': '预处理规则',
    'overlapCoverageratio': '重叠覆盖占比',
    'confirmSelectObject': '请确认勾选一个割接对象',
    'confirmHasSelectedObject': '确认要人工确认已选对象吗',
    'confirmAtLeastObject': '请勾选至少一个割接对象',
    'confirmItemAtLeast': '请勾选至少一个检测项目',
    'performanceItemLimit': '实时性能采集检测项目不能超过10个',
    'serviceItemLimit': '实时业务采集检测项目不能超过10个',
    'checkDetail': '检测明细',
    'units': '单位',
    'checkRuler': '检测规则说明',
    'checkResList': '检测结果列表',
    'checkValue': '检验值',
    'log': '日志',
    'checkLog': '检测日志',
    'roadTestinfo': '路测信息',
    'averageRSRP': '平均RSRP',
    'averageSINR': '平均SINR',
    'connectionRatio': '接通率',
    'droppedRatio': '掉话率',
    'specialityType': '专业类型',
    'paramVal': '参数值',
    'flowStatus': '流程状态',
    'conclusionCode': '结论编码',
    'conclusionDesc': '结论描述',
    'errorMsg': '错误信息',
    'reqTime': '请求时间',
    'respTime': '响应时间',
    'keywordNoEmp': '关键字词不能为空',
    'taskId': '任务标识',
    'enterFlowId': '请输入流程实例ID',
    'srvStatus': '服务状态',
    'attAttribute': '附属属性',
    'reqInstruction': '请求指令',
    'respInstruction': '应答指令',
    'link': '环节',
    'diagnosisDetail': '诊断详情',
    'flowInfo': '流程信息',
    'flowDemo': '流程实例',
    'diagnosisTime': '诊断时间',
    'noSubZero': '天数不可小于0',
    'cannotSubZero': '不可小于0',
    '4GConnectSuccessRate': '4G连接成功率',
    '5GConnectSuccessRate': '5G连接成功率',
    'beforeOptimizationRate': '同比优化前',
    'coverageTrend': '覆盖率趋势',
    'statementOfAccount': '结单',
    'service': '服务业务',
    'Service': '服务指标',
    'evaluationEnd': '评估结束',
    'evaluationStart': '评估开始',
    'createQuaGrid': '生成质差栅格',
    'whetheToConfirmSubmission': '是否确认提交？',
    'enterAdvice': '请输入建议',
    'advice': '建议',
    'currentValue': '目前值',
    'reference': '参考值',
    'mainPowerCell': '主强邻区',
    'secondPowerCell': '次强邻区',
    'interact': '交互方式',
    'methodChoose': '方法选择',
    'kafkaTheme': 'kafka主题',
    'Synchronous': '同步',
    'Asynchronous': '异步',
    'kafkaIdInput': '请选择kafka主题',
    'fetchMethod': '请选择取数方法',
    'judgeMethod': '请选择判断方法',
    'prameInput': '请选择参数列表',
    'methodSetChoose': '请选择方法设置',
    'resultSetConfig': '请配置完全结果设置',
    'fetchMethodConfig': '请配置完全取数方法中的参数列表',
    'judgeMethodConfig': '请配置完全判断方法中的参数列表',
    'methodSetConfig': '请配置完全方法设置中的参数列表',
    'lifecycleState': '生命周期状态',
    'uplinkDeviceIP': '上联设备IP',
    'uplinkDeviceName': '上联设备名称',
    'noRemark': '暂无说明',
    'optimizeThedetails': '优化详情',
    'netRobustness': '网络健壮性',
    'alarmFaultOvertime': '告警故障工单超时率',
    'keyFaultNum': '关键故障数',
    'modifier': '修改人',
    'modifierTime': '修改时间',
    'phenomenon': '现象',
    'commonSearch': '常用搜索',
    'testResult': '测试结果',
    'paramName': '参数名称',
    'apply': '申请',
    'platformAudit': '平台审核',
    'applyExplain': '申请说明',
    'addIP': '添加IP',
    'applyInfo': '申请信息',
    'accessIP': '访问IP',
    'choosedServices': '已选服务',
    'serviceMenu': '服务目录',
    'keepAtleastOneService': '至少保留一个服务',
    'timeoutSecond': '响应超时(秒)',
    'singleAccessMaxMb': '单次访问流量阈值(MB)',
    'serUrlInput': '请输入服务URL',
    'serRemarkInput': '请输入备注',
    'setTheFilter': '设置筛选',
    'microWave': '骨干微波',
    'Region': '地区',
    'Province': 'PROVINCE',
    'City': 'CITY',
    'newTest': '新测试一条',
    'benTest': '崩了之后重测一条',
    'publish': '发布',
    'timeoutLimit': '超时时限',
    'dealObject': '处理对象',
    'linkCode': '环节编码',
    'linkInfo': '环节信息',
    'readingLink': '阅办环节',
    'manualLink': '人工环节',
    'autoLink': '自动环节',
    'testOne': '测试一条数据',
    'propertyName': '属性名称',
    'addListItem': '添加列表项',
    'outSideLink': '外部链接',
    'notifiy': '通知',
    'pleaseInputLinkName': '请输入环节名称',
    'pleaseInputLinkCode': '请输入环节编码',
    'isAsync': '是否异步',
    'timeoutJump': '超期跳转',
    'requestTimeout': '请求超时',
    'retryTime': '重试次数',
    'retryDelay': '重试延迟',
    'dealFieldType': '失败处理策略',
    'endFlow': '结束流程',
    'endLink': '结束环节',
    'policyType': '决策类型',
    'subFlowSource': '子流程来源',
    'subFlowSourceMess': '选择字段的值提供子流程的编码!',
    'chooseSubFlow': '选择子流程',
    'fieldConfig': '字段配置',
    'newTestTest': '再测试一次',
    'content': '内容',
    'onlineUsersNumber': '在线用户数',
    'fixedValue': '固定值',
    'formField': '表单字段',
    'physicalSiteNumber': '物理站点数',
    'pleaseInputHeader': '请填写请求头',
    'pleaseChooseType': '请选择类型',
    'pleaseInputParam': '请填写参数名称',
    'pleaseInputResult': '请填写结果标识',
    'outsideLinkInfo': '注意:单选，多选，下拉，下拉多选的字段提供显示内容,其他字段提供值',
    'resultState': '结果标识',
    'pleaseInputParamType': '请选择参数类型',
    'pleaseInputParamPosition': '请选择参数位置',
    'pleaseChooseValueSource': '请选择值来源',
    'pleaseInputUrl': '请输入链接地址',
    'canChooseOper': '可选操作',
    'withDrawable': '可撤单',
    'convertible': '可转办',
    'recoveryRule': '回收规则',
    'receiptRule': '回单规则',
    'manyOrders': '多条工单',
    'passRate': '通过比例',
    'intefaceConfig': '接口配置',
    'doPersion': '办理人员',
    'operaConfig': '操作配置',
    'anyLink': '任何环节',
    'lastLink': '下一环节',
    'allGetOrder': '全部回单',
    'oneGetOrder': '一人回单',
    'huiqiang': '回签',
    'theDayOfWork': '工作日',
    'theDayOfNature': '自然日',
    'handUpLimit': '挂起时限',
    'dataOrder': '数据要求',
    'wordTarget': '工作目标',
    'workContent': '工作内容',
    'notifyContent': '通知内容',
    'sendTo': '发送至',
    'notifyRange': '通知范围',
    'notifyTitle': '通知标题',
    'notifyType': '通知类型',
    'triggerRule': '触发规则',
    'dealPerson': '办理人',
    'stepDeal': '逐级办理',
    'condition': '条件',
    'choosePersion': '选择人员',
    'dealPersionType': '办理人类型',
    'endCondition': '终止条件',
    'allLevel': '所有层级',
    'defaultConfig': '默认配置',
    'shuxingValue': '属性值',
    'showContent': '显示内容',
    'testetst': '测试测试',
    'errorNum': '异常次数',
    'createBeginEvent': '创建开始事件',
    'createEndEvent': '创建汇聚节点',
    'createGroupEvent': '创建结束事件',
    'createCondition': '创建条件判断',
    'createParalEvent': '创建并行分支',
    'createManaulEvent': '创建人工环节',
    'createAutoEvent': '创建自动环节',
    'createSubFlow': '创建动态子流程',
    'createReadEvent': '创建阅办环节',
    'createSubEvent': '创建子流程环节',
    'createServiceMenu': '创建服务目录环节',
    'pleaseConfigCondition': '请先配置条件判断环节',
    'pleaseLinkLastLink': '请先连接上一个环节',
    'lineConfig': '连线配置',
    'subFlowConfig': '子流程配置',
    'policyConfig': '决策条件配置',
    'conditionLinkConfig': '条件判断-环节配置',
    'contain': '包含',
    'notContain': '不包含',
    'conditionLink': '条件判断环节',
    'branchLink': '分支环节',
    'groupLink': '聚合环节',
    'subFlowLink': '子流程环节',
    'diySubFlow': '动态子流程',
    'stop': '停止',
    'physicalSite': '物理站点',
    'object': '对象',
    'linkStart': '环节开始',
    'linkEnd': '环节结束',
    'bigThan': '大于',
    'smallThat': '小于',
    'bigEqual': '大于等于',
    'smallEqual': '小于等于',
    'processMouldCode': '流程模板编码',
    'innerLoop': '内环回',
    'outerLoopBack': '外环回',
    'releaseLoop': '释放环回',
    'acyclicBack': '无环回',
    'SMSNotification': '短信通知',
    'subAlarm': '子告警',
    'hasCutover': '已割接',
    'processLinkName': '流程环节名称',
    'spendTime': '花费时间',
    'timeInterval': '时间间隔',
    'alarmNumber': '告警数量',
    'addUpgrade': '新增升级规则',
    'editUpgrade': '编辑升级规则',
    'copyUpgrade': '复制升级规则',
    'viewUpgrade': '查看升级规则',
    'collectionType': '采集类型',
    'alarmFor': '告警历时',
    'flashTime': '闪断时间',
    'failureTimes': '闪断次数',
    'alarmStateSynchronization': '告警状态同步',
    'serviceSearch': '服务查询',
    'updateAlarmFlag': '升级告警标识',
    'updateAlarmLevel': '升级告警级别',
    'intervalTimeInput': '请输入时间间隔',
    'alarmnumInput': '请输入告警次数',
    'faultDurationInput': '请输入告警历时',
    'brokenTimeInput': '请输入闪断时间',
    'brokenCountInput': '请输入闪断次数',
    'alarmidentifyInput': '请输入升级告警标识',
    'severityChoose': '请选择升级告警等级',
    'rootTermChild': '升级告警终止->终止故障告警',
    'childTermRoot': '故障告警终止->终止升级告警',
    'alarmUpgradeRule': '告警升级规则',
    'alarmNotZero': '告警历时、告警次数不能同时为0',
    'alarmThanFlash': '告警次数应大于等于闪断次数',
    'alarmNotZeroTwo': '告警历时、告警次数、闪断次数不能同时为0',
    'testRuleDesc': '测试规则说明',
    'TimeToBeTest': '待检测时间',
    'actualTestTime': '实际检测时间',
    'InvalidFormat': '格式错误',
    'mengTest': '我测试一条',
    'execPlanTime': '执行方案时间',
    'Advanced': '高级',
    'ctp': 'CTP',
    'taskWorkNo': '任务工单号',
    'reviewResults': '审核结果',
    'taskStatus': '任务状态',
    'ruleJson': '规则JSON',
    'filtrationCondition': '是否过滤条件',
    'addRule': '添加规则',
    'editRule': '编辑规则',
    'EnableOrDisable': '启用/禁用',
    'jsonInput': '请输入正确JSON数据',
    'redefineRuleConfig': '重定义规则配置',
    'rulesEntity': '规则实体',
    'codeChoose': '请选择主题',
    'ruleJsonInput': '请输入规则Json',
    'addRedefine': '新增重定义规则',
    'editRedefine': '编辑重定义规则',
    'copyRedefine': '复制重定义规则',
    'viewRedefine': '查看重定义规则',
    'theam': '主题',
    'redefineConfig': '请选择一条重定义规则配置',
    'redefineConfigMore': '请选择一条以上重定义规则配置',
    'redefineSet': '请配置一条重定义规则配置',
    'ruleEnDel': '规则实体无法删除',
    'viewRule': '查看规则',
    'alarmDefefine': '告警重定义规则',
    'ruleNameNo': '规则名称不能重复,请重新填写',
    'topo': '拓扑图',
    'failureRatio': '故障比率',
    'complaintVolume': '投诉单量',
    'processCoding': '流程编码',
    'ifTheTimeout': '是否超时',
    'timeConsuming': '耗时',
    'taskStartTime': '任务开始时间',
    'taskEndTime': '任务结束时间',
    'taskCoding': '任务编码',
    'latestCycle': '最新周期',
    'username': '用户名',
    'telephoneNumber': '电话号码',
    'searchAllSubtree': '搜索整颗子树',
    'fullName': '姓名',
    'organization': '组织机构',
    'whetherTheLeadership': '是否领导',
    'passwordUpdateTime': '密码更新时间',
    'lastLoginTime': '最后登录时间',
    'wholeOpen': '全网开放',
    'InternalUse': '系统内部使用',
    'roleInformation': '角色信息',
    'permissionsInformation': '权限信息',
    'associatedUsers': '关联用户',
    'userGroupName': '用户组名称',
    'userGroupAllName': '用户组全称',
    'userGroupCategory': '用户组类别',
    'parentUserGroup': '父用户组',
    'descriptiveInformation': '描述信息',
    'workConfirm': '作业确认',
    'faultRepairOrder': '故障工单',
    'timeOut': '超时',
    'processing': '处理中',
    'hang': '挂起',
    'theCPUAllocationRate': 'CPU分配率',
    'CPUUtilization': 'CPU使用率',
    'memoryAllocation': '内存分配率',
    'memoryUsage': '内存使用率',
    'storageAllocationRate': '存储分配率',
    'storageUsage': '存储使用率',
    'PCServer': 'PC服务器',
    'repairOrder': '工单',
    'orginalAlarmTime': '原始告警时间',
    'confirmAtLeastAlarm': '请确认勾选至少一个割接告警',
    'falutComp': '故障投诉',
    'RoamingRatio': '漫游比',
    'tableName': '表名',
    'dissipateTime': '消费时间',
    'messageID': '消息id',
    'cursor': '游标',
    'flag': '标识',
    'synchronizationTime': '同步时间',
    'checkMessage': '查看消息',
    'router': '路由器',
    'switches': '交换机',
    'firewall': '防火墙',
    'flowIn': '流入',
    'flowOut': '流出',
    'totalCPUCapacity': 'CPU总容量',
    'totalMemoryCapacity': '内存总容量',
    'totalStorageCapacity': '存储总容量',
    'checkTemplate': '验收模板',
    'checkItem': '验收项',
    'linkToDetails': '链接到详情',
    'totalAlarmEquipments': '有告警设备数',
    'totalPowerDownEquipments': '掉电设备数',
    'powerDownRatio': '掉电占比',
    'totalNEInterruptedEquipments': '网元中断设备数',
    'NEInterruptionRatio': '网元中断占比',
    'totalLinkInterruptedEquipments': '链路中断设备数',
    'otherAlarmEquipments': '其他告警设备数',
    'stick': '置顶',
    'startTimeChoose': '请选择开始时间',
    'endTimeChoose': '请选择结束时间',
    'loginName': '登录名',
    'affiliationOrganization': '归属组织',
    'phoneNumber': '手机号',
    'VoLTEVoice': 'VoLTE语音无线接通率',
    'VOLTEVoiceDrop': 'VOLTE语音掉话率',
    'exportBusinessFields': '输出到业务字段',
    'and': '并且',
    'or': '或者',
    'serviceOutput': '服务输出',
    'businessFieldsields': '业务字段',
    'policyGist': '决策依据',
    'branchName': '分支名称',
    'defaultBranch': '默认分支',
    'judgmentCondition': '判断条件',
    'businessData': '业务数据',
    'button': '按钮',
    'tacheNameNotNull': '环节名称不能为空',
    'tacheCodeNotNull': '环节编码不能为空',
    'branchNameCodeNotNull': '分支名称和编码不能为空',
    'bbuLayer': '基站图层',
    'meters': '米',
    'theDistanceIs': '距离是',
    'returnTemplate': '返参模板',
    'JudgmentWay': '判断方式',
    'serve': '服务',
    'groovy': 'groovy脚本',
    'scriptParameters': '脚本参数',
    'parameterNames': '参数名',
    'variableName': '变量名',
    'savePointSuccess': '坐标保存成功',
    'saveStyleSuccess': '样式保存成功',
    'notChange': '无变化',
    'changePwd': '修改密码',
    'oldPwd': '原密码',
    'password': '密码',
    'confirmPwd': '确认密码',
    'busiTypeAndBusiName': '业务类型/名称',
    'businessCode': '业务编码',
    'circuitCode': '电路编码',
    'waitingForReinsurance': '等待重保',
    'reinsurance': '重保',
    'hiddenDanger': '有隐患',
    'noHiddenDanger': '无隐患',
    'basicBusinessInfo': '业务基本信息',
    'businessDetailsInfo': '业务详情信息',
    'businessResourceTree': '业务资源树',
    'confirmOldPwd': '原密码错误，请确认',
    'pwdUnqualified': '密码强度不符合要求',
    'circuitStatus': '电路状态',
    'oldSameToComfirm': '确认密码必须与密码一致',
    'groovyInput': '请输入groovy脚本',
    'oneJudgment': '请先配置判断方法中第一行的数据',
    'contacts': '联系人',
    'completeJudgmentOne': '请填写完整判断方法中第',
    'completeJudgmentTwo': '行的数据',
    'responseDemoInput': '请输入返参模板',
    'circuitUse': '电路用途',
    'businessLevel': '业务级别',
    'circuitProtectionMode': '电路保护方式',
    'circuitMaintenanceLevel': '电路维护等级',
    'mainSpareType': '主备用类型',
    'circuitControlLevel': '电路管控等级',
    'businessNum': '业务编号',
    'circuitFeature': '电路特征',
    'startRentTime': '起租时间',
    'dispatchListLevel': '派单等级',
    'spareMode': '备用方式',
    'endRentTime': '退租时间',
    'businessFeature': '业务特征',
    'changeTime': '变更时间',
    'customerName': '客户名称',
    'qos': 'QOS',
    'isCenterNode': '是否中心点',
    'accType': '接入方式',
    'accServiceCode': '有线宽带业务号',
    'vpnNetworkNumber': 'VPN网号',
    'networkTopo': '组网拓朴',
    'groupServiceCode': '群电路业务号',
    'vpnRateIn': 'VPN速率（入）',
    'startAddress': '起始地址',
    'virtualRoadNumber': '虚拟路号',
    'vpnRateOut': 'VPN速率 (出)',
    'Threshold1': '阈值1',
    'Threshold2': '阈值2',
    'endAddress': '终止地址',
    'routeType': '路由类型',
    'configureThresholdOne': '请先配置完成判断方法中阈值1的数据',
    'configureVariableName': '请配置原始返回结果的变量名',
    'bpmnNotNull': 'bpmnXml不能为空',
    'changesStatistics': '变动统计',
    'editAssessmentSettings': '编辑考核设置信息',
    'businessChoice': '业务选择',
    'bulkChanges': '批量修改',
    'dateType': '日期类别',
    'typeMismatch': '参数的映射内容与原参数类型不一致',
    'setUp': '设置',
    'listOrderRule': '每次进入时依照此规则进行排序，先选择的规则优先排序',
    'ascending': '升序',
    'descending': '降序',
    'listOrderRemark': '注：重置后需再次保存后生效',
    'alarmSoundRule': '针对不同告警配置不同声音提醒',
    'Audition': '试听(当前)',
    'listOrder': '列表排序',
    'alarmSound': '告警声音',
    'originalSound': '原音效',
    'sound': '音效',
    'currentSound': '当前音效',
    'soundTip': '音效配置针对当前进行配置，点击音效进行试听。上传格式支持mp3、wma、wav,文件大小不超过10M,时长不超过1分钟',
    'sorting': '排序',
    'useSystem': '使用系统默认',
    'uploadFile': '上传文件',
    'sysSoundTip': '使用系统默认音效成功',
    'formatTip': '上传格式只支持mp3、wma、wav',
    'sizeTip': '文件大小不能超过10M，时长不能超过1分钟',
    'elasticCloudServer': '弹性云服务器',
    'managementMachine': '管理虚机',
    'dataNumTip': '系统不支持查看超10000条以后的记录，请通过筛选条件精确查询范围。',
    'localCity': '地市',
    'ip': 'IP',
    'accessEquipment': '途径设备',
    'functionalAuthority': '功能权限',
    'dataAccess': '数据权限',
    'role': '角色',
    'organizeEmployee': '组织员工',
    'defeatedNum': '失败任务数',
    'machineIP': '执行机器IP',
    'taskElapsedTime': '任务耗时',
    'roleName': '角色名称',
    'viewRole': '查看角色信息',
    'regionalAuthority': '区域权限',
    'resourceSelection': '资源选择界面',
    'timeUnit': '周期类型',
    'taskInstanceType': '任务实例类型',
    'netCate': '网元类别',
    'netStatus': '网元状态',
    'repNo': '请求号',
    'callpart': '调用方',
    'strucType': '指令类型',
    'resStatus': '结果状态',
    'returnBitNum': '返回字节数',
    'normalCtru': '普通指令',
    'hDangerCtru': '高危指令',
    'authCtru': '权限指令',
    'authority': '权限',
    'reqText': '请求报文',
    'cmdResult': '返回报文',
    'safetyLevel': '安全等级',
    'cmdCode': '指令编码',
    'forRecord': '转发记录',
    'reqSize': '请求报文大小',
    'respSize': '返回报文大小',
    'startReqT': '开始请求时间',
    'endReqT': '结束请求时间',
    'reqUseT': '请求用时',
    'currentConnNum': '当前连接数量',
    'integrity': '完整性',
    'executionTime': '执行时间',
    'fileNum': '文件数量',
    'dataNum': '数据条数',
    'installationRate': '完整率',
    'integrityThireshold': '完整率阈值',
    'fileName': '文件名称',
    'executeIP': '执行IP',
    'downLoadTime': '下载时间',
    'downLoadExpend': '下载耗时',
    'resolutionStartTime': '解析开始时间',
    'analysisExpend': '解析耗时',
    'intact': '完整',
    'noIntact': '不完整',
    'checkWay': '检测方法',
    'accessCode': '权限码',
    'resourceIdent': '资源标识',
    'resourceName': '资源名称',
    'department': '部门',
    'account': '账号',
    'email': 'Email',
    'parentNode': '父节点',
    'roleUser': '角色用户',
    'SiteName': 'Site Name',
    'resouceChoose': '请选择资源来源',
    'allSpecialty': '所有专业',
    'timeGranularity': '周期粒度',
    'totalNeNum': '网元总数',
    'connNeRate': '网元接通率',
    'equipmentLayer': '设备图层',
    'determineScript': '判断脚本',
    'outputInfomation': '输出信息',
    'treatment': '处理建议',
    'sendSingleIdentity': '派单标识',
    'determineScriptInput': '请输入判断脚本',
    'sendSingleIdentityChoose': '请选择派单标识',
    'resultEncoding': '结果编码不能为纯数字',
    'resultCoded': '结果编码已存在，请重新输入',
    'spread': '展开',
    'retract': '收回',
    'sceneManage': '场景管理',
    'storage': '存储',
    'exceptionName': '异常名称',
    'noEquipment': '暂无设备',
    'parentScene': '所属父场景',
    'detectionCycle': '检测周期',
    'subScene': '子场景',
    'ScenceDesc': '场景描述',
    'scene': '所属场景',
    'exceptionDesc': '异常描述',
    'mainScene': '主场景',
    'ruleManage': '规则管理',
    'abnormalManage': '异常原因管理',
    'sketchMap': '示意图',
    'analysisRules': '分析规则',
    'ruleDetails': '规则明细',
    'flowChart': '流程图',
    'allTypes': '所有类型',
    'objectCategory': '对象类别',
    'newRule': '新增规则',
    'suggestions': '建议内容',
    'newSubScene': '新增子场景',
    'programType': '程序类型',
    'programName': '程序名称',
    'processLogic': '处理逻辑',
    'waitingTime': '等待时间',
    'equipmentAlarm': '设备告警',
    'errorLog': '错误日志',
    'dymFlowConfig': '动态子流程配置',
    'configMode': '配置模式',
    'referenceTemplate': '入参模板',
    'parameterSettings': '入参设置',
    'newVariable': '新增变量',
    'processingState': '处理状态',
    'processLogDetails': '流程日志详情',
    'noFlowMsg': '暂无流程实例信息',
    'sourceType': '来源类型',
    'resultsProperties': '结果属性',
    'GroovyProperty': '返回结果属性/groovy参数',
    'RsetSource': '结果设置-原始返回结果中来源属性不能为空',
    'RsetDetection': '结果设置-检测结论中来源属性不能为空',
    'RsetDetectionBenchmark': '结果设置-检测结论中基准值不能为空',
    'RsetResultBenchmark': '结果设置-请求结果中基准值不能为空',
    'workDetails': '工单详情',
    'cutOverDetails': '割接详情',
    'relatedDetails': '关联详情',
    'lifeState': '生命状态信息',
    'dictionaryValue': '字典值查询异常',
    'causeProblem': '故障原因',
    'regionalName': '区域全称',
    'autoRefresh': '自动刷新',
    'refreshTime': '刷新时间',
    'configLink': '请先配置链接',
    'failureMessage': '失败信息',
    'lifeCycle': '生命周期',
    'onlyValue': '唯一值',
    'standardizeIdent': '标准化告警标识',
    'alarmSubtypes': '告警子类型',
    'alarmStandard': '告警标准化配置',
    'addAlarmStandard': '新增告警标准化配置',
    'editAlarmStandard': '编辑告警标准化配置',
    'copyAlarmStandard': '复制告警标准化配置',
    'viewAlarmStandard': '查看告警标准化配置',
    'pleaseAccNum': '请输入账号',
    'pleaseRoleName': '请输入角色名称',
    'pleaseRole': '请选择角色',
    'coordinateMissingTip': '坐标数据缺失，无法定位',
    'remoteIP': '远程IP',
    'toEndIP': '对端IP',
    'systemCode': '系统编码',
    'resend': '重发',
    'viewExceptionInfo': '查看异常信息',
    'alarmSource': '告警来源',
    'resendOrNot': '是否确认重发',
    'alarmNormal': '告警状态:正常',
    'alarmAbnormal': '告警状态:异常',
    'brokenAbnormal': '断站状态:异常',
    'brokenNormal': '断站状态:正常',
    'userCode': '用户编码',
    'staffName': '员工名称',
    'linkStatus': '连接状态:正常',
    'AlinkStatus': '连接状态:异常',
    'loginEquip': '登陆设备',
    'loginType': '登陆类型',
    'processConfSuccess': '流程配置成功',
    'acceptanceItemPlease': '请至少配置一个验收项',
    'Tstation': '台站',
    'maintainUnit': '维护单位',
    'sendOrdersMessage': '派单信息',
    'configFlow': '请配置流程',
    'effectBeginTime': '生效开始时间',
    'effectEndTime': '生效结束时间',
    'deviceId': '设备ID',
    'deviceManageIp': '设备管理IP',
    'isNetwork': '网络是否可达',
    'authePwd': '鉴权口令',
    'encryptionScheme': '加密模式',
    'encryptedString': '加密串',
    'connectivity': '连通性',
    'testTime': '测试时间',
    'loginWay': '登录方式',
    'loginPort': '登录端口',
    'loginAccount': '登录账号',
    'loginPwd': '登录口令',
    'snmpVersion': 'snmp版本',
    'snmpPort': 'snmp端口',
    'communityMsg': 'community信息',
    'networkLoginConfig': '网元登录配置',
    'snmpConfig': 'snmp协议配置',
    'alarmCreateTime': '综告创建时间',
    'isCurrentTime': '当前时间必须在选择的时间范围之内',
    'authenticationMode': '鉴权模式',
    'loginPortInput': '请输入登录端口',
    'loginAccountInput': '请输入登录账号',
    'loginPwdInput': '请输入登录口令',
    'snmpPortInput': '请输入snmp端口',
    'communityMsgInput': '请输入community信息',
    'authenticationModeChoose': '请选择鉴权模式',
    'authePwdInput': '请输入鉴权口令',
    'encryptionSchemeChoose': '请选择加密模式',
    'encryptedStringInput': '请输入加密串',
    'formExcel': '请上传正确格式的文件',
    'neConfig': '是否确认为可信网元',
    'isVirtual': '是否虚拟',
    'isVirtualChoose': '请选择是否虚拟',
    'snmpUserName': 'snmp用户名',
    'snmpUserNameInput': '请输入snmp用户名',
    'aStation': 'A端台站',
    'aMaintainUnit': 'A端维护单位',
    'aContactNumber': 'A端联系电话',
    'aOrganization': 'A端组织机构',
    'zStation': 'Z端台站',
    'zMaintainUnit': 'Z端维护单位',
    'zContactNumber': 'Z端联系电话',
    'zOrganization': 'Z端组织机构',
    'addressIp': '设备IP',
    'productSaleCode': '销售品编码',
    'ottName': 'OTT名称',
    'ottCode': 'OTT标识',
    'rgCode': 'RG编码',
    'fourProtocolType': '4层协议类型',
    'sourceIp': '源IP',
    'sourceMask': '源Mask',
    'sourceStartPort': '源起始端口',
    'sourceEndPort': '源结束端口',
    'destinationIp': '目的IP',
    'destinationMask': '目的Mask',
    'destinationStartPort': '目的起始端口',
    'destinationEndPort': '目的结束端口',
    'sevenProtocolType': '7层协议类型',
    'modifyStatus': '修改状态',
    'netMsg': '网元信息',
    'singleBack': '退单',
    'automated': '自动执行',
    'isShutDown': '是否关闭',
    'lock': '锁定',
    'unlock': '解锁',
    'startDelay': '启动延迟',
    'keywordInput': '请输入关键字并按回车查询',
    'ruleLogo': '规则标识',
    'batchLock': '批量锁定',
    'batchUnLock': '批量解锁',
    'empty': '为空',
    'notEmpty': '不为空',
    'changeTem': '改变条件会清空所选模板，是否继续?',
    'mustAlarm': '必须告警',
    'showLockAlarm': '显示锁定告警',
    'hideLockAlarm': '隐藏锁定告警',
    'copyCheckItem': '检查项复制',
    'inspectionTip': '没有选择对象，所有符合过滤条件的对象都将被添加巡检',
    'handleClosed': '工单关闭',
    'dispatch': '手动派单',
    'convergence': '汇聚',
    'parallel': '并行',
    'updatedUserName': '更新用户名',
    'requiredItemNotEmpty': '必填项不能为空',
    'updateBeforeSave': '请修改流程后保存',
    'saveBeforePublish': '请先保存流程后发布',
    'linkOutput': '环节输出',
    'linkInput': '环节输入',
    'queryFailed': '查询失败',
    'bbuType': 'BBU类型',
    'serviceType': '服务类型',
    'originalSystem': '原系统',
    'targetSystem': '目标系统',
    'requestWay': '请求方式',
    'InputOutput': '输入/输出',
    'ExceptionInfo': '异常信息',
    'output': '输出结果',
    'input': '输入',
    'outputing': '输出',
    'transferSideOdf': '传输侧ODF端口',
    'AtransferSideOdf': 'A端传输侧ODF端口',
    'ZtransferSideOdf': 'Z端传输侧ODF端口',
    'residualCapacity': '剩余容量',
    'connectionStatus': '连接状态',
    'disconnectTime': '断开连接时间',
    'aEndTranSidePort': 'A端传输侧端口',
    'zEndTranSidePort': 'Z端传输侧端口',
    'aSideBusinessSidePort': 'A端业务侧端口',
    'zSideBusinessSidePort': 'Z端业务侧端口',
    'aSideBusinessSideOdf': 'A端业务侧ODF端口',
    'zSideBusinessSideOdf': 'Z端业务侧ODF端口',
    'hierarchicalChannelList': '分层通道列表',
    'routingListAndCarry': '路由列表与承载电路',
    'ownerType': '拥有者类型',
    'schedulingType': '调度类型',
    'previousStep': '上一步',
    'nextStep': '下一步',
    'saveRoute': '保存路由',
    'jump': '跳转',
    'planConfirm': '方案确认',
    'pathSelect': '路径选择',
    'routeSave': '路由保存',
    'pleaseKeywords': '请输入关键词',
    'pleaseChoosePort': '请选择端口',
    'useRecommendedName': '使用推荐名称',
    'subnetConDir': '子网连接方向',
    'crossConDir': '交叉连接方向',
    'crossConType': '交叉连接类型',
    'subnetConType': '子网连接类型',
    'choosePort': '选择端口',
    'chooseNetOrPort': '待选网元/端口',
    'onlyObjects': '必经对象',
    'avoidObject': '避让对象',
    'pathObject': '路径对象',
    'aNetwork': 'A端网元',
    'zNetwork': 'Z端网元',
    'hop': '跳数',
    'sncRouteInfo': 'SNC路由信息',
    'sourcePort': '源端口',
    'hostPort': '宿端口',
    'crossConfigInfo': '交叉配置信息',
    'crossType': '交叉类型',
    'crossLinkaSide': '交叉链接A端',
    'crossLinkzSide': '交叉链接Z端',
    'existAvoidObject': '已选择数据，已存在避让对象中，请重新选择',
    'existOnlyObject': '已选择数据，已存在必经对象中，请重新选择',
    'confirmEdit': '是否确认修改',
    'aSiteChoose': '请选择A端站点',
    'aPortChoose': '请选择A端端口',
    'zSiteChoose': '请选择Z端站点',
    'zPortChoose': '请选择Z端端口',
    'rateChoose': '请选择速率',
    'circuitNameInput': '请输入电路名称',
    'sConnetDirChoose': '请选择子网连接方向',
    'sConnetTypeChoose': '请选择交叉连接方向',
    'crossConnetDirChoose': '请选择交叉连接类型',
    'crossConnetTypeChoose': '请选择子网连接类型',
    'topNChoose': '请选择topN',
    'NetworkRateInstruct': '无线网用户的网络平均下载速率',
    'BarangayQuantityInstruct': '无线站点覆盖Barangay数量',
    'PopulationCoverageInstruct': '无线站点覆盖人口数占总人口数的比例',
    '4GUserInstruct': '统计接入4G无线站点的用户数',
    '5GuserInstruct': '统计接入5G无线站点的用户数',
    'fiberOpticCableInstruct': '统计资源系统录入的光缆长度汇总值',
    'IPCoreNodeInstruct': '统计IP专业的核心节点设备数，包括以下设备类型：CR、PE、RR、E、IXR、MCE、NAT、BG',
    'OTNCoreNodeInstruct': '统计传输专业的OTN设备数',
    'OTNBackBoneRingInstruct': '统计IPRAN的B设备数指标',
    'VirtualMachineInstruct': '统计云池虚机数量',
    'cpuInstruct': 'CPU使用情况',
    'MemoryInstruct': '内存使用情况',
    'StorageInstruct': '存储使用情况',
    'fourSiteNumber': '4G站址数',
    'fiveSiteNumber': '5G站址数',
    'fourFlow': '4G的流量',
    'fiveFlow': '5G的流量',
    'fourConnectivity': '4G的连通率',
    'fiveConnectivity': '5G的连通率',
    'IPTrafficData': 'IP网流量数据',
    'pue': '能耗指标',
    'riskOperand': '风险操作数',
    'networkFailNum': '网络故障数',
    'measured': '投诉率',
    'alarmTotalInstruct': '统计传输专业所有未清除的告警',
    'alarmFaultOvertimeInstruct': '统计传输专业所有告警工单的超时率',
    'netRobustnessInstruct': '传输网设备的整体健壮性指标，通过判断传输的环网、同沟同缆的逻辑，计算出健壮性指标。',
    'healthTranInstruct': '传输网专业的巡检任务以设备汇总后巡检正常的比例，以体现传输网的整体健康度',
    'networkTranInstruct': '统计传输专业的网元告警总数',
    'fiberOpticCableAlarmInstruct': '统计光缆的告警总数',
    'submarineCableAlarmInstruct': '统计海缆的告警总数',
    'circuitAlarmInstruct': '统计电路的告警总数',
    'networkNumberInstruct': '统计传输专业的设备总数',
    'circuitNoInstruct': '统计传输电路的数量',
    'cableNoInstruct': '统计资源系统上录入的光缆总长度',
    'SubmarineCableNoInstruct': '统计海缆的总长度',
    'overWorkOrderInstruct': '统计传输专业已派发的工单中超时工单总数',
    'onWorkInstruct': '统计传输专业已派发的工单中在途工单总数',
    'hangWorkInstruct': '统计传输专业已派发的工单中挂起工单总数',
    'insNumInstruct': '统计传输巡检任务总数',
    'abINsInstruct': '统计传输巡检任务异常总数',
    'openInstruct': '统计开通电路工单总数',
    'adjustInstruct': '统计拆除电路工单总数',
    'demolitionInstruct': '统计调整电路工单总数',
    'cutOverWaitInstruct': '统计风险操作中待执行任务数',
    'cutOveringInstruct': '统计风险操作中执行中任务数',
    'cutOveredInstruct': '统计风险操作中已完成任务数',
    'storageUtilizationIndicator': '存储使用率指标',
    'hostNumber': '主机数量',
    'virtualMachinesNum': '虚拟机数量',
    'storageUnitsNumber': '存储机数量',
    'routersNumber': '路由器数量',
    'switchesNum': '交换机数量',
    'firewallsNumber': '防火墙数量',
    'totalCpuCapacity': '总的CPU容量',
    'cldSeriousAlarm': '云网严重告警数',
    'cldImpAlarm': '云网重要告警数',
    'cldSecAlarm': '云网次要告警数',
    'letNumInstruct': '呈现LTE BBU设备数/RRU设备数',
    'fiveNumInstruct': '5G BBU设备数/AAU设备数',
    'wirSiteNum': '无线站址数',
    'showLteAlarm': '呈现LTE告警数',
    'stationBreakNumberInstruct': 'LTE断站数/5G断站数',
    'workingOdd': '工单数',
    'fiveBusyInstruct': '本周期/上月同期5G超忙小区数(上月同期)',
    'fourBusyInstruct': '本周期/上月同期4G超忙小区数(上月同期)',
    'hierarchicalTopo': '分层路由拓扑',
    'pathSelectTipOne': '是否保存当前路径选择信息',
    'pathSelectTipTwo': '尚未选择路径信息，是否直接进入下一步',
    'createActivateCross': '创建并激活交叉',
    'autoDiscoveryPath': '自动发现路径',
    'getSncName': '获取SNC信息',
    'editSnc': '修改SNC信息',
    'executionNum': '执行中任务数',
    'completedNum': '执行完成任务数',
    'plan': '方案',
    'missPortPrompt': '缺失A,Z端端口数据',
    'sncNameInput': '请输入SNC名称',
    'ctpPortChoose': '请选择CTP端口数据',
    'portDifPrompt': 'AZ端口不能一样',
    'siteDifPrompt': 'AZ端站点不能相同',
    'sncLogo': 'Snc标识',
    'sncLogoInput': '请输入Snc标识',
    'isPlanConfig': '是否进行方案确认?',
    'stationTopo': '站内拓扑',
    'string': '字符串',
    'array': '列表',
    'number': '数值',
    'bool': '布尔值',
    'routeCalc': '是否进行路由计算?',
    'cableLength': '光缆长度',
    'planTime': '计划时间',
    'impactOnThePhilippines': '对菲律宾的影响',
    'estimatedTimeArrival': '预计到达时间',
    'currentWindSpeed': '当前风速',
    'currentWindLevel': '当前风力级别',
    'planningArea': '规划区域',
    'infomation': '信息',
    'calculating': '计算中',
    'siteStatus': '站点状态',
    'energy': '能量',
    'energySaving': '节能模式',
    'areaNetDetail': '区域网元详情',
    'implement': '执行',
    'recover': '恢复',
    'createTask': '创建任务',
    'theAreaName': '影响区域名称',
    'theTyphoonRadius': '台风半径',
    'selectTyphoon': '选择台风',
    'autoDraw': '自动绘制',
    'typhoonName': '台风名称',
    'infomationSource': '信息来源',
    'radiusInput': '请输入半径',
    'addCostomArea': '添加影响区域',
    'isSncName': '是否修改SNC名称',
    'ipName': 'IP名称',
    'aPortIp': 'A端IP',
    'zPortIp': 'Z端IP',
    'aPortEquName': 'A端设备名称',
    'aPortName': 'A端端口名称',
    'zPortEquName': 'Z端设备名称',
    'zPortName': 'Z端端口名称',
    'packetLoss': '丢包数',
    'portError': '端口误码信息',
    'currentInflowRate': '当前流入速率',
    'currentOutflowRate': '当前流出速率',
    'InflowBandwidth': '流入带宽利用率',
    'outflowBandwidth': '流出带宽利用率',
    'flowPattern': '流量波动图',
    'circuitIndicators': '电路指标',
    'circuitTopo': '电路拓扑',
    'iduNumber': 'IDU数量',
    'linkNumber': '链路数量',
    'bandwidthLoad': '带宽负荷',
    'electricFrequency': '电频值',
    'coreIndex': '核心指标',
    'perforAlertConfig': '性能告警配置',
    'trunk': '中继',
    'sceneNameIsRequired': '场景名称是必填项',
    'planTimeIsRequired': '计划时间是必填项',
    'estimatedLandingTimeIsRequired': '预计登录时间是必填项',
    'currentWindSpeedIsRequired': '当前风速是必填项',
    'currentWindScaleIsRequired': '当前台风等级是必填项',
    'areaNameIsRequired': '区域名称是必填项',
    'drawAreaIsRequired': '必须手动添加新的影响区域',
    'selectTyphoonIsRequired': '必须选择关联台风',
    'raduisIsRequired': '台风半径必须填写',
    'newSceneSuccess': '新的场景添加成功',
    'editSceneSuccess': '场景信息编辑成功',
    'newAreaSuccess': '新的影响区域添加成功',
    'editAreaSuccess': '影响区域信息编辑成功',
    'linkTyphoonSuccess': '关联台风成功',
    'linkTyphoonFail': '关联台风失败',
    'typhoonIsSelected': '该台风已被其它场景关联',
    'typhoonList': '台风列表',
    'sceneList': '场景列表',
    'newTyphoon': '新的台风',
    'newScene': '新的场景',
    'clickHereAddNewArea': '点击这里自定义添加可能影响的区域',
    'energySaveingIsOpen': '节能模式开启',
    'energySaveingIsClose': '节能模式关闭',
    'clickToStartDrawing': '点击开始绘画',
    'perssToStartReleaseToFinish': '点击添加图形，或者按住鼠标拖动开始绘画，松开鼠标结束绘画',
    'doubleClickToComplete': '双击完成绘画操作',
    'clickToContiuneDrawing': '点击继续绘画',
    'commandTaskNumber': '预案工单',
    'urgencyLevel': '缓急',
    'securityLevel': '保密级别',
    'whetherAssess': '是否考核',
    'requiredCompletionDate': '要求完成时间',
    'title': '标题',
    'commandTaskSaveSuccess': '预案工单保存成功',
    'commandTaskSaveFail': '预案工单保存失败',
    'titleIsRequired': '标题是必填项',
    'requiredCompletionIsRequired': '要求完成时间是必填项',
    'productionCommand': '生产任务',
    'meetingNotification': '会议通知',
    'urgent': '紧急',
    'confidential': '私密',
    'urgencyLv': '缓急级别',
    'abnormalObjectNum': '异常对象总数',
    'ruleSyncAlarm': '告警同步规则',
    'addRuleSyncAlarm': '新增告警同步规则',
    'editRuleSyncAlarm': '编辑告警同步规则',
    'otnAlarmTips': '修改OTN电路配置信息时，会将当前OTN电路进行删除后再创建，电路删除后将可能会影响到业务使用，请注意！',
    'otnChangeConfirm': '基本信息已经变化，确定是否进行调整',
    'alarmReverse': '告警反转',
    'cancelAlarmReverse': '取消告警反转',
    'hasCircuitTip': '电路已存在，是否查看？',
    'continueCircuitTip': '有未完成的开通电路任务，是否跳转继续？',
    'sceneActionSuccessRate': '场景执行成功率',
    'today': '今天',
    'thisWeek': '本周',
    'thisMonth': '本月',
    'actionSuccessRate': '执行成功率',
    'manuallyCreate': '手工创建',
    'operResult': '操作结果',
    'sensitiveOperations': '敏感操作',
    'executeTip': '此操作将执行预案, 是否继续',
    'recoverTip': '此操作将恢复已经执行的预案，是否继续',
    'planCount': '预案统计',
    'planActionNumTop5': '预案执行次数对象TOP5',
    'softwareVer': 'OS版本',
    'roomName': '机房名称',
    'currentAlarmInfo': '实时告警信息',
    'planInfo': '预案信息',
    'planStatus': '预案状态',
    'isHasAlarm': '是否告警',
    'sceneName': '场景名称',
    'actionNum': '执行次数',
    'currentLinkInfo': '链路实时情况',
    'linkGroup': '链路组',
    'logInfo': '日志信息',
    'portStatus': '端口状态',
    'errorPackageNum': '错包数',
    'inFlow': '输入流量',
    'outFlow': '输出流量',
    'linkRate': '链路利用率',
    'otnOccupationTips': '当前A/Z端已被占用，未完成开通的电路可以继续开通或者取消，已完成的电路可以取消开通。',
    'circuitList': '电路列表',
    'cancelTip': '取消操作，是否继续',
    'plansConfirm': '预案确认',
    'inUseRate': '输入利用率',
    'outUseRate': '输出利用率',
    'isWrongPackage': '是否错包',
    'batteryCapacity': '电池容量',
    'ConfiguringBackups': '配置备份',
    'backupLog': '备份日志',
    '4GuserMigration': '4G用户迁移',
    '5GuserMigration': '5G用户迁移',
    'migrationPercent': '迁移进度',
    'initUserInfo': '初始用户信息',
    'currentUserInfo': '当前用户信息',
    'isolationFailure': '隔离故障',
    'openMMEAbility': '开启MME接入能力',
    'openAMFAbility': '开启AMF接入能力',
    'ReleaseMEEAndAMFQuarantine': '解除MEE/AMF隔离'
}