/* eslint-disable no-template-curly-in-string */
export default {
    'common': {
        'errorRow': 'The {0} line',
        'addTitle': 'Add {0}',
        'viewTitle': 'View {0}',
        'editTitle': 'Edit {0}',
        'enableSuccess': 'Enable Success',
        'prohibitSuccess': 'Disable Success',
        'dataHandle': 'Data Processing...',
        'config': 'Configuration',
        'required': '{0} is required',
        'exists': '{0} Exists',
        'number': '{0} must be number',
        'integer': '{0} must be integer',
        'positiveInteger': '{0} must be positive integer',
        'nonnegativeInteger': '{0} must be non-negative integer',
        'largeThenMin': '{0} maximum value must be greater than or equal to minimum value',
        'isEquired': ' is required',
        'invalid': ' is invalid',
        'isExit': ' is exist',
        'isNumber': ' must be number',
        'isDel': '{0} has been associated and can not be deleted!',
        'enLetter': 'English letters, numbers, underscores',
        'delDesc': 'Are you sure to delete {0}?',
        'startEndDesc': '{0} must be greater than {1}',
        'batchDeleteTip': 'Please check one record first!',
        'selectObj': 'Check record first',
        'configNet': 'Config NE',
        'configEms': ' Config EMS',
        'input': 'Input',
        'output': 'Output',
        'impSuccess': 'Import Succeeded',
        'impFailure': 'Import Failure',
        'excelFileLimit': 'Only xls/xlsx format files can be uploaded',
        'excelSizeLimit': 'The size of uploaded excel can not exceed',
        'excelError': 'Upload excel processing error'
    },
    'datatypemgr': {
        'entityName': 'Data Type',
        'relateDataError': 'Data Type has been associated and can not be deleted!',
        'duplicateName': 'Data Type Name already exists! ',
        'legalFileSizeMinNumber': 'Legal file size minimum must be numeric and greater than or equal to 0, and the number of decimal places to retain up to 3 digits',
        'legalFileSizeMaxNumber': 'Legal file size maximum must be numeric and greater than or equal to 0, and the number of decimal places to retain up to 3 digits',
        'legalFileSizeMin': 'Legal File Size minimum value',
        'legalFileNumMin': 'Number of Legal Files minimum value',
        'numberOfLegalRecordsMin': 'Number of Legal File Records minimum value',
        'legalFileSizeMax': 'Legal File Size maximum value',
        'legalFileNumMax': 'Number of Legal Files maximum value',
        'numberOfLegalRecordsMax': 'Number of Legal File Records maximum value',
        'viewIndicatorItem': 'View Indicator Item',
        'configGroup': 'Configure Indicator Group',
        'addGroup': 'Add Indicator Group'
    },
    'datagroupmgr': {
        'entityName': 'Consolidated Indicator Type',
        'mergeTypeName': 'Consolidated Indicator Type',
        'duplicateName': 'Merge Type Name already exists!',
        'targetCount': 'Indicator',
        'consolidatedIndicatorTypeName': 'Consolidated Indicator Type Name',
        'delgroup': 'Are you sure to remove this indicator group and its items?',
        'delgitem': 'Are you sure to remove this indicator item？'
    },
    'mergetypemgr': {
        'entityName': 'Consolidated Indicator Type',
        'mergeTypeName': 'Consolidated Indicator Type',
        'duplicateName': 'Merge Type Name already exists!',
        'targetCount': 'Indicator',
        'consolidatedIndicatorTypeName': 'Consolidated Indicator Type Name',
        'delgroup': 'Are you sure to remove this indicator group and its items？',
        'delgitem': 'Are you sure to remove this indicator item？',
        'datafieldRequired': 'Merge index list can not be empty!'
    },
    'taskmodelmgr': {
        'entityName': 'Task Model',
        'taskType': 'Task Type',
        'taskModelName': 'Task Model Name',
        'defaultAppId': 'Default Execution Program',
        'duplicateName': 'Task Model Name already exists!',
        'relateDataError': 'This task model has been associated and can not be deleted!'
    },
    'taskmgr': {
        'allSpecialities': 'All Specialities',
        'operator': 'Operator',
        'operateRecord': 'Operation Record',
        'entityName': 'Task',
        'topNode': 'Task Manage',
        'periodType': 'Cycle Type',
        'taskModelName': 'Task Model Name',
        'defaultAppId': 'Default Execution Program',
        'isConcurrent': 'Execution Mode',
        'isConcurrentDesc': 'Whether the current task circle is allowed to be executed, when the last task circle is not finished',
        'timeout': 'Overdue Threshold',
        'second': 'Second (s) Interval',
        'minute': 'Minute (s) Interval',
        'hours': 'Hour (s) Interval',
        'timeoutDesc': '0 : No Limitation',
        'periodConfig': 'Execution Circle Configuration',
        'completeNotice': 'End Notification',
        'completeNoticeInterface': 'Notification Interface',
        'executeTask': 'Execute Task',
        'chooseTaskModel': 'Please select a task model before adding a new task!',
        'triggerDesc': 'Are you sure to execute the task now?',
        'triggerStartDesc': 'Are you sure you want to enable this task?',
        'triggerIsdDesc': 'Are you sure you want to disable this task?',
        'existRunningTask': 'This task is running!',
        'periodSecond': 'Seconds of Execution Cycle',
        'periodMinute': 'Minutes of Execution Cycle',
        'periodHour': 'Hours of Execution Cycle',
        'everyHour1': 'Hour of Execution Cycle',
        'everyMinute1': 'Minute of Execution Cycle',
        'everyDay': 'Week of Execution Cycle',
        'everyDay2': 'Daily',
        'everyWeek': 'Weekly',
        'sun': 'Day',
        'everyMonth': 'Monthly,the',
        'everyHour2': 'Hour of Execution Cycle',
        'everyMinute2': 'Minute of Execution Cycle',
        'everyDate': 'day of Execution Cycle',
        'everyHour3': 'Hour of Execution Cycle',
        'everyMinute3': 'Minute of Execution Cycle',
        'duplicateName': 'Task name already exists'
    },
    'emsmgr': {
        'entityName': 'EMS Name',
        'emsType': 'EMS Type',
        'editEms': 'Edit EMS',
        'addEms': 'Add EMS',
        'viewEms': 'View EMS',
        'emsNameRequired': 'EMS name is required',
        'emsTypeRequired': 'EMS type is required',
        'auasRequired': 'Code is required',
        'manufacturerRequired': 'Manufacturer is required',
        'auasIsExit': 'Code is exist',
        'auasIsNumber': 'Code must be numbers',
        'emsNameIsExit': 'EMS name is exist',
        'ipAddressRequired': 'IP address is invalid',
        'editNet': 'Edit NE',
        'addNet': 'Add NE',
        'viewNet': 'View NE',
        'entityNameinvalid': 'EMS name is invalid',
        'IpAddressrequired': 'IP address is required'
    },
    'emginfomgr': {
        'downImportModule': 'Download Template',
        'netSeries': 'NE Series',
        'friendlyName': 'Friendly Name',
        'office': 'Office Direction',
        'spareIpAddress': 'Alternate IP Address',
        'officelength': 'Allow 10 characters',
        'netNamerequired': 'NE name is required',
        'friendlyNamerequired': 'Friendly name is required',
        'spareIpAddressinvalid': 'Alternate ip address is invalid',
        'friendlyNameisExit': 'Friendly name is exist',
        'netNameisExit': 'NE name is exist',
        'connIsExitNotDel': 'The NE has connection, please delete it first from OSS Config->NE Connect config'
    },
    'ipsegmentmgr': {
        'ipRange': 'IP Range',
        'addressSegment': 'Address Segment',
        'showIpSegment': 'View IP Network Segment',
        'editIpSegment': 'Edit IP Network Segment',
        'segmentName': 'Network Segment Name',
        'ipaddressSegment': 'IP Address Segment',
        'netMask': 'Mask bit',
        'delDesc': 'Are you sure to delete this IP network segment',
        'start': ' Start',
        'end': ' End',
        'segmentNamerequired': 'Network segment name is required',
        'segmentNameinvalid': 'Network segment name is invalid',
        'arearequired': 'Region is required',
        'ipaddressSegmentrequired': 'IP address segment is required',
        'netMaskrequired': 'Mask bit is required',
        'ipRangestartisEquired': 'IP range start is required',
        'ipRangeendisEquired': 'IP range end is required',
        'ipRangestartisinvalid': 'IP range start is invalid',
        'ipRangeendisinvalid': 'IP range end is invalid',
        'ipaddressSegmentinvalid': 'IP address segment is invalid',
        'segmentNameisExit': 'Network segment name already exists',
        'ipaddressSegmentisExit': 'IP address segment already exists'
    },
    'interfacetypemgr': {
        'connProtocol': 'Connection Protocol',
        'interfaceProtocol': 'Interface Protocol',
        'showInterfacetype': 'View Interface Type',
        'editInterfacetype': 'Edit Interface Type',
        'interfacetypeName': 'Interface Type Name',
        'sessionComponent': 'Session Component',
        'addConfig': 'Add Parameter',
        'paramName': 'Parameter Name',
        'paramFlag': 'Parameter ID',
        'paramType': 'Parameter Type',
        'isNull': 'Nullable or Not',
        'editParma': 'Edit Parameter',
        'showParma': 'View Parameter',
        'isMulSelect': 'Multi-select or not',
        'separator': 'Splitter',
        'paramSourceType': 'Parameter Source Type',
        'paramSource': 'Parameter Source',
        'relationObjType': 'Association Object Type',
        'isPassSave': 'Encrypted Stored or Not',
        'defaultValue': 'Default Value',
        'paramformat': 'Parameter Format',
        'maxLength': 'Max Size',
        'paramformatDesc': 'Regular expression, empty means no restriction',
        'delConfigDesc': 'Are you sure to delete this parameter',
        'close': 'Close',
        'is_numeric': 'Numeric or not',
        'is_multi_row': 'Multiple rows or not',
        'inputType': 'Input Box Type'
    },
    'interfacemgr': {
        'interfacetype': 'Interface Type',
        'showInterface': 'View Interface',
        'editInterface': 'Edit Interface',
        'interfaceName': 'Interface Name',
        'instructionSet': 'Instruction Set',
        'logOffCmd': 'Log Out NE Command',
        'maximumConnection': 'Total Maximum Connections',
        'timeOut': 'Connection Timeout Period',
        'emptyTimeOut': 'Connection Idle Timeout Period',
        'strCode': 'Character Code',
        'connConfig': 'Connection Configuration',
        'paramValue': 'Parameter Value',
        'commParam': 'Common Parameter',
        'selection': 'Selected',
        'notSpecify': 'Not Specify',
        'specify': 'Specify',
        'registerNeCmd': 'Log NE Command',
        'maxConnTip': '0 : No Limitation'
    },
    'connectmgr': {
        'interface': 'Interface',
        'showConnect': 'View Connection',
        'editConnect': 'Modify Connection',
        'connectName': 'Connection Name',
        'specifyTheNe': 'Specify NE or not',
        'neEms': 'NE/EMS'
    },
    'shieldplanmgr': {
        'shieldplanName': 'Shield Plan Name',
        'shieldplanNum': 'Number of Shield NEs',
        'planName': 'Plan Name',
        'planStatus': 'Plan State',
        'notHand': 'Not Started',
        'handing': 'Ongoing',
        'theEnd': 'Ended',
        'shieldTime': 'Shield Period',
        'addNet': 'Add NE',
        'importNet': 'Import NE',
        'exportNet': 'Export NE',
        'localNetwork': 'Local Network',
        'largeArea': 'ORG Region',
        'cutNumber': 'Migration Order Number',
        'shieldplanList': 'Shield NE List',
        'selectDesc': 'Please check the ne record!',
        'delDesc': 'Are you sure to remove the selected NE?',
        'shieldplan': 'NE Shield Plan',
        'shieldplanNamerequired': 'Shield plan name is required',
        'shieldplanNameisExit': 'Shield plan name already exists',
        'shieldTimestartrequired': 'Shield period start date is required',
        'shieldTimeendrequired': 'Shield period end date is required',
        'shieldplanEndStart': 'The start time of shield plan can not be greater than the end time',
        'shieldplanDelDesc': 'Are you sure to delete the NE shield plan?'
    },
    'instructsetmgr': {
        'showInstructset': 'View Instruction Set',
        'editInstructset': 'Edit Instruction Set',
        'instructsetName': 'Instruction Set Name',
        'instructSubmitter': 'Submission Identifier',
        'commandPrompt': 'Prompt',
        'terminatInstruct': 'Terminator',
        'resultBlock': 'Result Block Identifier',
        'excEnd': 'End of Execution Identifier',
        'excNormal': 'Normal Execution Identifier',
        'excExcept': 'Execution Exception Identifier',
        'clearResultContent': 'Clear the content in the instruction result',
        'timeOut': 'Overdue Threshold',
        'second': 'Second',
        'interactiveConfig': 'Interaction Configuration',
        'interactiveInfo': 'Interaction Information',
        'operinfo': 'Operation Information',
        'matchFlag': 'Match ID',
        'sendContent': 'Sending Content'
    },
    'instructmouldmgr': {
        'ordinary': 'Ordinary',
        'jurisdiction': 'Privilege',
        'highRisk': 'High-risk',
        'instructFalg': 'Instruction ID',
        'addInstructmould': 'Add Instruction Template',
        'instructmouldName': 'Instruction Template Name',
        'safetyGrade': 'Security Level',
        'classLabel': 'Classification Tag',
        'analyticalTemplate': 'Parsing Template',
        'instructmouldDesc': 'Instruction Template Description',
        'whiteBill': 'White List',
        'blackBill': 'Blacklist',
        'networkInfo': 'NE Information',
        'selectInstructmould': 'Please select instruction template',
        'setupCmdBw': 'White and Blacklist configuration'
    },
    'instructmgr': {
        'instructmould': 'Instruction Template',
        'showInstruct': 'View Instruction',
        'editInstruct': 'Edit Instruction',
        'instructText': 'Instruction Script',
        'paramSourceScript': 'Parameter Source Script',
        'paramSourceScriptLink': 'Parameter Source Database Connection',
        'typeExcResult': 'Execution Result Judgement Type',
        'typeExcResultSql': 'Execution Result Judgement Script',
        'typeExcResultSqlLink': 'Execution Result Judgement Database Connection',
        'retryCount': 'Total Retries',
        'second': 'Second',
        'times': 'Times',
        'reInterval': 'Duration of Reconnection Interval',
        'millisecond': 'Millisecond',
        'isResultOutParam': 'Use last jump result as input parameter or not',
        'operConfig': 'Operation Configuration'
    },
    'analysismouldmgr': {
        'linkInstructSet': 'Instruction Set',
        'editAnalysismould': 'Edit Parsing Template',
        'showAnalysismould': 'View Parsing Template',
        'analysismouldName': 'Parsing Template Name',
        'relateDataError': 'Parsing template has been associated and can not be deleted!',
        'analysisMethod': 'Parsing Method',
        'groovySql': 'Groovy Script',
        'startFlag': 'Parsing Start ID',
        'endFlag': 'Parsing End ID',
        'analyticReg': 'Parsing Regex',
        'analyticConfig': 'Parsing Configuration'
    },
    'instructitemmgr': {
        'code': 'Virtual Instruction Item Code',
        'name': 'Virtual Instruction Item Name',
        'edit': 'Edit Virtual Instruction Item',
        'show': 'View Virtual Instruction Item',
        'version': 'Version Number',
        'in': 'Medium',
        'commonly': 'General',
        'reverseInstruct': 'Reverse Instruction',
        'selectedInstruct': 'To Be Selected Instruction',
        'editReverse': 'Reverse Instruction Configuration',
        'showReverse': 'View Reverse Instruction',
        'reverseInstructName': 'Reverse Instruction Name',
        'historyVersionList': 'Version Record',
        'versionStatus': 'Version Status',
        'inuseVersion': 'In Use Version',
        'historyVersion': 'History Version',
        'setInuseVersion': 'Set as In Use Version',
        'setInuseVersionTip': 'Are you sure to set this version as the in use version?',
        'parserScript': 'Parsing Script',
        'duplicateName': 'Virtual instruction item name already exists, can not be the same'
    },
    'virtualinstructmgr': {
        'code': 'Virtual Instruction Code',
        'name': 'Virtual Instruction Name',
        'edit': 'Edit Virtual Instruction',
        'show': 'View Virtual Instruction',
        'itemList': 'Virtual Instruction Item List',
        'shieldplanList': 'Shield NE List',
        'toBeSelectedInstructItem': 'To Be Selected Virtual Instruction Item',
        'virtualCmdType': 'Virtual Instruction Type'
    },
    'instructsafemgr': {
        'selectNetElement': 'NE list can not be empty!',
        'selectInstructTempl': 'Instruction list can not be empty!'
    },
    'stdfield': {
        'viewTitle': 'View Field',
        'editTitle': 'Edit Field',
        'fieldName': 'Field Name',
        'fieldCode': 'Field Code',
        'fieldType': 'Field Type',
        'inputWay': 'Enter Position',
        'fieldLevel': 'Application Scene',
        'isAutoRltService': 'Add to service or not',
        'fieldDemo': 'Sample',
        'remark': 'Description',
        'leftMenuTypeInfo': 'Type Information',
        'leftMenuDemoInfo': 'Sample Description',
        'attrConfig': 'Attribute Configuration',
        'attrName': 'Attribute Name',
        'attrCode': 'Code',
        'attrType': 'Type',
        'addAttr': 'Add Attribute',
        'fieldNameRequired': 'Field name is required',
        'fieldCodeRequired': 'Field code is required',
        'fieldCodePatternError': 'Field code can only be entered in English, number, underline, and can only start in English',
        'fieldTypeRequired': 'Field type is required',
        'inputWayRequired': 'Enter position is required',
        'fieldLevelRequired': 'Application scene is required',
        'attrNameRequired': 'Attribute name is required',
        'attrCodeRequired': 'Attribute code is required',
        'attrCodePatternError': 'Attribute code can only be entered in English, number, underline, and can only start in English',
        'attrTypeRequired': 'Attribute type is required',
        'delAttrDescWithChild': 'Are you sure to remove this attribute and its subattribute?',
        'delAttrDescWithoutChild': 'Are you sure to remove this attribute?',
        'duplicateName': 'Field code already exists!'
    },
    'microservice': {
        'cmptAlias': 'Microservice Name',
        'cmptName': 'Microservice Code',
        'microDescription': 'Microservice Description',
        'msAppServicesSum': 'Total of APIs',
        'instNum': 'Total of running instances',
        'cmptCode': 'Service Code',
        'cmptStatus': 'Service Status',
        'cmptFlag': 'Service Tag',
        'cmptView': 'Microservice Details',
        'apiList': 'API List',
        'runObj': 'Running Instance',
        'errorParam': 'Error Code Reference',
        'openPort': 'Open Port',
        'srcLink': 'Resource Pool',
        'mirrorImage': 'Image',
        'environmentVariable': 'Environment Variable',
        'pathMapping': 'Path Mapping',
        'readyProbe': 'Ready Probe',
        'apiName': 'API Name',
        'intfAddr': 'Interface Address',
        'retrunFmat': 'Response Format',
        'postMentch': 'Request Mode',
        'instanceName': 'Instance Name',
        'host': 'Mainframe',
        'image': 'Image',
        'status': 'Status',
        'readyRestart': 'Ready/Reboot',
        'startTime': 'Startup Time',
        'errorCode': 'Error Code',
        'description': 'Description',
        'apiView': 'API Details',
        'microService': 'Micro Service',
        'requestParameters': 'Request Parameter',
        'returnResult': 'Response Result',
        'objectExample': 'Object Sample',
        'returnToExample': 'Response Sample',
        'isRequired': 'Is required or not?',
        'hostPath': 'Host',
        'mountPath': 'In'
    },
    'serviceaudit': {
        'attrTreeCodeMess': 'You can only input letters or numbers with letters, and you can use short lines and underscores in the middle',
        'viewAudit': 'Service Audit',
        'approve': 'Agree',
        'refuse': 'Reject',
        'apply': 'Application',
        'audit': 'Audit',
        'chargeAudit': 'Head of Department Audit',
        'platAudit': 'Platform Audit',
        'complete': 'Complete',
        'seq': 'Times',
        'dsh': 'To Be Audited',
        'ywc': 'Audit Pass',
        'yjj': 'Rejected',
        'thisAudit': 'This Audit',
        'auditRecord': 'Audit Record',
        'auditOpinion': 'Audit Opinion',
        'expireTime': 'Expire Time',
        'issueApply': 'Issue Apply',
        'offlineApply': 'Offline Apply',
        'callApply': 'Call Apply',
        'applyTime': 'Apply Time',
        'applyUser': 'Apply User',
        'auditOpinionRequired': 'Please select audit opinion',
        'expireTimeRequired': 'Please select expiration time',
        'expireTimeLessNow': 'Expiration time can not be less than current time',
        'auditFailureServiceMsg': 'Audit Failure and Service Failure'
    },
    'myService': {
        'servDesc': 'Service Description',
        'myFavList': 'My Collection',
        'myApplyList': 'My Invocation',
        'myPubList': 'My Publish',
        'applyPage': ' Service Call Request ',
        'pubPage': 'Service Publish Request',
        'applyRecord': 'Call Record',
        'platAudit': 'Platform Audit',
        'ypub': 'Published',
        'ystop': 'Suspended',
        'yoffline': 'Offline',
        'ycancel': 'Cancelled',
        'djoint': 'Not Joint Debug',
        'yjoint': 'Debugged',
        'registered': 'Registered',
        'confirmCancelCollectOrNot': 'Cancel collection or not?',
        'confirmCancelOrNot': 'Cancel service or not?',
        'duration': 'Duration',
        'invokestatus': 'Call Status',
        'invoderesult': 'Call Result',
        'heartabnormal': 'Abnormal Heartbeat',
        'onlinesuccess': 'Online application submitted successfully, please wait for the review to be completed',
        'jointoroffline': 'Only the joint debugged or offline services are allowed to be online',
        'offlineandcancel': 'Only offline services are allowed to log out',
        'cancelsuccess': 'Log out successfully',
        'suspendandoffline': 'Only suspended services can be offline',
        'offlinesuccess': 'Offline application submitted successfully, please wait for approval to complete',
        'cancelService': 'Cancel Service',
        'onlinecallapply': 'Only online services are allowed to call applications',
        'senderDetail': 'Sender Detail',
        'senderCode': 'User Name',
        'authType': 'authType',
        'requestRateType': 'Request Rate Type',
        'requestRateNum': 'Request Rate Num',
        'requestTraffType': 'Request Traff Type',
        'requestTraffValue': 'Request Traff Value(MB)',
        'ips': 'Total Access IPs',
        'delIpDesc': 'Delete the selected IP or not',
        'dataShared': 'Data Shared',
        'serviceApply': 'Service Apply',
        'configParam': 'Config Param',
        'senderUser': 'Send User',
        'ipsNumError': 'Ips num retain one ip',
        'serviceImportMethod': 'Step 1: select service import mode',
        'serviceTextImport': 'Step 2: enter the service text',
        'serviceFileImport': 'Step 2: select file',
        'serviceInterConfirm': 'Step 3: confirm interface',
        'serviceImport': 'Service import',
        'intfAddrRequired': 'Interface address is required, and can not add / at the end',
        'belongDomainRequired': 'Domain is required',
        'belongSystemRequired': 'System is required',
        'serviceTextRequired': 'Input service text can not be empty!',
        'serviceTextError': 'Input service text error, please check!',
        'serviceText': 'Text Import',
        'serviceFile': 'File Import',
        'importNext': 'Next Step',
        'importPrev': 'Previous',
        'importService': 'Import',
        'chooseFile': 'Select File',
        'importError': 'Import failed, service name is duplicate'
    },
    'servicedetails': {
        'serviceView': 'Service Details',
        'serviceAdd': 'Add Service',
        'serviceEdit': 'Modify Service',
        'serviceCopy': 'Service Replicatio',
        'testInfo': 'Test Information',
        'paramDescription': 'Parameter Description',
        'testAgain': 'Test Again',
        'testSuccess': 'Test Successfully',
        'testError': 'Test Failed',
        'dependentDomain': 'Domain',
        'system': 'System',
        'labelName': 'Tag Name',
        'capabilityPriority': 'Capability Priority',
        'singleAccessMax': 'Single Access Traffic Threshold',
        'heartbeat': 'Heartbeat',
        'reqMethodName': 'Method Name',
        'serviceDescription': 'Service Description',
        'versionHis': 'Version Information',
        'sendTime': 'Publish Time',
        'editContent': 'Revision Content',
        'encapsulated': 'Encapsulated',
        'composition': 'Combination',
        'image': 'Picture',
        'doNotAddepeatedly': 'Do not add again',
        'favSucceeded': 'Collection Successfully',
        'favCancelled': 'Collection Failed',
        'escfavSucceeded': 'Cancel Collection Successfully',
        'escfavCancelled': 'Cancel Collection Failed',
        'favDel': 'Cancel Collection',
        'topCenter': 'Code Rule:{bus label}.{system}.{service name,the initial is uppercase}_{method name}.{call mode}',
        'inputCreateName': 'Please enter the person in charge',
        'message1': 'Value: JSONPath is supported, ${standard.input.#field#} reference standard service input parameters , #field# is the English name of the standard field',
        'message2': 'Maximum two cached keys are supported, separated by a vertical bar (|)',
        'message3': '${#service_code#.input.JSONPath} is the input parameter of the target service, ${#service_code#.output.JSONPath} is the output of the target service',
        'reqHeadersRow': 'The request header allows up to 10 parameters to be configured',
        'reqBodyRow': 'The request parameter allows up to 10 parameters to be configured',
        'respContentRow': 'The response result allows up to 10 parameters to be configured',
        'statusCodeRow': 'The status code allows up to 10 parameters to be configured',
        'servNamerequired': 'Service name is required',
        'createdUserNamerequired': 'Person in charge is required',
        'descriptionrequired': 'Service description is required',
        'apiUrlrequired': 'Service URL is required',
        'reqHeadersCodeIsRequired': 'Request header name is required',
        'reqBodyCodeIsRequired': 'Request parameter name is required',
        'respContentCodeIsRequired': 'Response result name is required',
        'statusCodeNameIsRequired': 'Status code is required',
        'reqHeadersLength': 'Request header parameter exceeds the maximum range (0-2000)',
        'respContentLength': 'Response result parameter exceeds the maximum range (0-1000)',
        'statusCodeLength': 'Status code parameter exceeds the maximum range (0-2000)',
        'reqBodyLength': 'Request parameter exceeds the maximum range (0-2000)',
        'reqDemoLength': 'request sample exceeds the maximum range (0-2000)',
        'respDemoLength': 'the returned sample exceeds the maximum range (0-2000) ',
        'testHeadersLength': 'the test configuration request header exceeds the maximum range (0-2000)',
        'testParamLength': 'The test configuration request parameter exceeds the maximum range (0-2000)',
        'paramLength': 'You can only enter 2000 characters at most.',
        'submitTip': 'Submit Successfully',
        'submitErrorTip': 'Submit Failed',
        'operationFailureMsg': 'System exception: no reviewer configured, please contact the administrator',
        'operationFailureMsg2': 'Service parameter parsing failed',
        'addParam': 'Add Parameter',
        'delParamDescWithChild': 'Are you sure to remove this parameter and its sub parameters?',
        'delParamDescWithoutChild': 'Are you sure to remove this parameter?',
        'operationFailureApply': 'The service is applying for approval, operation not allowed',
        'operationFailureVersion': 'The same version number already exists for the service, please confirm',
        'operationFailureKey': 'Can not find corresponding record based on the ID',
        'showNewTag': 'View new tag information',
        'checkNewTag': 'New tag detected',
        'tabsNewTag': 'Switch new tag information',
        'tabsNewBelong': 'Switch new home tag',
        'showNewBelong': 'View new home tag',
        'servNameIsExit': 'Service name already exists',
        'serviceViewHis': 'Service Details (Version Information)',
        'timeoutIsQ': 'Response timeout is required',
        'timeoutIsNumber': 'Response timeout must be positive integers',
        'pkgSizeLimitIsQ': 'Single access traffic threshold is required',
        'pkgSizeLimitIsNumber': 'Single access traffic threshold must be positive integers',
        'reqTraffValueIsQ': 'Traffic limit (MB) is required',
        'reqTraffValueIsNumber': 'Traffic limit (MB) must be positive integers',
        'reqRateNumIsQ': 'Frequency limit (times) is required',
        'reqRateNumIsNumber': 'Frequency limit (times) must be positive integers',
        'delError': 'Only registered services can be deleted',
        'pauseSucceeded': 'Suspend Succeed',
        'recoverySuccessful': 'Recovery Succeed',
        'reqMethodNameRequired': 'Method name is required',
        'stopServeNO': 'Only suspended services are allowed to resume',
        'onLineToStop': 'Only online services are allowed to be suspended'
    },
    'servicemgr': {
        'servName': 'Service Name',
        'servCode': 'Code',
        'servStatus': 'Status',
        'chooseRule': 'Selected Rule',
        'packageService': 'Encapsulation Service',
        'combineService': 'Combination Service',
        'selectGroupServiceTip': 'Two or more published or suspended services must be selected to combine services',
        'bePackagedService': 'Encapsulated Service',
        'currentServStatus': 'Current Service Status',
        'packageServNme': 'Name after Encapsulation',
        'groupServName': 'Combination Service Name',
        'origParamName': 'Original Parameter Name',
        'origParamType': 'Original Parameter Type',
        'paramMapIn': 'Parameter Mapping',
        'respMapCheck': 'Result Verification',
        'oldServiceParamName': 'Original Service Parameter Name',
        'sourceType': 'Source Type',
        'mapRelation': 'Mapping Relationship',
        'sourceTypeFixed': 'Fixed Value',
        'sourceTypeCache': 'Cache',
        'sourceTypeStand': 'Standard Field',
        'sourceTypeStdParam': 'Standard Parameter',
        'sourceTypePrev': 'Service Parameter',
        'sourceTypeSpec': 'Specified Attribute',
        'sourceTypeOrigin': 'Original Output',
        'standardFieldName': 'Standard Field Output',
        'standardFieldType': 'Standard Field Type',
        'addField': 'Add Field',
        'validateSuccess': 'Verification Succeeded!',
        'paramCodeRequired': 'Parameter name is required',
        'sourceTypeRequired': 'Source type is required',
        'valueExpPatternError': 'JSONPath pattern error, please modify!',
        'valueExpRequired': 'Value is required!',
        'delFieldDescWithChild': 'Are you sure to remove this field and its subitems?',
        'delFieldDescWithoutChild': 'Are you sure to remove this field?',
        'combineContent': 'Combination Content',
        'executeMethod': 'Execute Method',
        'executeMethodParallel': 'Parallel',
        'executeMethodSequence': 'Sequence',
        'errorRowOfReqHeader': 'Request header line {0}',
        'errorRowOfReqHeaderMap': 'Request header parameter mapping line {0}',
        'errorRowOfReq': 'Request Parameter line {0}',
        'errorRowOfReqMap': 'Parameter Mapping line {0}',
        'errorRowOfResp': 'Response Result line {0}',
        'atleaseTwoService': 'Combination Service at lease two service',
        'layoutService': 'Orchestration Service',
        'layout': 'Orchestration',
        'layoutServiceName': 'Orchestration Service Name',
        'selectOneService': 'Please select one service at least',
        'paramCodeDuplicate': 'duplicate with the parameter name in line {0}',
        'resultCodeRequired': 'Standard field output is required',
        'resultCodeDuplicate': 'duplicate with the standard field output in line {0}',
        'valueExpStandFieldError': '{0} does not exist in the request parameter, please modify!',
        'valueExpNoServiceError': 'service code does not exist, please modify!',
        'valueExpNoFieldError': '{0} does not exist in the specified service, please modify!',
        'layoutServiceConfig': 'Orchestration start service is not configured',
        'getServCodeError': 'Failed to get service code',
        'reloadLayoutFlow': 'Reloading',
        'layoutFlowError': 'Process orchestration failed, submission is not allowed',
        'layoutFlowStart': 'Process orchestration is not started, submission is not allowed',
        'startFlowParam': 'Start Process Parameters',
        'applyTypeP': 'Publish Under Approval',
        'applyTypeO': 'Offline Under Approval',
        'applyTypeV': 'Call Under Approval',
        'applyTypePError': 'Service publish is under approval, and not allowed to be modified',
        'applyTypeVError': 'Service call is under approval, and if to reapply or not'
    },
    'serviceMonitorLifeS': {
        'servCode': 'Service Code',
        'servName': 'Service Description',
        'zxjk': 'Online Monitoring ',
        'beginTimes': 'Start Time',
        'endTimes': 'End Time',
        'fwwg': 'Service Gateway',
        'glzx': 'Management Center',
        'jkzx': 'Monitoring Center',
        'tbfw': 'Synchronization Service',
        'smzq': 'Life Cycle',
        'smzqzt': 'Status',
        'smzqczsj': 'Operating Time',
        'zxjkZc': 'Register',
        'zxjkFb': 'Publish',
        'zxjkZt': 'Normal',
        'zxjkHf': 'Recovery'
    },
    'nodemonitoring': {
        'status': 'Status',
        'server': 'Server',
        'cpu': 'CPU',
        'disk': 'Hard Disc',
        'mem': 'Memory',
        'partition': 'Partition',
        'freeSpace': 'Free Space',
        'useRatio': 'Usage',
        'total': 'Nodes',
        'normal': 'Normal',
        'abnormal': 'Abnormal',
        'viewPerformance': 'View Performance',
        'performanceImage': 'Node Performance Curve',
        'sysname': 'System Name',
        'sysversion': 'System Version',
        'sysruntime': 'System Running Time (Weekly)',
        'cpunum': 'CPU Cores',
        'tcpConnNum': 'Total TCP Connections',
        'cpuperformance': 'CPU Performance',
        'memUseRatio': 'Memory Usage',
        'totalCapacity': 'Total Capacity',
        'used': 'Used',
        'diskUseRatio': 'Hard Disc Usage',
        'partitions': 'Hard Disc Partition',
        'fileSystem': 'File System'
    },
    'callrecord': {
        'requestNo': 'Request Number',
        'servName': 'Service',
        'servVersion': 'Version',
        'reqUser': 'Caller',
        'reqTime': 'Request Time',
        'useTime': 'Used Time (ms)',
        'isSuccess': 'Result Status',
        'respContentLength': 'Return Bytes',
        'toBeCalled': 'To Be Called',
        'startTime': 'Request Time (Start)',
        'endTime': 'Request Time (End)',
        'viewCall': 'Call Details',
        'reqUseTime': 'Request Used Time (ms)',
        'returnCall': 'Forwarding Record',
        'returnParam': 'Response Parameter',
        'reqUrl': 'Request URL',
        'reqWay': 'Request Method',
        'reqPakSize': 'Request Message Size',
        'targetUrl': 'Destination Address',
        'transmitReqTime': 'Forwarding Time',
        'transmitReqHeaders': 'Forwarding Request Header',
        'transmitReqBody': 'Forwarding Request Content',
        'transmitRespTime': 'Forwarding Return Time',
        'transmitRespHeaders': 'Forwarding Return Header',
        'transmitRespContent': 'Forwarding Return Content',
        'respTime': 'Return Time',
        'respCode': 'Return Code',
        'respHeaders': 'Return Header',
        'failedType': 'Failure Type',
        'creator': 'Capability Provider',
        'servType': 'Capability Type',
        'respCodeType': 'Exception Type',
        'respContent': 'Return Content'
    },
    'monitorCenter': {
        'requestNum': 'Call Times',
        'lastWeek': 'Compared with Last Week',
        'successtNum': 'Success Times',
        'failNum': 'Failure Times',
        'calltatistics': 'Call Monitoring',
        'day': 'Day',
        'week': 'Week',
        'month': 'Month',
        'startDate': 'Start Time',
        'endDate': 'End Time',
        'nodeMonitoring': 'Node Monitoring',
        'more': 'More',
        'busAvailabilityMonitoring': 'Bus Availability Monitoring',
        'registrationStatistics': 'Registration Statistics',
        'service': 'Service',
        'abnormal': 'Abnormal',
        'individual': 'A',
        'serviceStatus': 'Service Status',
        'capabilityProvider': 'Capability Provider',
        'capabilityType': 'Capability Type',
        'callRelationship': 'Call Relationship',
        'abilityRanking': 'Ranking of Capacity Authorization',
        'userAuthorizationRanking': 'User Authorization Ranking',
        'greenBarValue': 'Success Times',
        'redBarValue': 'Failure Times',
        'totalnodes': 'Total Nodes',
        'normal': 'Normal'
    },
    'operationanalysis': {
        'todayRequestNum': 'Total Calls Today',
        'lastWeek': 'Compared with Last Week',
        'successtNum': 'Success Times',
        'failNum': 'Failure Times',
        'nodenumber': 'Total Nodes',
        'normal': 'Normal',
        'calltatistics': 'Call Statistics',
        'caller': 'Caller',
        'capabilityProvider': 'Capability Provider',
        'capabilityClassification': 'Capability Classification',
        'startDate': 'Start Time',
        'endDate': 'End Time',
        'transfer': 'Call',
        'times': 'Second',
        'performanceAnalysis': 'Performance Analysis',
        'requests': 'Total of Requests',
        'averageDuration': 'Average Time',
        'caller1': 'Caller 1',
        'caller2': 'Caller 2',
        'caller3': 'Caller 3',
        'caller4': 'Caller 4',
        'caller5': 'Caller 5',
        'caller6': 'Caller 6',
        'other': 'Other',
        'nodeMonitoring': 'Node Monitorin',
        'more': 'More',
        'choose': 'Please select',
        'registrationStatistics': 'Registration Statistics',
        'service': 'Service',
        'abnormal': 'Abnormal',
        'individual': 'A',
        'callRelationship': 'Call Relationship',
        'abilityRanking': 'Ranking of Capacity Authorization',
        'userAuthorizationRanking': 'Ranking of Users Authoriz',
        'anomalyAnalysis': 'Exception Analysis',
        'exceptionType': 'Exception Type',
        'serviceStatus': 'Service Status',
        'capabilityType': 'Capability Type',
        'greenBarValue': 'Success Times',
        'redBarValue': 'Failure Times',
        'packetize': 'Message Size',
        'responsetime': 'Response Time',
        'anomalyanalysis': 'Abnormal Analysis',
        'notFindServ': 'No such service'
    },
    'servicemonitormgr': {
        'serviceNo': 'Service Code',
        'updatedTime': 'Status Change Time',
        'recoveryTime': 'Estimated Recovery Time',
        'suspended': 'Suspended',
        'published': 'Published',
        'senderCode': 'Caller',
        'authTime': 'Authorization Time',
        'expiredTime': 'Expire Time'
    },
    'serviceMonitorIndex': {
        'zxkyxjk': 'Bus Availability Monitoring',
        'fbfws': 'Total of Published Services',
        'ztfws': 'Total of Suspended Services',
        'xxfws': 'Total of Offline Services',
        'sqfws': 'Total of Authorized Services',
        'dygx': 'Call Relationship',
        'fwmc': 'Service Name',
        'dxsc': 'Offline Time Length (h)',
        'zxsc': 'Online Time Length (h)',
        'dxbl': 'Offline Ratio',
        'xnjk': 'Performance Monitoring',
        'qqqcss': 'Total of Requests',
        'times': '(Times)',
        'xysc': 'Response Time Length',
        'zxfws': 'Total of Online Services',
        'jdjk': 'Node Monitoring',
        'mores': 'More',
        'rzjk': 'Log Monitoring',
        'rzzs': 'Total Logs',
        'bwzrz': 'Incomplete Log',
        'wzrz': 'Full Log',
        'serviceMonitor': 'Call Monitoring',
        'ycfwpm': 'Abnormal Service Ranking',
        'ycyypm': 'Abnormal Causes Ranking',
        'jdzs': 'Total Nodes',
        'pjxysc': 'Average Response Time',
        'ycfw': 'Exception Service',
        'bkjkfw': 'Non Monitorable Service',
        'zcfw': 'Normal Service',
        'fwzs': 'Total Services',
        'yyc': 'Exception',
        'zzc': 'Normal',
        'bbk': 'White Frame',
        'bwk': 'Outer Frame',
        'nums': 'Strip',
        'isDrop': 'Offline',
        'serviceDetail': 'Service information',
        'authServiceDetail': 'Authorization service details',
        'fwzxjk': 'Service online monitoring',
        'fwchsc': 'Srvice Lifetime',
        'yysj': 'Operation Data'
    },
    'businesstypemgr': {
        'flowCode': 'Process Code',
        'updateTime': 'Updated Date',
        'description': 'Description',
        'addBusinessType': 'Add Business Type',
        'editBusinessType': 'Edit Business Type',
        'showBusinessType': 'View Business Type',
        'delDesc': 'Are you sure to delete current record?',
        'businessTypeName': 'Business Name',
        'operSet': 'Operation Information',
        'operinfoIsNotNull': 'Operation information list can not be empty!',
        'addOper': 'Add Operation',
        'operName': 'Operation Name',
        'operCode': 'Operation Code',
        'enableSuccess': 'Enabled successfully',
        'prohibitSuccess': 'Disabled successfully',
        'businessTypeNameRequired': 'Business name is required',
        'businessTypeNameIsExit': 'Business name already exists',
        'nameRequired': 'Operation name is required',
        'nameIsExit': 'Operation name already exists',
        'notDelOpertar': 'Virtual instruction code has been associated and can not be deleted',
        'notDelname': 'Scene has been associated and can not be deleted',
        'nameDelDesc': 'Are you sure to delete current operation information?',
        'flowVersion': 'Flow Version',
        'flowToolTip': 'Failed to submit process information configuration, continue to improve the configuration information?'
    },
    'scenarioMgr': {
        'sceneName': 'Scene Name',
        'businessTypeName': 'Business Type Name',
        'description': 'Description',
        'enabled': 'Status',
        'updateTime': 'Modified Time',
        'showScenario': 'View Scene',
        'editScenario': 'Edit Scene',
        'bureauInfo': 'Office Direction Information',
        'itemList': 'Instruction Orchestration',
        'vendorName': 'Vendor',
        'netType': 'Network Type',
        'neTypeName': 'Device Type',
        'neName': 'NE Name',
        'selectNe': 'Select NE',
        'pleaSelectNe': 'Please select NE!',
        'showCmd': 'View Virtual Instruction',
        'vCmdName': 'Virtual Instruction Name',
        'vCmdCode': 'Virtual Instruction Code',
        'coreNoFind': 'Speciality code core not found',
        'romveCmd': 'Remove selected instruction or not',
        'sceneNameRequired': 'Scene name is required',
        'businessTypeIdRequired': 'Business type name is required',
        'moveSuccess': 'Move Successfully',
        'moveFailure': 'Move Failed',
        'netCode': 'NE Code',
        'neNameisQ': 'NE name is required',
        'networkInfo': 'NE Information'
    },
    'categorymanagementmgr': {
        'typeName': 'Category Name',
        'typeNameIsExist': 'Category name already exists',
        'typeCode': 'Category Code',
        'newTypeDialog': 'Add Category',
        'editTypeDialog': 'Edit Category',
        'delTypeDialog': 'Delete Category',
        'delConfirm': 'Confirm Deletion',
        'delConfirm2': 'and all its child nodes?',
        'hasCantDelete': 'There are parameter types, and can not be deleted!',
        'parentTypeName': 'Parent Category',
        'TypeDesc': 'Category Description',
        'typeNameRequire': 'Category name is required!',
        'exportParamType': 'Category Management',
        'editParamType': 'Edit Type',
        'showParamType': 'View Type',
        'addParamType': 'Add Type'
    },
    'parameterMaintain': {
        'exportParamType': 'Parameter Management',
        'enabled': 'Status',
        'edit': 'Edit',
        'add': 'Add',
        'regionIdRequired': 'Region is required',
        'netWordRequired': 'NE is required',
        'hanndleError': 'Handling Errors',
        'excelFileLimit': 'Only xls/xlsx format files can be uploaded',
        'excelSizeLimit': 'The size of uploaded excel can not exceed ',
        'excelError': 'Upload excel processing error ',
        'excelSuccess': 'Upload succeeded',
        'importTemplateName': 'Parameter management import',
        'exportTemplate': 'Template Export'
    },
    'parameterManagement': {
        'serverDataNoNull': 'Service data access parameter is empty, please re select',
        'version': 'Version',
        'dataPercision': 'Precision',
        'serverDataRequired': ' Illegal content in service data',
        'serverRequired': 'Data Source must be selected',
        'paramTypeNameRequire': 'Parameter type name is required',
        'categoryParamRequire': 'Parameter category is required',
        'fieldNameRequire': 'Field name is required',
        'paramTypeCodingRequire': 'Field code is required',
        'dataTypeRequire': 'Field type is required',
        'dataLengthRequire': 'Data length is required',
        'paramFieldContentRequire': 'Parameter field remarks is required',
        'controlTypesRequire': 'Control type is required',
        'paramTypeName': 'Parameter Type Name',
        'categoryName': 'Category',
        'granularity': 'Granularity',
        'createUserName': 'Created By',
        'createTime': 'Created Date',
        'updateUserName': 'Updated By',
        'enabled': 'Status',
        'fieldManagement': 'Field Management',
        'categoryParam': 'Parameter Category',
        'paramTypeCoding': 'Field Code',
        'paramDataGranularity': 'Parameter Data Granularity',
        'fieldName': 'Field Name',
        'fieldEncoding': 'Field Code',
        'controlTypes': 'Control Type',
        'isShow': 'Display or Not',
        'addField': 'Add Field',
        'editField': 'Edit Field',
        'viewField': 'View Field',
        'dataLength': 'Data Length',
        'dataType': 'Data Type',
        'templateField': 'Import Column or Not',
        'templateSearch': 'Query Criteria or Not',
        'inputParameter': 'Input Parameter',
        'outputParameter': 'Output Parameter',
        'isRequired': 'Is Required or Not',
        'sourceType': 'Origin Type',
        'targetType': 'Output Destination',
        'selectServe': 'Select Service',
        'exportParamType': 'Type Management',
        'templateChange': 'Change Parameter Fields or Not',
        'isFieldCRepeat': 'New parameter field name already exists',
        'onChange': 'Parameter fields are not allowed to be changed',
        'ptCode': 'Parameter Type Code',
        'pFCode': 'Parameter Field Code',
        'ptCodeRequire': 'Parameter type code is required'
    },
    'uamoitor': {
        'normalService': 'Service Monitoring',
        'totalServiceNum': 'Total Services',
        'normalServiceNum': 'Total of Normal Services',
        'errorServiceNum': 'Total of Exception Services',
        'normalNode': 'Node Monitoring',
        'totalNodeNum': 'Total Nodes',
        'normalNodeNum': 'Total of Normal Nodes',
        'errorNodeNum': 'Total of Exception Nodes',
        'normalCall': 'Call Monitoring',
        'totalCallNum': 'Number of Calls',
        'normalCallNum': 'Number of Normal',
        'errorCallNum': 'Total Exceptions',
        'error': 'Exception',
        'normal': 'Normal',
        'taskNum': 'Task Monitoring',
        'totalTaskNum': 'Total Tasks',
        'normalTaskNum': 'Total of Normal Tasks',
        'errorTaskNum': 'Total Abnormal Tasks',
        'endTime': 'End Date',
        'startTime': 'Start Date',
        'totalExeCmd': 'Instruction Monitoring',
        'totalExeCmdNum': 'Total of Execution Instructions',
        'totalExeDangerCmdNum': 'Total of High Risk Instructions',
        'exeSuccessCmdNum': 'Total of Successes',
        'exeFailCmdNum': 'Total of Failures',
        'exeFailDangerCmdNum': 'Failed High Risk Instruction',
        'componentMonitoring': 'Component Monitoring',
        'database': 'Database',
        'fileServices': 'File Service',
        'messageMiddleware': 'Message Middleware',
        'componentOperation': 'Component operation',
        'caching': 'Cache',
        'dataQuality': 'Collection Data Quality Monitoring',
        'taskTimeRate': 'Task Timeliness',
        'dataCompleteRate': 'Data Integrity',
        'dataVolatility': 'Data Volatility',
        'warn': 'Early Warning Monitoring',
        'criticaAlarmlNum': 'Urgent',
        'majorAlarmNum': 'Major',
        'minorAlarmNum': 'Minor',
        'warningAlarmNum': 'Alarm',
        'select': 'Please select',
        'network': 'NE Monitoring',
        'totalNeNum': 'Total of NEs',
        'connNeRate': 'NE Connection Ratio',
        'errorNeNum': 'Number of Abnormal NEs',
        'normalNeNum': 'Normal Numbers',
        'connNeRate1': 'Call Completing Ratio',
        'errorNeNum1': 'Abnormal Numbers'
    },
    'nodata': 'No data available',
    'add': 'Add',
    'delete': 'Delete',
    'modify': 'Modification',
    'collectionTask': 'Collection Task',
    'fileCollection': 'File Collection',
    'alarmCollection': 'Alarm Collection',
    'taskClassification': 'Task Classification',
    'specialty': 'Speciality',
    'batchNumber': 'Batch Number',
    'cycleTime': 'Cycle Time',
    'startTime': 'Start Time',
    'endTime': 'End Time',
    'servingObject': 'Sponsor Object',
    'implementationSituation': 'Implementation Situation',
    'searchPlaceholder': 'Please enter key words',
    'taskDescription': 'Task Description',
    'taskInstance': 'Task Instance',
    'taskLog': 'Task Log',
    'taskName': 'Task Name',
    'executionState': 'Implementation Status',
    'major': 'Speciality',
    'taskModel': 'Task Model',
    'processTaskOrNot': 'Process Task or Not',
    'sponsorObject': 'Sponsor Object',
    'describe': 'Description',
    'businessTargetMonitoringDetails': 'Business Target Monitoring Details',
    'dataTypeName': 'Data Type Name',
    'manufacturer': 'Vendor',
    'status': 'Status',
    'numberOfCollectedFiles': 'Total Collection Files',
    'totalCollectionFileSize': 'Total Collection File Size',
    'acquisitionTime': 'Collection Time',
    'analysisNumberOfFiles': 'Total Analysis Files',
    'analysisTime': 'Analysis Time',
    'mergeNumberOfFiles': 'Total Merged Files',
    'mergeSizeOfFiles': 'Merge Size of Files',
    'mergeTime': 'Merge Time',
    'fileAnalysis': 'File Parsing',
    'fileMerge': 'File Merge',
    'linkState': 'Activity Status',
    'executed': 'Executed',
    'inExecution': 'Implementing',
    'unexecuted': 'Not Executed',
    'successfulImplementation': 'Success',
    'executionFailure': 'Failed',
    'fileCollectionDetail': 'File Collection Detail',
    'connectId': 'Connect ID',
    'sourceAddressIP': 'Source Address IP',
    'sourceFilePath': 'Source File Path',
    'destinationIpAddress': 'Destination IP Address',
    'destinationFilePath': 'Destination File Path',
    'fileSize': 'File Size',
    'synchronizationStartTime': 'Synchronization Start Time',
    'synchronizationEndTime': 'Synchronization End Time',
    'synchronizationSuccessfulOrNot': 'Synchronization Successful or Not',
    'area': 'Region',
    'netType': 'NE Type',
    'netModel': 'NE Model',
    'netName': 'NE Name',
    'IpAddress': 'IP Address',
    'hdVersion': 'Hardware Version',
    'sncName': 'SNC Name',
    'aPortRegion': 'A Side Region',
    'aPortEquip': 'A Side Device',
    'hierarchy': 'Level',
    'zPortRegion': 'Z Side Region',
    'zPortEquip': 'Z Side Device',
    'aPortSite': 'A Side Site',
    'zPortSite': 'Z Side Site',
    'aPort': 'A Side Port',
    'zPort': 'Z Side Port',
    'occupyRate': 'Occupancy Ratio',
    'circuitName': 'Circuit Name',
    'circuitNumber': 'Circuit No.',
    'circuitType': 'Circuit Type',
    'customer': 'Customer',
    'netLevel': 'Network Level',
    'rate': 'Rate',
    'type': 'Type',
    'cableNumber': 'No.',
    'businessState': 'Business Status',
    'name': 'Name',
    'query': 'Query',
    'reset': 'Reset',
    'highSearch': 'Advanced',
    'export': 'Export',
    'import': 'Import',
    'choosePlaceholder': 'Please select',
    'edit': 'Modify',
    'more': 'More',
    'copy': 'Copy',
    'copyTip': 'Copy Success',
    'attribute': 'Attribute',
    'enable': 'Enable',
    'disable': 'Disable',
    'cancel': 'Cancel',
    'config': 'Acknowledge',
    'sava': 'Save',
    'cut': 'Cut',
    'cutTip': 'Cut Successfully',
    'paste': 'Paste',
    'pasteTip': 'Paste Successfully',
    'up': 'Up',
    'validation': 'Verification',
    'yes': 'Yes',
    'no': 'No',
    'article': 'Article',
    'alarm': 'Alarm',
    'criticalBoardCardFailure': 'Key Card Failure',
    'cableInterruptionFault': 'Optical Cable Interrupted Failure',
    'monitorSignalInterruption': 'Monitoring Signal Interruption',
    'alarmNum': 'Total Alarms',
    'network': 'NE',
    'circuit': 'Circuit',
    'fiberOpticCable': 'Optical Cable',
    'resources': 'Resource',
    'networkNumber': 'Total NEs',
    'channelNumber': 'Total Channels',
    'circuitNo': 'Total Circuits',
    'cableNo': 'Total Optical Cables',
    'SubmarineCableNo': 'Total Submarine Cables',
    'perIns': 'Performance Inspection',
    'health': 'Health',
    'abINs': 'Total Exception Tasks',
    'abCir': 'Circuit Exception',
    'abNet': 'NE Exception',
    'insNum': 'Total Tasks',
    'netIns': 'NE Inspection Total',
    'open': 'Provisioning',
    'adjust': 'Adjustment',
    'demolition': 'Dismantlement',
    'cutOver': 'Migration',
    'abnormal': 'Abnormal',
    'cutOverWait': 'To Be Migrated',
    'cutOvering': 'Under Migrating',
    'cutOvered': 'Finished',
    'netCutOver': 'NE',
    'circuitCutOver': 'Circuit',
    'FOCcutOver': 'Optical Cable',
    'NOSCcutOver': 'Submarine Cable',
    'dataUser': 'Total',
    'alarmFault': 'Fault Ticket',
    'timeOutThan': 'Timeout Ratio',
    'overWorkOrder': 'Timeout',
    'overAlarm': 'Timeout Early-warning',
    'onWork': 'In Progress',
    'hangWork': 'Hang-up',
    'contentPlaceholder': 'Please enter the content',
    'length': 'Length',
    'coverageSpotPre': 'Forecast Coverage Spot',
    'signIn': 'Sign In',
    'down': 'Move Down',
    'viewTopo': 'View Topology',
    'linkName': 'Activity Name',
    'executionProcedure': 'Execute Programms (verb)',
    'clickSearch': 'Click Search',
    'keyIndex': 'Key Indicator',
    'wireless': '4G Wireless Connection Success Ratio',
    'userExperience': 'User DL Average Throughput',
    'ERab': 'E-RAB Utilization',
    'Prb': 'DL PRB Utilization',
    'Gnb': 'GNB-end RAB Utilization Ratio',
    'inspection': 'Inspection Normal Ratio',
    'remote': 'Total Passed Acceptances',
    'mobile': 'Mobile Service',
    'volteVoice': 'VoLTE Voice Duration',
    'fiveTraffic': '5G Traffic',
    'fourTraffic': '4G Traffic',
    'volteUser': 'Total VoLTE Users',
    'volteRegistered': 'Total VoLTE Registrations',
    'fourUser': 'Total 4G Users',
    'unit': '10 K People',
    'netSize': 'Network Scale',
    'LteNum': 'Total LTEs',
    'fiveNum': 'Total 5Gs',
    'addressNum': 'Total Sites',
    'netFault': 'Network Fault',
    'LieAlarm': 'Total LTE Alarms',
    'fiveAlarm': 'Total 5G Alarms',
    'alarmFailureOrders': 'Total Fault Tickets',
    'netProfile': 'Network Overview',
    'fiveBusy': '5G Super Busy Cell',
    'fourBusy': '4G Super Busy Cell',
    'regionName': 'ORG Region',
    'dataTraffic': 'Data (GB)',
    'dataUserNum': 'Total Data Users',
    'applicationDesc': 'Change operations should put through "Service Protection System" based on provincial rule configuration management regulations',
    'delDesc': 'Delete the selected object or not',
    'enableDesc': 'Enable the selected object or not',
    'disableDesc': 'Disable the selected object or not',
    'saveDesc': 'Will lost all contents,are you sure to cancel',
    'setting': 'Advanced Setting',
    'tooltip': 'Notice',
    'application': 'Operation Application',
    'note': 'Remark',
    'applyOrder': 'Change Application Order Number',
    'editDesc': 'Please select one rule to edit',
    'copyDesc': 'Please select one rule to copy',
    'viewDesc': 'Please select one rule to view',
    'enableTip': 'Please select the rule to enable',
    'disableTip': 'Please select the rule to disable',
    'requestInterfaceException': 'Request Interface Exception',
    'applicationTip': 'Please enter change application order number',
    'noApplication': 'Change application is invalid',
    'missApplication': 'Change application is missiing',
    'isDel': 'Please select the rule to be deleted',
    'delTip': 'Delete Successfully',
    'successTip': 'Operation succeeded',
    'ruleNametip': 'Please enter rule name, format suggestion: area-speciality-device type',
    'ruleDescTip': 'Please enter rule description',
    'remarkDescTip': 'Please enter remark',
    'moduleName': 'Module Type',
    'currentUsername': 'Operation Account',
    'operType': 'Operation Type',
    'operTime': 'Operation Time',
    'operContent': 'Operation Contents',
    'hostIp': 'Host IP',
    'base': 'Basic Information',
    'rootConfig': 'Root Alarm Configuration',
    'ruleConfig': 'Rule Expression Configuration',
    'operationLog': 'Operation Log',
    'rootAlarmDesc': 'Root Alarm Not Setting in Rule Configuration',
    'checkTip': 'Detection Passed',
    'saveTip': 'Save Successfully',
    'saveRule': 'Basic informtion saved successfully, rule saving Failed',
    'configuration': 'Condition not set, will not generate effective expression, continue to save?',
    'addEntity': 'Add Entity',
    'editEntity': 'Edit Entity',
    'entityDesc': 'Please select entity',
    'entityNameDesc': 'Please enter entity name',
    'setAlarm': 'Set Root Alarm',
    'addSonterm': 'Add Sub Condition',
    'editSonterm': 'Edit Sub Condition',
    'sonTermDesc': 'Please select a sub condition',
    'sonTermNameDesc': 'Please enter sub condition name',
    'addTerm': 'Add Condition Item',
    'editTerm': 'Edit Condition Item',
    'entityOrSonDesc': 'Please select a device type or sub condition',
    'termDesc': 'Please select condition item',
    'rootAlarm': 'Root Alarm',
    'operator': 'Operator',
    'value': 'Value',
    'entityName': 'Entity Name',
    'sonTermName': 'Sub Condition Name',
    'timeFormat': 'Time Format',
    'dataTime': 'Date Time',
    'data': 'Date',
    'time': 'Time',
    'dataTimeDesc': 'Please select date and time',
    'dataDesc': 'Please select date',
    'timeDesc': 'Please select time',
    'start': 'Start',
    'end': 'End',
    'internalLogic': 'Internal Logic',
    'internalLogicDesc': 'Multiple-line results, please select internal relations',
    'isInternalLogic': 'Validation failed, please select operator',
    'isValue': 'Validation Failed, Value is Wrong',
    'between': 'Bewteen',
    'noBetween': 'Not Between',
    'upDesc': 'Please select one item to move up',
    'upWarning': 'No More Move Ups',
    'downDesc': 'Please select one item to move down',
    'downWarning': 'No More Move Downs',
    'chooseItem': 'Please select a piece of data',
    'chooseSon': 'Please select sub condition or condition item first',
    'copyOrCut': 'Please copy or cut sub condition or condition item first',
    'sonOrEntity': 'Please select a sub condition or entity',
    'chooseAttribute': 'Please select a attribute',
    'firstRelation': 'Can not Set External Relation to the First Item',
    'termAddSon': 'Can not Add Sub Condition to Condition Item',
    'equal': 'Equal',
    'noEqual': 'Not Equal',
    'hintAlertStr': 'Beause you select',
    'hintAlertStr1': 'and the internal logic judgment of values',
    'hintAlertStr2': 'the Logic is:',
    'hintAlertStr3': 'the judgement is invalid at this time,and the condition item is invalid, please modify! the logic is',
    'inputValue': 'Please enter a value',
    'ruleName': 'Rule Name',
    'netTopo': 'Topology Scene',
    'alarmType': 'Root Alarm Association Type',
    'founder': 'Created By',
    'founderTime': 'Created Time',
    'update': 'Updated By',
    'updateTime': 'Updated Time',
    'entityNum': 'Total Entities',
    'lowRuleNum': 'Total Minimum Entities',
    'waitTime': 'Waiting Time',
    'isOrderRule': 'Sequential Alarm Matching Rule',
    'isConvergence': 'Converge Associated Alarm',
    'rootMoreTickets': 'Root Alarm Grid',
    'relatedTicketType': 'Dispatch Type of Derived Alarm',
    'cableAnalysis': 'In Cable Analysis or Not',
    'isChildSms': 'Send SMS or Not',
    'ruleDesc': 'Rule Description',
    'remark': 'Remark',
    'relatedType': 'Association Type',
    'perceivedSeverity': 'Root Alarm Severity',
    'rootAlarmIdentify': 'Root Alarm Name',
    'deriveAlarmfiled': 'Extract Root Alarm Field',
    'rootEditType': 'Definition of Root Alarm Field',
    'rootClearType': 'Root Alarm Recover Mode (from child alarm to root alarm)',
    'rootTermType': 'Root Alarm Termination Mode',
    'childTermType': 'Child Alarm Termination Mode',
    'noConfig': 'Not Configured',
    'click': 'Please click',
    'addAssType': 'Add Associated Rule',
    'editAssType': 'Edit Associated Rule',
    'copyAssType': 'Copy Associated Rule',
    'viewAssType': 'View Associated Rule',
    'first': 'The',
    'rootAlarmFiledTip': 'All columns in root alarm filed can not be null',
    'isRuleName': 'Please fill in rule name',
    'isRegionId': 'Please select region',
    'isSpeciality': 'Please select speciality',
    'isAlarmNum': 'Please fill in the number of required alarms',
    'isWaitTime': 'Please fill in the waiting time',
    'isRelatedType': 'Please select root alarm associated type',
    'isRootAlarmIdentifyOne': 'The "Root Alarm Name" field is required if the alarm associated type is " Generated root alerts"',
    'isRootAlarmIdentifyTwo': 'The "Extract Root Alarm" field is required if the alarm associated type is " Generated root alerts"',
    'isLowRuleNum': 'The number of minimum entities should more than 0',
    'isEntityNum': 'The number of configured entities [Rule Configuration] should not more than the number of entities [Basic Information]',
    'isAlarmNumTip': 'The number of required alarms should not Less than 1',
    'isWaitTimeTip': 'Waiting time should less than 9999',
    'mustAlarmNumber': 'Total Required Alarms',
    'resultsOfEnforcement': 'Execution Result',
    'isDelName': 'Delete or Not',
    'addFilter': 'Add Filter Rule',
    'editFilter': 'Edit Filter Rule',
    'copyFilter': 'Copy Filter Rule',
    'viewFilter': 'View Filter Rule',
    'executiveDetails': 'Execution Detail',
    'managerName': 'Rule Administrator',
    'isRepeateDesc': 'Multiple Notification or Not',
    'interval': 'Interval',
    'phoneNumList': 'Specified Number',
    'phoneNumListDesc': 'Please use semi-colon to seperate multiple phone numbers',
    'isImmediateSend': 'Immediately Send',
    'sendStartTime': 'SMS Start Time',
    'sendEndTime': 'SMS End Time',
    'delayTime': 'Delay Time',
    'messageModel': 'SMS Customization',
    'messageAddContent': 'SMS Additional Msgs',
    'isSendToPhoneNum': 'Directly send to specified number or not',
    'isSendDefault': 'Send to default number or not, when not matching receiver',
    'keyName': 'Matching Field Receiver',
    'addAutoMsg': 'Add Auto SMS Rule',
    'editAutoMsg': 'Edit Auto SMS Rule',
    'copyAutoMsg': 'Copy Auto SMS Rule',
    'viewAutoMsg': 'View Auto SMS Rule',
    'isIsImmediate': 'Please select immediately dispatch or not',
    'isDelayTime': 'Please fill in the delay time',
    'isSendStartTime': 'Please select sending start time',
    'isSendEndTime': 'Please select sending end time',
    'sendTimeDesc': 'Sending end time should not less than sending start time',
    'isMessageModel': 'Please select SMS customized content',
    'isIsRepeate': 'Please select sendig repeatable or not',
    'isIsRepeateDesc': 'Please fill in interval time if sending repeatable, the interval time should more than 0',
    'isIsSendToPhoneNum': 'Please select if send to the pointed number or not',
    'iskeyName': 'Please select recipient matching filed',
    'isIsSendDefault': 'Please select if send to default number or not, when can not match recipient',
    'isPhoneNum': 'Please enter the specified number',
    'invalidNum': 'Invalid Number',
    'refill': 'Please refill',
    'isPhoneNumDesc': 'The pointed number was invalid, please refill',
    'addTicket': 'Add Auto Dispatch Rule',
    'editTicket': 'Edit Auto Dispatch Rule',
    'copyTicket': 'Copy Auto Dispatch Rule',
    'viewTicket': 'View Auto Dispatch Rule',
    'isBtypeDltIdObj': 'Please select specifica business',
    'isBtypeMstIdObj': 'Please select business type',
    'isbfaultTypeIdObj': 'Please select business item',
    'isfaultphenoid': 'Please select fault phenomenon',
    'isalarmseverity': 'Please select fault severity',
    'isentityNum': 'Please select the number of entities',
    'istktsbpflag': 'Please select recipient object',
    'btypeMstIdObj': 'Business Type',
    'btypeDltIdObj': 'Specific Business',
    'bfaultTypeIdObj': 'Complaint Item',
    'faultPhenoId': 'Fault Phenomenon',
    'alarmSeverity': 'Fault Severity',
    'isImport': 'Important Insurance Rule or Not',
    'isIsImport': 'Please select if important rule or not',
    'isAdditional': 'Multiple Dispatch or Not',
    'isDifMntlevel': 'Distinguish Maintenance Level or Not',
    'isIsDifMntlevel': 'Please select if differentiate maintenance level',
    'isDifMntlevelDesc': 'Please fill in time limit of fault and handling,if donot differentiate maintenance level',
    'isDifMntlevelDescTwo': 'Only Configure One Dispatch Time,if donot differentiate Maintenance level',
    'haveDifMntlevel': 'Maintenance level already exists, please select again',
    'isImmediate': 'Immediate Dispatch',
    'isdelayTime': 'Please fill in delay time',
    'tktBeginTime': 'Start Time of Dispatch',
    'istktBeginTime': 'Please select dispatch start time',
    'tktEndTime': 'End Time of Dispatch',
    'istktEndTime': 'Please select dispatch end time',
    'tktsbpflag': 'Receiver Object',
    'keyfiledname': 'Region Matching Field',
    'iskeyfiledname': 'Please select region filed',
    'tktsbnflag': 'Default Dispatch',
    'istktsbnflag': 'Please select default dispatch or not',
    'isRootTicket': 'Dispatch When Has Deprived Alarm',
    'isIsRootTicket': 'Please select dispatch or not when has derived alarm',
    'isRootImmediate': 'Dispatch Immediately When Has Deprived Alarm',
    'isIsRootImmediate': 'Please select immediate dispatch or not when has derived alarm',
    'isAddFault': 'Multiple Dispatch or Not',
    'isIsAddFault': 'Please select if add fault dispatch or not',
    'faultlimit': 'Fault Limit',
    'processlimit': 'Process Limit',
    'maintenanceLevelDesc': 'Maintenance Grade',
    'isMaintenanceLevel': 'Please select maintenance level',
    'isEffectBusinessDesc': 'Affect Business or Not',
    'isIsEffectBusiness': 'Please select if affect business',
    'isImmediateDesc': 'Dispatch Immediately or Not',
    'isIsImmediateDesc': 'Please select if dispatch immediately or not',
    'isRegionName': 'Please select region',
    'haveRegionName': 'Region already exists, please select again',
    'suggestion': 'Pre-Treatment Suggestion',
    'isSuggestion': 'Please select pre-processing suggestion',
    'problemCount': 'Total Cumulative Alarms',
    'isproblemCount': 'Please fill in the number of cumulative alarms',
    'addSendTime': 'Add Dispatch Time Configuration',
    'editSendTime': 'Please modify dispatch time configuration',
    'haveSendTime': 'Please select and edit a dispatch time configuration',
    'delSendTime': 'Please select dispatch time configuration that to be deleted',
    'addSuggestion': 'Add Pre-processing Suggestion',
    'editSuggestion': 'Modify Pre-processing Suggestion',
    'haveSuggestion': 'Please select and edit a pre-processing suggestion',
    'delSuggestion': 'Please select pre-processing suggestion that to be deleted',
    'sendTimeConfig': 'Dispatch Time Configuration',
    'isSendTimelist': 'Dispatch Time Configuration can not be Empty',
    'checkTimeBasicInfo': 'Dispatch Time Configuration, the 【',
    'checkTimeBasicInfoTwo': '】 item,',
    'alarmStatistics': 'Alarm Statistics',
    'untreated': 'Unprocessed',
    'NotClear': 'Not Cleared',
    'NotSingle': 'Undispatched',
    'alarmList': 'Alarm List',
    'batchOperation': 'Batch Operation',
    'batchConfirmation': 'Batch Acknowledge',
    'terminateBatch': 'Batch Terminate',
    'batchComments': 'Batch Notes',
    'region': 'Region',
    'labelState': 'Status Label',
    'alarmTime': 'Alarm Time',
    'removeTime': 'Clear Time',
    'deviceName': 'Device Name',
    'IPAddress': 'IP Address',
    'alarmName': 'Alarm Name',
    'alarmLogo': 'Alarm Identity',
    'locationInfo': 'Location',
    'keyFields': 'Key Field',
    'fault': 'Fault',
    'similar': 'Similar',
    'after': 'Duration',
    'operation': 'Operation',
    'day': 'Day',
    'hours': 'Hour',
    'points': 'Points',
    'detailedInfo': 'Detail Information',
    'terminationOf': 'Terminate',
    'sendSingle': 'Handled',
    'payAttentionField': 'Concerned Field',
    'basicInformation': 'Basic Information',
    'extendedInfo': 'Extended Information',
    'alarmLable': 'Alarm Tag',
    'networkLable': 'NE Tag',
    'originalAcquisition': 'Original Collection',
    'alarmLevel': 'Alarm Severity',
    'serious': 'Critical',
    'important': 'Major',
    'secondary': 'Minor',
    'warning': 'Warning',
    'remove': 'Clear',
    'noSure': 'Uncertain',
    'unconfirmed': 'Unacknowledged',
    'sentSingled': 'Dispatched',
    'belongsType': 'Model',
    'alarmID': 'Alarm ID',
    'segmentationTip': 'Please split multiple values with half angle semicolon (;)',
    'workOrderID': 'Fault Ticket ID',
    'alarmStatus': 'Alarm Status',
    'isSendSingle': 'Dispatch or Not',
    'isClear': 'Clear or Not',
    'isCutOver': 'Migration or Not',
    'startDate': 'Start Date',
    'endData': 'End Date',
    'to': 'To',
    'historyQuery': 'History Query',
    'emptyHistory': 'Clear History',
    'emptyCondition': 'Clear Condition',
    'annotationPlaceholder': 'Please enter notes',
    'determine': 'OK',
    'correlation': 'Correlations',
    'pathCode': 'Light Path Code',
    'originalAlarm': 'Original Alarm',
    'similarAlarm': 'Similar Alarm',
    'rulesMatch': 'Rule Match',
    'moreOperations': 'More Operations',
    'menu': 'Menu',
    'OperatingLable': 'Operation Tag',
    'alarmCollector': 'Alarm Collector',
    'sonMajor': 'Sub Speciality',
    'deviceType': 'Device Type',
    'alarmRank': 'Alarm Severity',
    'singleState': 'Problem Status',
    'failureFrequency': 'Total Alarms',
    'similarAlarmNumber': 'Total Similar',
    'InitialAlarmTime': 'First Alarm Time',
    'latestAlarmTime': 'Event Time',
    'cutOverIdentity': 'Migration',
    'clearlogo': 'Clear Flag',
    'endUser': 'Terminated By',
    'terminationTime': 'Terminated Time',
    'finalFailureTime': 'Last Alarm Time',
    'confirmUser': 'Confirmed By',
    'confirmTime': 'Confirmed Time',
    'alarmObject': 'Alarm Object',
    'operatingAnnotation': 'Operation Note',
    'originalWarningName': 'Original Alarm Name',
    'originalWarningLabel': 'Original Alarm Identity',
    'boardPosition': 'Card Location',
    'portLocation': 'Port Location',
    'additionalFieldOne': 'Additional Field 1',
    'additionalFieldTwo': 'Additional Field 2',
    'additionalFieldThree': 'Additional Field 3',
    'additionalFieldFour': 'Additional Field 4',
    'additionalFieldFive': 'Additional Field 5',
    'alarmDescription': 'Alarm Description',
    'refresh': 'Refresh',
    'view': 'View',
    'regularClassName': 'Rule Classification Name',
    'isMatchRule': 'Match Rule or Not',
    'matchRuleID': 'Matching Rule ID',
    'matchRuleName': 'Matching Rule Name',
    'isEmpty': 'Can not Be Empty',
    'periodicRecording': 'Periodic Recording',
    'dataTypeManagement': 'Data Type Management',
    'indexGroupManagement': 'Indicator Group Management',
    'consolidatedIndicatorTypeManagement': 'Consolidated Indicator Type Management',
    'businessObjectiveManagement': 'Business Objective Management',
    'cirIns': 'Circuit Inspection',
    'dataClassification': 'Data Classification',
    'dataType': 'Data Type',
    'basicProcess': 'Basic Process',
    'businessObjectives': 'Business Objectives',
    'resultConnectionInterface': 'Result Connection Interface',
    'indexGroupName': 'Indicator Group Name',
    'targetGroupName': 'Target Group Name',
    'cycle': 'Cycle',
    'minute': 'Minute',
    'cycleDelayTime': 'Cycle Delay Time',
    'consolidatedIndicatorTypeName': 'Consolidated Indicator Type Name',
    'legalFileSize': 'Legal File Size',
    'legalFileNum': 'Total Legal Files',
    'numberOfLegalRecords': 'Total Legal File Records',
    'cycleTimeExtractionExpression': 'Cycle Time Extraction Expression',
    'fileTimeFormatExpression': 'File Time Format Expression',
    'submit': 'Submit',
    'regularExpression': 'Regular Expression',
    'indicatorItemConfiguration': 'Indicator Item Configuration',
    'indexItemName': 'Indicator Item Name',
    'columnName': 'Column Name',
    'columnOrder': 'Column Order',
    'fieldType': 'Field Type',
    'addIndexItem': 'Add Indicator Item',
    'batchDelete': 'Batch Remove',
    'mergerIndex': 'Merge Index',
    'mergeTypeName': 'Merge Type Name',
    'indexGroupToBeSelected': 'Index Group to Be Selected',
    'orderNumber': 'Serial Number',
    'indexGroupOrName': 'Index Group or Item Name',
    'indexNumberOrType': 'Total Indexes or Grouping Type',
    'submarineCable': 'Submarine Cable',
    'notTimeOutThan': 'Nun-timeout Ratio',
    'netVersion': 'NE Version',
    'criticalAlarmTotal': 'Total Critical Alarms',
    'majorAlarmTotal': 'Total Major Alarms',
    'activityAlarm': 'Active Alarm',
    'queryCondition': 'Query Conditions',
    'alarmConfirmRepeatTooltip': 'Alarm confirmed, no more confirmation operations',
    'alarmTerminationRepeatTooltip': 'Alarm terminated, can not continue to operate',
    'operationFailure': 'Operation Failure',
    'equipmentBoardDiagram': 'NE Panel',
    'alarmInfo': 'Alarm Message',
    'alarmTotal': 'Total Alarms',
    'level': 'Severity',
    'workNumber': 'Ticket No.',
    'numberOfPatrolPorts': 'Total Inspection Ports',
    'patrolInformation': 'Inspection Information',
    'numberOfPatrolExceptionPorts': 'Total Abnormal Inspection Ports',
    'jobName': 'Title',
    'entryIntoForceTime': 'Effective Time',
    'operationArea': 'Organization Region',
    'implementationState': 'Implementation Status',
    'networkElementHealth': 'NE Health',
    'bearerInformation': 'Bearer Information',
    'totalNumberOfCuts': 'Total Migrations',
    'nameOfCutOff': 'Migration Name',
    'jobNumber': 'Ticket No.',
    'cutOffInformation': 'Migration Information',
    'enterKeywords': 'Enter key words',
    'configurationInformation': 'Configuration Information',
    'calendarInformation': 'Machine Maintenance Information',
    'machineCalendarCardNo': 'Machine Card Number',
    'documentNumber': 'Document Number',
    'eventType': 'Event Type',
    'Dealer': 'Operated By',
    'processingTime': 'Operated Time',
    'diachronicDuration': 'Duration Period',
    'processingResult': 'Processing Result',
    'contactNumber': 'Contact Number',
    'softwareVersion': 'Software Version',
    'remarksDescription': 'Remark Description',
    'affiliatedComputerRoom': 'Room',
    'networkElementIP': 'NE IP',
    'affiliatedSite': 'Site',
    'equipmentManufacturer': 'Device Vendor',
    'detailAddress': 'Detailed Address',
    'admissionTime': 'Connection Time',
    'maintenanceAttribute': 'Maintenance Attribute',
    'usageState': 'Current Status',
    'networkLevel': 'Network Level',
    'totalFrameNumber': 'Total Shelves',
    'channelTotal': 'Total Slots',
    'totalNumberOfCards': 'Total Cards',
    'totalNumberOfPhysicalTerminals': 'Total Physical Terminals',
    'totalLogicalTerminals': 'Total Logical Terminals',
    'cabinetInformation': 'Cabinet Information',
    'channelInformation': 'Slot Information',
    'cardInformation': 'Card Information',
    'physicalPortInformation': 'Physical Port Information',
    'logicalPortInformation': 'Logical Port Information',
    'frameNumber': 'Shelf Number',
    'frameName': 'Shelf Name',
    'frameTypeName': 'Shelf Type Name',
    'isDoubleSided': 'Double Side or Not',
    'height': 'Height',
    'thickness': 'Thickness',
    'channelNo': 'Channel Number',
    'channelName': 'Channel Name',
    'boardCode': 'Card Number',
    'boardName': 'Card Name',
    'boardType': 'Card Type',
    'portNumber': 'Port Number',
    'portName': 'Port Name',
    'portType': 'Port Type',
    'loopbackState': 'Loopback Status',
    'inputOpticalPower': 'Input Optical Power',
    'outputOpticalPower': 'Output Optical Power',
    'laserState': 'Laser Status',
    'portDirection': 'Port Direction',
    'opticalPowerType': 'Optical Power Type',
    'opticalPower': 'Optical Power',
    'relatedInformation': 'Associated Information',
    'moveRingInformation': 'Power Information',
    'electricityChargeManagement': 'Electricity Charge Management',
    'detail': 'Detail',
    'additionalInfo': 'Additional Information',
    'cleared': 'Cleared',
    'stationCode': 'Site Code',
    'longitude': 'Longitude',
    'stationName': 'Site Name',
    'latitude': 'Latitude',
    'stationType': 'Site Type',
    'village': 'Cell',
    'chooseAlarmTip': 'Please select alarm',
    'paymentStartDate': 'Start of Payment Time',
    'paymentTerminationDate': 'End of Payment Time',
    'electricMeterNumber': 'Electric Meter Number',
    'dateOfRead': 'Meter Reading Date',
    'allCategories': 'All Category',
    'wattHourMeter': 'Electric Meter Degree',
    'netConfigData': 'Network Configuration Data',
    'networkWarn': 'Network Alarm',
    'electricQuantity': 'Electric Quantity',
    'networkPerformance': 'Network Performance',
    'startDegree': 'Network Performance',
    'billRate': 'Billing Ratio',
    'monthOfEntry': 'Entry Month',
    'settlementAmount': 'Settlement Amount',
    'settlementStatus': 'Settlement Status',
    'settlementDate': 'Closing Date',
    'powerSupplyMode': 'Power Supply Mode',
    'moveRingID': 'Power ID',
    'moveRingCode': 'Power Code',
    'moveRingName': 'Power Name',
    'fourAccessUsers': 'Total 4G Access Users',
    'moveRingSystem': 'Power System',
    'ratedInputVoltage': 'Rated Input Voltage (V)',
    'ratedInputCurrent': 'Rated Input Current (A)',
    'upboundEquipmentCodeOne': 'Uplink Device Code 1',
    'upboundEquipmentCodeTwo': 'Uplink Device Code 2',
    'uplinkDevicePortOne': 'Uplink Device Port 1',
    'uplinkDevicePortTwo': 'Uplink Device Port 2',
    'numberOfInputCircuits': 'Total Input Loops',
    'numberOfOutputCircuits': 'Total Output Loops',
    'alarmDetail': 'Alarm Details',
    'newCheckItem': 'New Detection Item',
    'belongsCategory': 'Category',
    'detectionObject': 'Detection Object',
    'disposalAdvice': 'Disposal Suggestion',
    'testingWay': 'Testing Method',
    'patrolItemConfig': 'Inspection Configuration',
    'basicInfo': 'Basic Information',
    'objectSet': 'Object Configuration',
    'methodsSet': 'Method Configuration',
    'abnormalDisposal': 'Abnormal Disposal',
    'inspectionName': 'Inspection Item Name',
    'category': 'Classification',
    'objectType': 'Object Type',
    'vender': 'Vendor',
    'equipmentModel': 'Device Model',
    'boardModel': 'Card Model',
    'termsOfInspection': 'Inspection Method',
    'accessMethod': 'Data Access Method',
    'JudgmentMethod': 'Judgement Method',
    'serviceName': 'Service Name',
    'path': 'Path',
    'paramsList': 'Parameter List',
    'source': 'Source',
    'alias': 'Alias',
    'isAllowedEdit': 'Editable or Not',
    'InformWay': 'Notification Method',
    'allInspectionObjects': 'All Inspection Objects',
    'equipment': 'Device',
    'board': 'Card',
    'port': 'Port',
    'inspectionObject': 'Inspection Object',
    'InspectionItem': 'Inspection Item',
    'templateName': 'Template Name',
    'whetherEnable': 'Is Enable',
    'selectedPatrol': 'Selected Inspection Item',
    'alternativeInspection': 'Optional Inspection Item',
    'inspectionCategory': 'Inspection Category',
    'inspectionItem': 'Inspection Item',
    'parameter': 'Parameter',
    'toBeImplemented': 'To Be Implemented',
    'inImplementation': 'Under Implementation',
    'operateState': 'Status',
    'focusOn': 'Significant Attention',
    'jobStartTime': 'Start Time',
    'jobEndTime': 'End Time',
    'GISlocation': 'GIS Positioning',
    'energyConsumption': 'Energy Consumption',
    'BBU_name': 'BBU Name',
    'diagnosis': 'Diagnosis',
    'networkElementHealthScore': 'NE Health Score',
    '5GCellList': '5G Cell List',
    'fourCellList': '4G Cell List',
    'baseStationID': 'Base Station ID',
    'standardSystem': 'Signal Pattern',
    'baseStationName': 'Base Station Name',
    'subnet': 'Subnetwork',
    'equipmentStatus': 'Device Status',
    'lastYear': 'Last Year',
    'lastMonth': 'Last Month',
    'current': 'Current',
    '4GMainUsesIP': '4G Main IP',
    '4GMainUsesPort': '4G Main Port',
    '5GMainUsesIP': '5G Main IP',
    '5GMainUsesPort': '5G Main Port',
    '4GstandbyIP': '4G Alternate IP',
    '4GstandUsesPort': '4G Alternate Port',
    '5GstandUsesIP': '5G Alternate IP',
    '5GstandUsesPort': '5G Alternate Port',
    'deviceIcon': 'Device Icon',
    'back': 'Return',
    'user': 'User',
    'traffic': 'Data',
    'framePanelDraw': 'Frame Pannel Diagram',
    'high': 'High',
    'low': 'Low',
    'distributionUsersAndTraffic': 'User Traffic Distribution',
    'RRCMaximumConnection': 'Total RRC Maximum Connections',
    'vallageLogicCode': 'Cell Logic Code',
    'vallagePhysicalCode': 'Cell Physical Code',
    'vallageID': 'Cell ID',
    'coverageType': 'Coverage Type',
    'centerFrequencyPoint': 'Central Frequency',
    'centralBroadband': 'Central Broadband',
    'cellReselectionPriorityInFrequency': 'Frequency Cell Reprioritization',
    'uplinkFrequencyPoint': 'Uplink Frequency',
    'uplinkBroadband': 'Uplink Broadband',
    'penMROrNot': 'MR Enable or Not',
    'downlinkFrequency': 'Downlink Frequency',
    'downlinkBroadband': 'Downlink Broadband',
    'sectorApplicationType': 'Sector Application Type',
    'performanceInformation': 'Performance Information',
    'performanceIndexTime': 'Performance Index Time',
    'RRCConnectionSuccessRate': 'RRC Connection Success Ratio',
    'ERABDropRate': 'E-RAB Drop Ratio',
    'flow': 'Data (GB)',
    'uplinkPRBUtilization': 'Uplink PRB Utilization Ratio',
    'RRU_name': 'RRU Name',
    'radioFrequency': 'Radio Frequency',
    'antennaInformation': 'Antenna Information',
    'equipmentCode': 'Device Code',
    'installWidth': 'Mounting Width',
    'installationWidth': 'Mounting Width (m)',
    'antennaGain': 'Antenna Gain',
    'totalAntennaPitch': 'Antenna Accumulated Pitch Angle',
    'builtInPitchAngle': 'Build-in Pitch Angle',
    'mechanicalPitchAngle': 'Mechanical Pitch Angle',
    'electricPitch': 'ESC Pitch Angle',
    'azimuth': 'Azimuth Angle',
    'feederSpecification': 'Feeder Specification',
    'feederLength': 'Feeder Length (m)',
    'coverageArea': 'Coverage (m)',
    'antennaBand': 'Antenna Frequency Band',
    'electricAdjustmentAzimuth': 'ESC Azimuth Angle',
    'horizontalLobe': 'Horizontal Lobe',
    'RRU_logo': 'RRU ID',
    'RRU_type': 'RRU Type',
    'gnbDrops': 'E-RAB Drop Ratio(GNB)',
    'normalNumberOfPatrolInspection': 'Total Normal Inspections',
    'patrolVariable': 'Total Abnormal Inspections',
    'totalInspectionItems': 'Total Inspection Items',
    'dispatchNumber': 'Total Dispatch Fault Tickets',
    'receiptNumber': 'Total Check-in Orders',
    'baseStationGrade': 'Base Station Level',
    'cellPhysicalID': 'Cell Physical ID',
    'cellLogicalID': 'Cell Logical ID',
    'villageName': 'Cell Name',
    'station': 'Site',
    'welcomeLoginIn': 'Welcome to Log-in',
    'pleaseInputUserName': 'Please enter user name',
    'pleaseInputPwd': 'Please enter password',
    'loginIn': 'Log-in',
    'accountNotEmpty': 'Account Can Not Be Empty',
    'pwdNotEmpty': 'Password Can Not Be Empty',
    'topologicalLocation': 'Topology Location',
    'subdomain': 'Sub Area',
    'sectorName': 'Sector Name',
    'AverageNumRRC': 'Total Average RRC Connection Users',
    'averageRRC': 'Total Average RRC Connection Users in the Same Period Last Month',
    'PrbUtilization': 'DL PRB Utilization in the Same Period Last Month',
    'rank': 'Ranking',
    'reduction': 'Restore',
    'savePic': 'Save as Image',
    'numberOfLoadCircuits': 'Total Bearer Circuits',
    'importantCustomers': 'VIP Customer',
    'generalCustomers': 'General Customer',
    'width': 'Width',
    'grid': 'Grid',
    'uptodateTime': 'Latest Data Time',
    'statistics': 'Statistics',
    'siteNumber': 'Total Sites',
    'gridNumber': 'Total Grids',
    'RRUNumber': 'Total RRUs',
    'BBUNumber': 'Total BBUs',
    'coverage': 'Coverage Status',
    'GoodSINR': 'Good SINR',
    'RSRPcoverage': 'RSRP Coverage Ratio',
    'ComprehensiveCoverage': 'Comprehensive Coverage Ratio',
    'OverlappingCoverage': 'Overlapping Coverage Condition',
    'Modular3Coverage': 'Mode 3 Coverage Ratio',
    'Overlappingcoverage': 'Overlapping Coverage Ratio',
    'DifferentNetworkForStandard': 'Off-net Benchmarking',
    'cPortRegion': 'C Side Area',
    'timeDelay': 'Time Delay',
    'DITO': 'DITO',
    'PLDT': 'PLDT',
    'GLODE': 'CLODE',
    'regional': 'ORG Region',
    'totalGrid': 'Total Grids',
    'SINRInferior': 'SINR Poor Quality',
    'WeakCoverage': 'Weak Coverage',
    '3Interference': 'Mode 3 Interference',
    'overlappingCoverage': 'Overlapping Coverage',
    'differentNetwork': 'Worse Than Off-net',
    'gridDistribution': 'Grid Dynamic Distribution',
    'fullRoute': 'Routing of Whole Process',
    'legendIllustration': 'Instructions for the Legends',
    'REGION': 'REGION',
    'GoodSINRthan': 'SINR Good Ratio',
    'thePhilippines': 'Philippines',
    'CQIgoodThan': 'CQI Good Ratio',
    'netAlarm': 'Network Alarm',
    'addInspection': 'Add Inspection Item',
    'patrolConfiguration': 'Inspection Configuration',
    'objectConfig': 'Object Configuration',
    'methodConfig': 'Method Configuration',
    'errorConfig': 'Abnormal Configuration',
    'addConditions': 'Add Condition',
    'disposalAdviceDec': 'Please check transmission optical path,optical module,enamel disc, and other devices',
    'aisleName': 'Channel Name',
    'fiberOpticCableName': 'Optical Cable Name',
    'fiberOpticCableNumber': 'Optical Cable Number',
    'fiberOpticCableType': 'Optical Cable Type',
    'hierarchicalRoutingList': 'Hierarchical Route List',
    'totalNumberOfSNC': 'Total SNCs',
    'totalNumberOfCrossConnections': 'Total Cross Connections',
    'totalTopologyConnections': 'Total Topology Connections',
    'objectName': 'Object Name',
    'protectionState': 'Protection Status',
    'SNC_level': 'SNC Level',
    'rsrpPoorCnt': 'Total RSRP Poor Quality Grids',
    'addTemplate': 'New Template',
    'patrolJobTemplateConfig': 'Inspection Job Template Configuration',
    'resourceData': 'Resource Data',
    'paramConfig': 'Parameter Configuration',
    'performComp': 'Meet Performance Targets',
    'IntactEquip': 'Device Serviceability Ratio',
    'equipDiag': 'Device Diagnosis',
    'isHidden': 'Hazards or Not',
    'choose': 'Select',
    'patrolOperationPlanConfig': 'Inspection Job Plan Configuration',
    'patrolJobTemplate': 'Inspection Job Template',
    'inspectionObjectSelection': 'Inspection Object Selection',
    'patrolItemSet': 'Inspection Item Setting',
    'cycleSet': 'Cycle Setting',
    'threshold': 'Threshold',
    'disposalWay': 'Disposal Method',
    'lifecycle': 'Execution Cycle',
    'verificationCycle': 'Validate Cycle',
    'hour': 'Hour',
    'sun': 'Day',
    'weeks': 'Week',
    'month': 'Month',
    'pleasePatrolKeyWord': 'Please enter inspection item key words',
    'gridSize': 'Grid Scale',
    'selAlarmHasNoMeetRecords': 'No Records Satisfying Operating Conditions In Selected Alarms!',
    'successful': 'Successful',
    'unsuccessful': 'Failed',
    'Monday': 'Monday',
    'Tuesday': 'Tuesday',
    'skip': 'Skip',
    'Wednesday': 'Wednesday',
    'Thursday': 'Thursday',
    'Friday': 'Friday',
    'Saturday': 'Saturday',
    'Sunday': 'Sunday',
    'enterTheQueryCriteriaName': 'Please enter query condition name',
    'forecastCovering': 'Forecast Coverage',
    'allAssignments': 'All Jobs',
    'transmissionNetwork': 'Transmission Network',
    'ipNetwork': 'IP Network',
    'coreNetwork': 'Core Network',
    'wirelessNetwork': 'Wireless',
    'operationMonitoringDetails': 'Job Monitor Detail',
    'totalNumber': 'Total',
    'normal': 'Normal',
    'hierarchicalRoute': 'Hierarchical Route',
    'totalOf': 'Total of',
    'toPerform': 'To Be Executed',
    'normalExecution': 'Normal Execution',
    'completes': 'Execution Complete',
    'receipt': 'Check In',
    'currentState': 'Current Status',
    'recently': 'Recent Execution',
    'completionTime': 'Completion Time',
    'diagramDescription': 'Diagram Description',
    'patrolAbnormalRanking': 'Top 10 Inspection Exceptions',
    'abnormalDevices': 'Total Abnormal Devices',
    'inspectionTime': 'Inspection Time',
    'portAlarm': 'Port Alarm',
    'performanceQuery': 'Performance Query',
    'bearerCircuit': 'Bearer Circuits',
    'routeList': 'Routing List',
    'occurrenceTime': 'Event Time',
    'clearState': 'Clear Status',
    'keyField1': 'Key Field 1',
    'workOrderNumber': 'Fault Ticket Number',
    'lightHarvestePower': 'Receiving Optical Power',
    'luminousPower': 'Transmitting Optical Power',
    'unavailableSeconds': 'Unavailable Second',
    'seriousBitErrorSeconds': 'Severely Errored Second',
    'bitErrorSeconds': 'Errored Second',
    'performing': 'Implementing',
    'performError': 'Exceptional Execution',
    'continuousAbnormal': 'Persistent Exception',
    'clearAllElements': 'Clear All Elements',
    'sector': 'Sector',
    'testInfo': 'Detection Information',
    'layerFilter': 'Layer Filtering',
    'areaLocation': 'Area Location',
    'testResults': 'Inspection Result',
    'inspectionOperationPlan': 'Inspection Job Plan',
    'checkItemConfig': 'Check Item Configuration',
    'testMethod': 'Inspection Method',
    'checkItemName': 'Detection Item Name',
    'coverage_area': 'Coverage',
    'crossover': 'Frequency',
    'directionAngle': 'Azimuth',
    'downtilt': 'Downlink Inclination Angle',
    'configurationMethod': 'Configuration Method',
    'associatedGridNumber': 'Total Associated Grids',
    'incident': 'Event',
    'customizationMethod': 'Customization Method',
    'geographicGridnumber': 'Total Geographical Grids',
    'additionalCoveragepoint': 'New Addtion of Coverage',
    'constructionDemandbank': 'Construction Requirement Repository',
    'profileInfo': 'Profile Information',
    'allObjectsOfAcceptance': 'All Acceptance Objects',
    'adjustPriority': 'Adjust Priority',
    'objectOfAcceptance': 'Acceptance Object',
    'planning': 'Planning',
    'originalCollectedInfo': 'Original Collection Information',
    'patrolItemsClassification': 'Inspection Item Classification',
    'priority': 'Priority',
    'TestingTime': 'Test Time',
    'workOrderStatus': 'Fault Ticket Status',
    'persistentAnomalies': 'Total Persistent Exceptions',
    'actualValue': 'Actual Value',
    'results': 'Result',
    'provinceLayer': 'Province Layer',
    'regionLayer': 'Region Layer',
    'sectorLayer': 'Sector Layer',
    'stationLayer': 'Station Layer',
    'gridLayer': 'Grid Layer',
    'networkCoverageQuality': 'Network Coverage Quality',
    'coverageRate': 'Coverage Ratio',
    'deviceSearchLocation': 'Device Retrieval Location',
    'toolBox': 'Tool Box',
    'frameSelection': 'Frame Selection',
    'ranging': 'Ranging',
    'alarmLocation': 'Alarm Location',
    'alarmTitle': 'Alarm Title',
    'faultyWorkOrderNo': 'Fault Ticket No.',
    'powerDownNumber': 'Total Power Downs',
    'powerDownRate': 'Power Down Ratio',
    'stationBreakNumber': 'Total Base Station Breakdowns',
    'stationBreakRate': 'Base Station Breakdown Ratio',
    'linkBreakage': 'Total Link Interruptions',
    'otherStationBreakNumber': 'Total Other Type of Base Station Breakdowns',
    'alarmFilter': 'Alarm Filtering',
    'stationBreak': 'Base Station Breakdown',
    'powerDown': 'Power Down',
    'linkBreak': 'Link Interruption',
    'other': 'Other',
    'deviceFilter': 'Device Filtering',
    'LTEStation': 'LTE Base Station',
    'LTESector': 'LTE Cell',
    '5GStation': '5G Base Station',
    '5Gsector': '5G Cell',
    'retirementState': 'Out of Service Status',
    'sectorDetailsPage': 'Sector Detail Page',
    'RRCConnectionAttempts': 'Total RRC Connection Attempts',
    'RRCConnectionSuccess': 'Total RRC Successful Connections',
    'maximumNumberOfRRCConnectedUsers': 'Total Maximum RRC Connection Users',
    'cellUplinkTraffic': 'Cell Uplink Traffic',
    'cellDownlinkTraffic': 'Cell Downlink Traffic',
    'PRBDownlinkAverageUtilization': 'PRB Downlink Average Utilization Ratio',
    'userDownlinkExperienceRate': 'User Downlink Experience Rate',
    'break': 'Break',
    'performanceStatistics': 'Performance Statistics',
    'AcceptanceSchemeTemplateConfiguration': 'Acceptance Scheme Template Configuration',
    'InspectionItemConfiguration': 'Inspection Item Configuration',
    'SchemeName': 'Plan Name',
    'PeriodicExecutionOrNot': 'Periodic Execution or Not',
    'DaysOfDuration': 'Total Duration Days',
    'TestTimePoint': 'Detection Time Point',
    'Checklist': 'Detection Item List',
    'ProgramTemplate': 'Solution Template',
    'CheckItem': 'Detection Item',
    'ThresholdSetting': 'Threshold Setting',
    'referenceValue': 'Baseline Value',
    'direction': 'Direction',
    'ResultSet': 'Result Setting',
    'SourceAttribute': 'Source Attribute',
    'numberOfGeneralCustomers': 'Total General Customers',
    'numberOfImportantCustomers': 'Total VIP Customers',
    'stockForecastcoveragePoint': 'Stock Forecasting Coverage Spot',
    'rural': 'Rural Area',
    'township': 'Town',
    'city': 'City',
    'outdoorAcerstation': 'Outdoor Macro Base Station',
    'smallStation': 'Micro Cell',
    'chamberSubsystem': 'Indoor Distributuion System',
    'coveredAreatype': 'Coverage Area Type',
    'installHeight': 'Mounting Height',
    'terminationDegree': 'Termination Degree',
    'HTable': 'H Code List',
    'officeInfo': 'Office Direction Information',
    'instructionList': 'Instruction List',
    'downScene': 'Distribution Scene',
    'operators': 'Operator',
    'Hcode': 'H Code',
    'areaCode': 'Area Code',
    'HCodeFile': 'H Code File',
    'updTemplate': 'Modify Template',
    'workArea': 'Working Area',
    'operaeGrade': 'Job Grade',
    'InstructionName': 'Instruction Name',
    'instructions': 'Instruction',
    'please': 'Please enter',
    'generatingCommand': 'Generate Instruction',
    'derivedInstruction': 'Export Instruction',
    'comfirmSubmit': 'Confirm Submission',
    'majorChoose': 'Please select speciality',
    'frame': 'Shelf',
    'channel': 'Slot',
    'physicalPort': 'Physical Port',
    'logicalPort': 'Logical Port',
    'typeName': 'Type Name',
    'code': 'Code',
    'all': 'All',
    'existsChoose': 'Data already exists, please select again',
    'businessScale': 'Business Scale',
    'pass': 'Pass',
    'noPass': 'Fail',
    'runIndicator': 'Running Indicator',
    'exeCommand': 'Execution Instruction',
    'editForecastcoveragePoints': 'Edit Forecasting Coverage Spot',
    'reExeCommand': 'Redo',
    'showInsFail': 'Display Failure Instruction',
    'showInsAll': 'Display All Instructions',
    'taskNo': 'Task Number',
    'business': 'Business',
    'taskScene': 'Task Scene',
    'executionList': 'Instruction List',
    'exeLog': 'Implementation Log',
    'bureauStationnumber': 'Total Sites',
    'model': 'Model',
    'totalNumberofGrid': 'Total Grids',
    'SINRqualityDifferencegridNumber': 'Total SINR Poor Quality Grids',
    'weakCovergridNumber': 'Total Weak Coverage Grids',
    'NumberOfinferiorAnddissimilarGrids': 'Total Worse Than Off-net Grids',
    'physicalCode': 'Physical Code',
    'logicCode': 'Logical Code',
    'thousandPeople': 'K',
    'dataUserNumThousand': 'Users (K)',
    'fourGigabytes': '4G Data (GB)',
    'insReport': 'Execution Report',
    'yesOrNo': 'Yes or No',
    'comfirPass': 'Confirm Pass or Not',
    'comfirmNoPass': 'Confirm Rejection or Not',
    'insTatal': 'Total Instructions',
    'queryMsg': 'Search Result',
    'geometry': 'Longitude and Latitude',
    'address': 'Address',
    'clearMap': 'Clear Map',
    'siteAlarm': 'Base Station Alarm',
    'sectorAlarm': 'Sector Alarm',
    'beforeRegionName': 'Area Name before Adjustment',
    'beforeRegionNum': 'Area Code before Adjustment',
    'exportSuccess': 'Export Successfully',
    'exportAddSuccess': 'Export New Template Successfully',
    'exportEditSuccess': 'Export Edit Template Successfully',
    'exportInsSuccess': 'Export Instruction Successfully',
    'tendencyChart': 'Tendency Chart',
    'detailsResources': 'Resource Detail',
    'sectorLongitude': 'Sector Longtitude',
    'networkType': 'Network Type',
    'inAndout': 'Indoor or Outdoor',
    'sectorLatitude': 'Sector Latitude',
    'problemSituation': 'Problem Situation',
    'wirelessParameters': 'Wireless Parameter',
    'activityInformation': 'Neighbour Cell Information',
    'KPItarget': 'KPI Indicator',
    'directionalRuleTable': 'Directed Rule List',
    'directionalRuleFile': 'Directed Rule File',
    'productId': 'Offer ID',
    'productSaleName': 'Offer Name',
    'rateGroup': 'Rating Group',
    'regularIndex': 'Rule Index',
    'protocolType': 'Protocol Type',
    'iPMask': 'IP/Mask',
    'startingPort': 'Start Port',
    'homeNetwork': 'Home Network',
    'SINRcoverage': 'SINR Coverage Ratio',
    'recentTime': 'Latest Completion Time',
    'rasterData': 'Grid Data',
    'addedForecastcoveragePoints': 'Add Forecast Coverage Spot',
    'selectResult': 'Select Result',
    'selectNetworkInfo': 'Please select ne information',
    'importHCode': 'Please import number data',
    'timeJudge': 'End time must be greater than start time',
    'CheckItemEdit': 'Edit Detection Item',
    'editAcceptanceSchemeTemplate': 'Acceptance Scheme Template Edit',
    'SelectPointInTime': 'Select Time Point',
    'gridIdentifier': 'Grid Number',
    'bureauStanddetails': 'Site Detail',
    'generateCommand': 'Please generate instruction',
    'NetworkRate': 'DownLink Throughput',
    'BarangayQuantity': 'Barangay Coverage',
    'PopulationCoverage': 'Population Coverage',
    '4GUserNumber': 'Total 4G Users',
    'RoamingUser': 'Roaming User',
    'RoamingTrafficIndex': 'Roaming Data',
    '4GUser': '4G User',
    '5Guser': '5G User',
    'BroadbandUser': 'WTTx User',
    'alternative': 'Optional',
    'selected': 'Selected',
    'originalReturnedResult': 'Original Response',
    'detectionConclusion': 'Detection Conclusion',
    'NeedToFillIn': 'Need to Fill in',
    'exportTemplate': 'Export Template',
    'ruleType': 'Rule Type',
    'endPort': 'End Port',
    'numberOfbusinessGrids': 'Total Business Grids',
    'businessIndicators': 'Business Indicator',
    'predictThenumberOfgrids': 'Total Forecast Grids',
    'jobDetail': 'Job Detail',
    'migrationObject': 'Migration Object',
    'migrationAlarm': 'Migration Alarm',
    'migrationPerformance': 'Migration Performance',
    'migrationBusiness': 'Migration Business',
    'jobSource': 'Source',
    'jobSpeciality': 'Speciality',
    'jobType': 'Type',
    'concernedLevel': 'Concerned Level',
    'notificationEndTime': 'Notification End Time',
    'notificationStartTime': 'Notification Start Time',
    'transmission': 'Transmission',
    'access': 'Access',
    'core': 'Core',
    'archived': 'Archived',
    'datas': 'Data',
    'resourceInformation': 'Resource Information',
    'migrationKeyword': 'Migration Keyword',
    'totalNumberOfItems': 'Total of Items',
    'abnormalNumbers': 'Total of Exceptions',
    'confirmor': 'Confirmor',
    'compareResult': 'Comparative Result',
    'detectionItem': 'Detection Item',
    'initialTime': 'Initial Time',
    'lastTime': 'Last Time',
    'initialDetectionValue': 'Initial Detection Value',
    'initialResult': 'Initial Result',
    'lastResult': 'Last Result',
    'lastDetectionValue': 'Last Detection Value',
    'alignmentResult': 'Comparison Result',
    'archivingPersonnel': 'Archived By',
    'archivingTime': 'Archived Time',
    'archivingType': 'Archived Type',
    'archivingRemark': 'Archived Remark',
    'maskWarningOrNot': 'Shield Warning or Not',
    'maskAlarmOrNot': 'Shield Alarm or Not',
    'objectFiltering': 'Object Filtering',
    'jobLevel': 'Level',
    'alarmAttribut': 'Alarm Attribute',
    'jobCycle': 'Cycle',
    'manualConfirmation': 'Manual Confirmation',
    'businessAcquisition': 'Business Collection',
    'locationObject': 'Location Object',
    'locationAlarm': 'Location Alarm',
    'performanceAcquisition': 'Performance Collection',
    'alarmSynchronized': 'Alarm Synchronization',
    'alarmConfirm': 'Alarm Acknowledge',
    'alarmTerminated': 'Alarm Terminate',
    'expandAll': 'Expand All',
    'packUpAll': 'Collapse All',
    'advancedOperations': 'Advanced Operation',
    'networkManagementID': 'Network Management ID',
    'opticalCableSegmentName': 'Optical Cable Segment Name',
    'numberOfMaskedAlarms': 'Total of Shield Alarms',
    'stateRemark': 'Status Remark',
    'fiberCoreNumber': 'Fiber Core Number',
    'DITORSRPCoverage': 'DITO RSRP Coverage Ratio',
    'PLDTRSRPCoverage': 'PLDT RSRP Coverage Ratio',
    'GLOBERSRPCoverage': 'GLOBE RSRP Coverage Ratio',
    'ThisFunctionIsNotAvailable': 'Function is not available currently',
    'MethodDescription': 'Method Description',
    'MethodDescriptionDesc': 'This method is used to compare the size of two values. The parameters include the actual value, threshold value, and pass standard (1 big pass and 2 small pass)',
    'lastData': 'This is the last data, unable to move down',
    'firstData': 'This is the first data, unable to move up',
    'nameInput': 'Please enter name',
    'categoryInput': 'Please select the category',
    'adjustSuccess': 'Adjust Priority Successfully!',
    'ruleDescrInput': 'Please enter rule description',
    'checkObjTypeInput': 'Please select object type',
    'checkWayInput': 'Please select inspection method',
    'dealSuggestInput': 'Please enter disposal suggestion',
    'vendorIdInput': 'Please select vendor',
    'neTypeIdInput': 'Please select device type',
    'neModelIdInput': 'Please select device model',
    'cardTypeIdInput': 'Please select card type',
    'cardModelIdInput': 'Please select card model',
    'portTypeIdInput': 'Please select port type',
    'checkProErrorDealListInput': 'Please select notification mode',
    'switchProfessional': 'If the specialty is re-selected, the configured conditions will be cleared, continue or not',
    'crossAreaCoverage': 'Cross Border Coverage',
    'customerComplaint': 'User Complaints',
    'networkQuality': 'Network Quality',
    'crossAreaCoverageRate': 'Cross Border Coverage Ratio',
    'mode3': 'Mode 3 Ratio',
    'wirelessVolteVoiceQuality': 'Wireless VoLTE Voice Quality',
    'objectSetInput': 'Object setting adjustment will clear optional inspection items and selected inspection items, continue or not',
    'objectSetInputPlease': 'Object setting can not be empty, please configure at least one',
    'fluctuationRatio': 'Fluctuation Ratio (%)',
    'cellNum': 'Total Cells',
    'bussinessMaintenir': 'Business Maintain',
    'overlayGridAnalysis': 'Coverage Grid Analysis',
    'problemSolveStatistics': 'Problem-Solving Statistics',
    'bussinessAccessExperience': 'Business Access Experience',
    'rrcSuccessRate': 'RRC Success Ratio',
    '700MExperienceRate': '700M Experience Ratio',
    'ERABDrops': 'ERAB Drop Ratio',
    'secondExperienceRate': '2.1G Experience Ratio',
    'wirelessVolteConnRate': 'Wireless VoLTE Call Completing Ratio',
    'wirelessVolteDropRate': 'Wireless VoLTE Call Drop Ratio',
    'mode3Inter': 'Mode 3 Interference',
    'flowGB': 'Data (GB)',
    'roadTestDropRate': 'DT Call Drop Ratio',
    'roadTestSwitchingSuccessRate': 'DT Switching Success Ratio',
    'userNumber': 'Total Users',
    'networkCoverage': 'Network Coverage Ratio',
    'migrationKeyValue': 'Migration Key Value',
    'equipmentIPAddress': 'Device IP Address',
    'alarmRecoverTime': 'Alarm Recover Time',
    'dataMsgInput': 'Please select more than one data information',
    'seleted': 'Selected',
    'enableInput': 'Please select whether to enable',
    'specialtyName': 'Speciality Name',
    'faultTitle': 'Fault Title',
    'deviceInformation': 'Device Information',
    'repairAdvice': 'Repair Suggestion',
    'everyMinute': 'Per Minute',
    'every': 'Every',
    'specifiedRange': 'Specified Range',
    'specifyValue': 'Specified Value',
    'perHour': 'Per Hour',
    'everyDay': 'Daily',
    'lastDayMonth': 'Last Day of Current Month',
    'workDay': 'Closed Work Day to the Specified Date',
    'notSpecify': 'Unspecified',
    'everyWeek': 'Weekly',
    'lastWeek': 'Last Week of Current Month',
    'firstWeek': 'the First Week',
    'secondWeek': 'the Second Week',
    'thirdWeek': 'the Third Week',
    'fourthWeek': 'the Fourth Week',
    'fiveWeek': 'the Fifth Week',
    'everyMonth': 'Monthly',
    'taskPlanModelInput': 'Please select inspection job template',
    'cronExpSet': 'Please configure execution cycle',
    'takeEffectTime': 'Effective end time must be greater than start time',
    'thresholdValue': 'Threshold',
    'dealWayChoose': 'Please select at least one type of disposal method',
    'dealWayInput': 'Please select at least one disposal method',
    'patrolItemInput': 'Select at least one inspection item setting',
    'taskPhaseName': 'Task Activity Name',
    'numberOfCollecteRecords': 'Total Collection Records',
    'acquisitionSize': 'Collection Size',
    'collecteStatus': 'Collection Status',
    'fileResoluteDetail': 'File Parsing Detail',
    'fileMergedetail': 'File Merge Detail',
    'signInSuccess': 'Congratulations,sign in succeeded!',
    'BearerNetwork': 'Bearer Network',
    'ThousandKmOptacalCable': 'Optical Cable',
    'IPCoreNode': 'IP Core',
    'OTNCoreNode': 'OTN Core',
    'OTNBackBoneRing': 'IPRAN B',
    'WirelessNetwork': 'Wireless Network',
    '4GBaseStationNumber': 'Total 4G Base Stations',
    '5GBaseStationNumber': 'Total 5G Base Stations',
    'CloudInfrastructure': 'Cloud Infrastructure',
    'VirtualMachine': 'Virtual Machine',
    'CPUCore': 'CPU',
    'Memory': 'Memory',
    'Storage': 'Storage',
    'Bandwidth': 'Bandwidth',
    'OperationAndMaintenanceIndex': 'Operation and Maintenance Indicator',
    'IPTraffic': 'IP Traffic',
    'VoLTETraffic': 'VoLTE Traffic',
    'MMEAttachedUsers': 'MME Additional User',
    'PagingSuccessRate': 'Paging Success Ratio',
    'HealthDegree': 'Health Degree',
    'TotalNumberOfWorlOrders': 'Total Project Orders',
    'NumberOfOpeningOrders': 'Total Openning Orders',
    'zoomIn': 'Zoom Out',
    'zoomOut': 'Zoom In',
    'returnScreen': 'Current Screen',
    'fullScreen': 'Full Screen',
    'fullScreen2': 'Full Screen',
    'search': 'Search',
    'move': 'Drag and Drop',
    'tab': 'Form',
    'element': 'Element',
    'default': 'Default',
    'upperAndLowerLayout': 'Upper and Lower Layout',
    'leftAndRightLayout': 'Left and Right Layout',
    'circularLayout': 'Annular Layout',
    'orthogonalLayout': 'Orthogonal Layout',
    'sectorLayout': 'Sector Layout',
    'treeLayout': 'Tree Layout',
    'classHierarchy': 'Classification Level',
    'loopLayout': 'Circular Layout',
    'networkAtta': 'Attribute Information',
    'topologyInfo': 'Additional Information',
    'modelName': 'Model Name',
    'recoverTime': 'Recover Time',
    'faultNumber': 'Fault Ticket Number',
    'ocName': 'OC Name',
    'goOnDispatch': 'Continue Dispatch',
    'exit': 'Exit',
    'optimizationDemand': 'Optimization Requirement',
    'displayMode': 'Display Mode',
    'tabulation': 'List',
    'MRCoverage': 'MR Coverage Ratio',
    'mainPlot': 'Main Cell',
    'optimizeLibrary': 'Optimizing Library',
    'mainPlotname': 'Main Cell Name',
    'PLDTCoverage': 'PLDT Coverage Ratio',
    'GLOBECoverage': 'GLOBE Coverage Ratio',
    'selectOptimizationscheme': 'Select Optimization Solution',
    'OptimizationType': 'Optimizing Type',
    'preOptimizationcoverage': 'Coverage Ratio Before Optimization',
    'requirementTypes': 'Demand Type',
    'optimizedCoverage': 'Coverage Ratio After Optimization',
    'optimizationScheme': 'Optimization Solution',
    'optimizationResults': 'Optimization Result',
    'dataSource': 'Data Source',
    'GLODECoverage': 'GLODE Coverage Ratio',
    'ParamSet': 'Parameter Setting',
    'AdjustOrder': 'Adjustment Order',
    'GLOBE': 'GLOBE',
    'runStatus': 'Running Status',
    'principal': 'Person in Charge',
    'test': 'Test',
    'stopService': 'Pause Service',
    'recoverService': 'Resume Service',
    'offlineService': 'Offline Service',
    'onlineService': 'Online Service',
    'attributionInfo': 'Associated Information',
    'labelInfo': 'Tag Information',
    'reqHeader': 'Request Header',
    'reqParams': 'Request Parameter',
    'respResult': 'Response Result',
    'testConfig': 'Test Configuration',
    'statusCode': 'Status Code',
    'accessDenied': 'Access Restriction',
    'servName': 'Service Name',
    'version': 'Version Number',
    'servURL': 'Service URL',
    'reqMethodName': 'Request Method Name',
    'heartbeatUrl': 'Heartbeat URL',
    'timeout': 'Response Timeout',
    'apiProtooc': 'Interface Protocol',
    'apiType': 'API Type',
    'invokeType': 'Invocation Method',
    'isRequired': 'Required',
    'description': 'Description',
    'paramType': 'Parameter Type',
    'location': 'Position',
    'defaultValue': 'Default Value',
    'reqDemo': 'Request Demo',
    'respDemo': 'Response Demo',
    'frequencylimit': 'Frequency Limitation',
    'trafficLimit': 'Traffic Limitation',
    'second': 'Second',
    'capabilitySafetyLevel': 'Ability Security Level',
    'singleAccessMax': 'Single Access Traffic Threshold',
    'serviceTest': 'Service Test',
    'deadline': 'Deadline',
    'addService': 'Add Service',
    'assignPrincipal': 'Designated Officer',
    'spectacular': 'Kanban',
    'selectAll': 'Select All',
    'collect': 'Collect',
    'cancelCollect': 'Cancel Collection',
    'applicationSharing': 'Application Sharing',
    'addCart': 'Add to Service Cart',
    'reqContent': 'Request Content',
    'respCode': 'Response Code',
    'respContnet': 'Response Content',
    'respHeader': 'Response Header',
    'resultCheck': 'Result Verification',
    'editService': 'Edit Service',
    'viewService': 'View Service',
    'selectAssignPrincipalTip': 'Please select the data for which the person in charge needs to be specified',
    'systemHint': 'System Hints',
    'confirmCoverServOrNot': 'Confirm to restore the service or not',
    'confirmOfflineServOrNot': 'Confirm to log off the service or not?',
    'confirmOnlineServOrNot': 'Confirm to launch the service or not?',
    'checkThedetails': 'View Detail',
    'fullLifecycle': 'Whole Life Cycle',
    'ruleRelateAlarm': 'Association Rule',
    'ruleRelateFilter': 'Filter Rule',
    'ruleRelateAutoMsg': 'Automatic SMS Rule',
    'ruleAutoTicket': 'Automatic Dispatch Rule',
    'tabOptions': 'Tag Options',
    'closeAll': 'Close All',
    'closeOther': 'Close Others',
    'logout': 'Log-out',
    'NetworkIndicators': 'Network Indicator',
    'CutNumber': 'Total Migrations',
    'FailureRate': 'Failure Ratio',
    'recommendedPlan': 'Recommended Solution',
    'generateSolution': 'Generate Solution',
    'autoOptimization': 'Automatic Optimization',
    'beforeOptgridCoverage': 'Grid Coverage Ratio Before Optimization',
    'dipangleCfmval': 'Confirmation Value of Downlink Inclination Angle',
    'dipangleRecval': 'Recommended Value of Downlink Inclination Angle',
    'dipangleOrival': 'Initial Value of Downlink Inclination Angle',
    'artificialOptimization': 'Manual Optimization',
    'communityInfo': 'Cell Information',
    'parameterInfo': 'Parameter Information',
    'mainCellCurrVal': 'Main Cell（Current Value）',
    'mainCellReferVal': 'Main Cell（Reference Value）',
    'firstPowerCellCurrVal': 'Main Strong Neighbour Cell（Current Value）',
    'firstPowerCellReferVal': 'Main Strong Neighbour Cell（Reference Value）',
    'secPowerCellCurrVal': 'Secondary Neighbour Cell（Current Value)',
    'secPowerCellReferVal': 'Secondary Neighbour Cell（Reference Value)',
    'rsSendPower': 'RS Transmit Power',
    'miniReceptionLevel': 'The Minimum Received Power Level',
    'direcAngleOriginalVal': 'Initial Value of Azimuth',
    'direcAngleReferVal': 'Recommended Value of Azimuth',
    'direcAngleAffirmVal': 'Confirmation Value of Azimuth',
    'adjacentRegionIsMatch': 'Neighbour Cell Matched or Not',
    'top1AdjacentRegion': 'TOP1 Neighbour Cell',
    'top2AdjacentRegion': 'TOP2 Neighbour Cell',
    'constructionRequirement': 'Construction Requirement',
    'coverageDistrict': 'Coverage Area',
    'optimizationEffectEvaluation': 'Assessment of Optimization',
    'connectSuccessRate': 'Connection Success Ratio',
    'contextDropRate': 'Context Drop Ratio',
    'userFlow': 'User Side Traffic',
    'top3AdjacentRegionCoverRate': 'TOP3 Neighbour Cell Coverage Ratio',
    'top3AdjacentRegionName': 'TOP3 Neighbour Cell Name',
    'top2AdjacentRegionCoverRate': 'TOP2 Neighbour Cell Coverage Ratio',
    'top2AdjacentRegionName': 'TOP2 Neighbour Cell Name',
    'top1AdjacentRegionCoverRate': 'TOP1 Neighbour Cell Coverage Ratio',
    'top1AdjacentRegionName': 'TOP1 Neighbour Cell Name',
    'power': 'Power',
    'confirmSave': 'Confirm Saving',
    'flowType': 'Preprocess Type',
    'alarmTypeDesc': 'Alarm Type',
    'addPretreatConfig': 'Add Preprocessing Rule Configuration',
    'editPretreatConfig': 'Edit Preprocessing Rule Configuration',
    'copyPretreatConfig': 'Copy Preprocessing Rule Configuration',
    'viewPretreatConfig': 'View Preprocessing Rule Configuration',
    'timeLimit': 'Transfinite Frequency',
    'avoidTime': 'Avoidance Period',
    'flowName': 'Process Name',
    'flowId': 'Process ID',
    'requestTime': 'Request Time Limit',
    'timeSpan': 'Request Interval',
    'clientKey': 'Client Key',
    'flowVersion': 'Flow Version',
    'inputParams': 'Request Parameter Configuration',
    'serCode': 'Service Code',
    'resultCode': 'Result Code',
    'resultDesc': 'Result Description',
    'resultType': 'Result Type',
    'resultInfo': 'Additional Information Configuration',
    'outputInfo': 'Output Attribute Configuration',
    'resultConfig': 'Result Configuration',
    'outputInfoConfig': 'Output Information',
    'processConfig': 'Process Configuration',
    'beforeAdjustment': 'Before Adjustment',
    'primaryCellCoverage': 'Main Cell Coverage Ratio',
    'gridCoverage': 'Grid Coverage Ratio',
    'afterAdjustment': 'After Adjustment',
    'confirmFileFormat': 'Only filename extension in xlsx format are allowed to upload',
    'afterAdjGridCoverRate': 'Grid Coverage Ratio After Optimization',
    'assessmentResult': 'Assessment Result',
    'assessEndTime': 'Assessment End Time',
    'assessBeginTime': 'Assessment Start Time',
    'CLOBE': 'CLOBE',
    'inferiorToglobe': 'Worse Than GLOBE',
    'inferiorTopldt': 'Worse Than PLDT',
    'AREA': 'AREA',
    'flowNameInput': 'Please enter process name',
    'flowIdInput': 'Please enter process id',
    'serCodeInput': 'Please enter service code',
    'serNameInput': 'Please enter service name',
    'resultCodeInput': 'Please enter result code',
    'resultDescInput': 'Please enter result description',
    'resultTypeInput': 'Please select result type',
    'priorityLvlInput': 'Please enter priority',
    'alarmTypeInput': 'Please select alarm type',
    'flowTypeInput': 'Please select preprocessing type',
    'waitTimeInput': 'Please enter delay time',
    'limitNumInput': 'Please enter transfinite frequency',
    'timeLimitInput': 'Please enter request time limit',
    'flowVersionInput': 'Please enter flow version',
    'inputParamsInput': 'Please enter request parameter configuration',
    'resultConfigOper': 'Please select a result configuration to operate',
    'onlyResultConfig': 'Please configure at least one result configuration',
    'calculatingThegrid': 'Calculating Grid',
    'pretreatment': 'Preprocessing Rule',
    'overlapCoverageratio': 'Overlapping Coverage Proportion',
    'confirmSelectObject': 'Please check a migration alarm',
    'confirmHasSelectedObject': 'Manually confirm the selected object or not',
    'confirmAtLeastObject': 'Please check at least one migration object',
    'confirmItemAtLeast': 'Please check at least one detection item',
    'performanceItemLimit': 'No More Than 10 Real-time Performance Collection Detection Item',
    'serviceItemLimit': 'No More Than 10 Real-time Business Collection Detection Item',
    'checkDetail': 'Detection Detail',
    'units': 'Unit',
    'checkRuler': 'Detection Rule Description',
    'checkResList': 'Detection Result List',
    'checkValue': 'Detection Value',
    'log': 'Log',
    'checkLog': 'Test Log',
    'roadTestinfo': 'Drive Test Information',
    'averageRSRP': 'Average RSRP',
    'averageSINR': 'Average SINR',
    'connectionRatio': 'Call Completing Ratio',
    'droppedRatio': 'Call Drop Ratio',
    'specialityType': 'Speciality Type',
    'paramVal': 'Parameter Value',
    'flowStatus': 'Process Status',
    'conclusionCode': 'Conclusion Code',
    'conclusionDesc': 'Conclusion Description',
    'errorMsg': 'Error Message',
    'reqTime': 'Request Time',
    'respTime': 'Response Time',
    'keywordNoEmp': 'Key word can not be empty',
    'taskId': 'Task ID',
    'enterFlowId': 'Please enter process instance id',
    'srvStatus': 'Service Status',
    'attAttribute': 'Additional Attribute',
    'reqInstruction': 'Request Instruction',
    'respInstruction': 'Response Instruction',
    'link': 'Activity',
    'diagnosisDetail': 'Diagnose Detail',
    'flowInfo': 'Process Information',
    'flowDemo': 'Process Instance',
    'diagnosisTime': 'Diagnose Time',
    'noSubZero': 'Days can not be less than 0',
    'cannotSubZero': 'Not less than 0',
    '4GConnectSuccessRate': '4G Call Completion Ratio',
    '5GConnectSuccessRate': '5G Call Completion Ratio',
    'beforeOptimizationRate': 'Before Year-On-Year Optimization',
    'coverageTrend': 'Coverage Ratio Trend',
    'statementOfAccount': 'Statement',
    'service': 'Service Business',
    'Service': 'Service Indicator',
    'evaluationEnd': 'Assessment Finished',
    'evaluationStart': 'Assessment Start',
    'createQuaGrid': 'Generate Poor Call Quality Grid',
    'whetheToConfirmSubmission': 'Confirm submission or not',
    'enterAdvice': 'Please enter suggestion',
    'advice': 'Propose',
    'currentValue': 'Current Value',
    'reference': 'Reference Value',
    'mainPowerCell': 'Main Strong Neighbour Cell',
    'secondPowerCell': 'Secondary Neighbour Cell',
    'interact': 'Interactive Mode',
    'methodChoose': 'Method Selection',
    'kafkaTheme': 'Kafka Topic',
    'Synchronous': 'Synchronization',
    'Asynchronous': 'Asynchronous',
    'kafkaIdInput': 'Please select kafka topic',
    'fetchMethod': 'Please select data access method',
    'judgeMethod': 'Please select judgement method',
    'prameInput': 'Please select parameter list',
    'methodSetChoose': 'Please select method setting',
    'resultSetConfig': 'Please configure full result setting',
    'fetchMethodConfig': 'Please configure parameter list of full data access method',
    'judgeMethodConfig': 'Please configure parameter list of full judgement method',
    'methodSetConfig': 'Please configure parameter list of full method setting',
    'lifecycleState': 'Lifecycle Status',
    'uplinkDeviceIP': 'Uplink Device IP',
    'uplinkDeviceName': 'Uplink Device Name',
    'noRemark': 'No Description Currently',
    'optimizeThedetails': 'Optimization Detail',
    'netRobustness': 'Network Robustness',
    'alarmFaultOvertime': 'Timeout Ratio',
    'keyFaultNum': 'Total Key Faults',
    'modifier': 'Modified By',
    'modifierTime': 'Modified Time',
    'phenomenon': 'Phenomenon',
    'commonSearch': 'Common Search',
    'testResult': 'Test Result',
    'paramName': 'Parameter Name',
    'apply': 'Apply',
    'platformAudit': 'Platform Audit',
    'applyExplain': 'Application Description',
    'addIP': 'Add IP',
    'applyInfo': 'Application Information',
    'accessIP': 'Access IP',
    'choosedServices': 'Selected Service',
    'serviceMenu': 'Service Catalog',
    'keepAtleastOneService': 'Keep at least one service',
    'timeoutSecond': 'Response Timeout(s)',
    'singleAccessMaxMb': 'Single Access Data Threshold(MB)',
    'serUrlInput': 'Please enter service url',
    'serRemarkInput': 'Please enter remark',
    'setTheFilter': 'Set Filter',
    'microWave': 'Backbone Microwave',
    'Region': 'Region',
    'Province': 'PROVINCE',
    'City': 'CITY',
    'newTest': 'A New Test',
    'benTest': 'Retest after Crash',
    'publish': 'Publish',
    'timeoutLimit': 'Timeout Period',
    'dealObject': 'Processing Object',
    'linkCode': 'Activity Code',
    'linkInfo': 'Activity Information',
    'readingLink': 'To Read Activity',
    'manualLink': 'Manual Activity',
    'autoLink': 'Automatic Activity',
    'testOne': 'Test a piece of data',
    'propertyName': 'Attribute Name',
    'addListItem': 'Add List Item',
    'outSideLink': 'External link',
    'notifiy': 'Notification',
    'pleaseInputLinkName': 'Please enter activity name',
    'pleaseInputLinkCode': 'Please enter activity code',
    'isAsync': 'Asynchronous or Not',
    'timeoutJump': 'Overdue Jump',
    'requestTimeout': 'Request Timeout',
    'retryTime': 'Total Retries',
    'retryDelay': 'Retry Delay',
    'dealFieldType': 'Failure Handling Policy',
    'endFlow': 'End Process',
    'endLink': 'End Activity',
    'policyType': 'Decision Type',
    'subFlowSource': 'Subprocess Source',
    'subFlowSourceMess': 'Select the value of the field to provide the sub process code!',
    'chooseSubFlow': 'Select Subprocess',
    'fieldConfig': 'Field Configuration',
    'newTestTest': 'Retest',
    'content': 'Content',
    'onlineUsersNumber': 'Total Online Users',
    'fixedValue': 'Fixed Value',
    'formField': 'Form Field',
    'physicalSiteNumber': 'Total Physical Sites',
    'pleaseInputHeader': 'Please enter request header',
    'pleaseChooseType': 'Please select type',
    'pleaseInputParam': 'Please enter parameter name',
    'pleaseInputResult': 'Please enter result id',
    'outsideLinkInfo': 'Note: single selection, multiple selection, drop-down, drop-down multiple selection fields provide display content, and other fields provide values',
    'resultState': 'Result ID',
    'pleaseInputParamType': 'Please select parameter type',
    'pleaseInputParamPosition': 'Please select parameter position',
    'pleaseChooseValueSource': 'Please select parameter source',
    'pleaseInputUrl': 'Please enter link address',
    'canChooseOper': 'Optional Operation',
    'withDrawable': 'Withdrawable',
    'convertible': 'Transferable',
    'recoveryRule': 'Recycle Rule',
    'receiptRule': 'Check in Rule',
    'manyOrders': 'Multiple Fault Tickets',
    'passRate': 'Pass Ratio',
    'intefaceConfig': 'Interface Configuration',
    'doPersion': 'Handler',
    'operaConfig': 'Operation Configuration',
    'anyLink': 'Any Activity',
    'lastLink': 'Next Activity',
    'allGetOrder': 'All Check In',
    'oneGetOrder': 'Single Check In',
    'huiqiang': 'Countersign',
    'theDayOfWork': 'Working Day',
    'theDayOfNature': 'Natural day',
    'handUpLimit': 'Hang-up Time Limit',
    'dataOrder': 'Data Requirement',
    'wordTarget': 'Work Target',
    'workContent': 'Work Content',
    'notifyContent': 'Notification Content',
    'sendTo': 'Send To',
    'notifyRange': 'Notification Range',
    'notifyTitle': 'Notification Title',
    'notifyType': 'Notification Type',
    'triggerRule': 'Trigger Rule',
    'dealPerson': 'Handler',
    'stepDeal': 'Step By Step Handling',
    'condition': 'Condition',
    'choosePersion': 'Selection Officer',
    'dealPersionType': 'Handler Type',
    'endCondition': 'Termination Condition',
    'allLevel': 'Level',
    'defaultConfig': 'Default Configuration',
    'shuxingValue': 'Attribute Value',
    'showContent': 'Display Content',
    'testetst': 'Test and Test',
    'errorNum': 'Total Exceptions',
    'createBeginEvent': 'Create Start Event',
    'createEndEvent': 'Create Aggregation Node',
    'createGroupEvent': 'Create End Event',
    'createCondition': 'Create Condition Judgement',
    'createParalEvent': 'Create Parallel Branch',
    'createManaulEvent': 'Create Manual Activity',
    'createAutoEvent': 'Create Automatic Activity',
    'createSubFlow': 'Create Dynamic Subprocess',
    'createReadEvent': 'Create to read activity',
    'createSubEvent': 'Create Subprocess Activity',
    'createServiceMenu': 'Create service catalog Activity',
    'pleaseConfigCondition': 'Please configure condition judgment activity first',
    'pleaseLinkLastLink': 'Please connect the previous activity first',
    'lineConfig': 'Connection Configuration',
    'subFlowConfig': 'Subprocess Configuration',
    'policyConfig': 'Decision Condition Configuration',
    'conditionLinkConfig': 'Conditional Judgement Activity Configuration',
    'contain': 'Contain',
    'notContain': 'Not Contain',
    'conditionLink': 'Conditional Judgement Activity',
    'branchLink': 'Branch Activity',
    'groupLink': 'Aggregation Activity',
    'subFlowLink': 'Subprocess Activity',
    'diySubFlow': 'Dynamic Subprocess',
    'stop': 'Stop',
    'physicalSite': 'Physical Site',
    'object': 'Object',
    'linkStart': 'Activity Start',
    'linkEnd': 'Activity End',
    'bigThan': 'Greater than',
    'smallThat': 'Less than',
    'bigEqual': 'Greater than or equal to',
    'smallEqual': 'Less than or equal to',
    'processMouldCode': 'Process Template Code',
    'innerLoop': 'Inner Loopback',
    'outerLoopBack': 'Outer Loopback',
    'releaseLoop': 'Release Loopback',
    'acyclicBack': 'No Loopback',
    'SMSNotification': 'SMS Notification',
    'subAlarm': 'Associated Alarm',
    'hasCutover': 'Migrated',
    'processLinkName': 'Process Activity Name',
    'spendTime': 'Time Spend',
    'timeInterval': 'Time Interval',
    'alarmNumber': 'Total Alarms',
    'addUpgrade': 'Add Escalation Rule',
    'editUpgrade': 'Edit Escalation Rule',
    'copyUpgrade': 'Copy Escalation Rule',
    'viewUpgrade': 'View Escalation Rule',
    'collectionType': 'Collection Type',
    'alarmFor': 'Fault Duration',
    'flashTime': 'Short Interruption Time',
    'failureTimes': 'Total Short Interruptions',
    'alarmStateSynchronization': 'Alarm Status Synchronization',
    'serviceSearch': 'Service Query',
    'updateAlarmFlag': 'Escalate Alarm Identity',
    'updateAlarmLevel': 'Escalate Alarm Severity',
    'intervalTimeInput': 'Please enter time interval',
    'alarmnumInput': 'Please enter the total alarms',
    'faultDurationInput': 'Please enter fault duration',
    'brokenTimeInput': 'Please enter short interruption time',
    'brokenCountInput': 'Please enter the total short interruptions',
    'alarmidentifyInput': 'Please enter the alarm escalation identity',
    'severityChoose': 'Please select the alarm escalation severity',
    'rootTermChild': 'Termination of Alarm Escalation -> Terminate Fault Alarm',
    'childTermRoot': 'Termination of Fault Alarm -> Terminate Alarm Escalation',
    'alarmUpgradeRule': 'Alarm Escalation Rule',
    'alarmNotZero': 'Fault duration,the total alarms can not be 0 at the same time',
    'alarmThanFlash': 'The total alarms should greater than or equal to the total short interruptions',
    'alarmNotZeroTwo': 'Fault duration,the total alarms,and the total short interruptions can not be 0 at the same time',
    'testRuleDesc': 'Test Rule Description',
    'TimeToBeTest': 'Planned Detection Time',
    'actualTestTime': 'Actual Detection Time',
    'InvalidFormat': 'Format Error',
    'mengTest': 'I test one',
    'execPlanTime': 'Execute Plan Time',
    'Advanced': 'Advanced',
    'ctp': 'CTP',
    'taskWorkNo': 'Task No.',
    'reviewResults': 'Audit Result',
    'taskStatus': 'Task Status',
    'ruleJson': 'Rule JSON',
    'filtrationCondition': 'Filter condition or not',
    'addRule': 'Add Rule',
    'editRule': 'Edit Rule',
    'EnableOrDisable': 'Enable/Disable',
    'jsonInput': 'Please input correct JSON data',
    'redefineRuleConfig': 'Redefinition Rule Configuration',
    'rulesEntity': 'Rule Entity',
    'codeChoose': 'Please select a theme',
    'ruleJsonInput': 'Please enter rule JSON',
    'addRedefine': 'Add Redefinition Rule',
    'editRedefine': 'Edit Redefinition Rule',
    'copyRedefine': 'Copy Redefinition Rule',
    'viewRedefine': 'View Redefinition Rule',
    'theam': 'Theme',
    'redefineConfig': 'Please select a redefinition rule',
    'redefineConfigMore': 'Please select more than one redefinition rule',
    'redefineSet': 'Please configure a redefinition rule',
    'ruleEnDel': 'Rule entity can not be deleted',
    'viewRule': 'View Rule',
    'alarmDefefine': 'Alarm Redefinition Rule',
    'ruleNameNo': 'Rule name can not be repeated, please enter again',
    'topo': 'Topology',
    'failureRatio': 'Fault Ratio',
    'complaintVolume': 'Complaint Volume',
    'processCoding': 'Process Code',
    'ifTheTimeout': 'Timeout or Not',
    'timeConsuming': 'Time Consumption',
    'taskStartTime': 'Task Start Time',
    'taskEndTime': 'Task End Time',
    'taskCoding': 'Task Code',
    'latestCycle': 'Latest Cycle',
    'username': 'Username',
    'telephoneNumber': 'Telephone Number',
    'searchAllSubtree': 'Search entire subtree',
    'fullName': 'Full Name',
    'organization': 'Institutional Framework',
    'whetherTheLeadership': 'Whether The Leadership',
    'passwordUpdateTime': 'Password Update Time',
    'lastLoginTime': 'Last Login Time',
    'wholeOpen': 'Network-wide Openning',
    'InternalUse': 'System Interior',
    'roleInformation': 'Role Information',
    'permissionsInformation': 'Permissions Information',
    'associatedUsers': 'Associated User',
    'userGroupName': 'User Group Name',
    'userGroupAllName': 'Full Name of User Group',
    'userGroupCategory': 'User Group Category',
    'parentUserGroup': 'Parent User Group',
    'descriptiveInformation': 'Description Information',
    'workConfirm': 'Job Confirmation',
    'faultRepairOrder': 'Fault Ticket',
    'timeOut': 'Timeout',
    'processing': 'Processing',
    'hang': 'Hang-up',
    'theCPUAllocationRate': 'CPU Usage',
    'CPUUtilization': 'CPU Allocation Ratio',
    'memoryAllocation': 'Memory Usage',
    'memoryUsage': 'Memory Allocation Ratio',
    'storageAllocationRate': 'Storage Usage',
    'storageUsage': 'Storage Allocation Ratio',
    'PCServer': 'PC Server',
    'repairOrder': 'Fault Ticket',
    'orginalAlarmTime': 'Original Alarm Time',
    'confirmAtLeastAlarm': 'Please check at least one migration alarm',
    'falutComp': 'Fault Complaint',
    'RoamingRatio': 'Roaming Ratio',
    'tableName': 'Table Name',
    'dissipateTime': 'Consumption Time',
    'messageID': 'Message ID',
    'cursor': 'Cursor',
    'flag': 'ID',
    'synchronizationTime': 'Synchronization time',
    'checkMessage': 'View Message',
    'router': 'Router',
    'switches': 'Switch',
    'firewall': 'Firewall',
    'flowIn': 'Inflow',
    'flowOut': 'Outflow',
    'totalCPUCapacity': 'Total CPU Capacity',
    'totalMemoryCapacity': 'Total Memory Capacity',
    'totalStorageCapacity': 'Total Storage Capacity',
    'checkTemplate': 'Acceptance Template',
    'checkItem': 'Acceptance Item',
    'linkToDetails': 'Link to details',
    'totalAlarmEquipments': 'Total Alarm Devices',
    'totalPowerDownEquipments': 'Total Power Down Devices',
    'powerDownRatio': 'Power Down Ratio',
    'totalNEInterruptedEquipments': 'Total NE Interrupted Devices',
    'NEInterruptionRatio': 'NE Interruption Ratio',
    'totalLinkInterruptedEquipments': 'Total Link Interrupted Devices',
    'otherAlarmEquipments': 'Other Alarm Devices',
    'stick': 'Top',
    'startTimeChoose': 'Please select start time',
    'endTimeChoose': 'Please select end time',
    'loginName': 'Login Name',
    'affiliationOrganization': 'Organization',
    'phoneNumber': 'Mobile Phone Number',
    'VoLTEVoice': 'VoLTE Voice Wireless Call Completing Ratio',
    'VOLTEVoiceDrop': 'VoLTE Voice Call Drop Ratio',
    'exportBusinessFields': 'Output to Business Field',
    'and': 'And',
    'or': 'Or',
    'serviceOutput': 'Service Output',
    'businessFieldsields': 'Business Field',
    'policyGist': 'Decision Basis',
    'branchName': 'Branch Name',
    'defaultBranch': 'Default Branch',
    'judgmentCondition': 'Judgment Condition',
    'businessData': 'Business Data',
    'button': 'Button',
    'tacheNameNotNull': 'Activity name can not be empty',
    'tacheCodeNotNull': 'Activity code can not be empty',
    'branchNameCodeNotNull': 'Branch name and code can not be empty',
    'bbuLayer': 'BBU Layer',
    'meters': 'Meter',
    'theDistanceIs': 'The distance is',
    'returnTemplate': 'Return Parameter Template',
    'JudgmentWay': 'Judgment Method',
    'serve': 'Service',
    'groovy': 'Groovy Script',
    'scriptParameters': 'Script Parameter',
    'parameterNames': 'Parameter Name',
    'variableName': 'Variable Name',
    'savePointSuccess': 'Coordinates saved successfully',
    'saveStyleSuccess': 'Style saved successfully',
    'notChange': 'Unchanged',
    'changePwd': 'Change Password',
    'oldPwd': 'Original Password',
    'password': 'Password',
    'confirmPwd': 'Confirm Password',
    'busiTypeAndBusiName': 'Business type / name',
    'businessCode': 'Business Code',
    'circuitCode': 'Circuit Code',
    'waitingForReinsurance': 'Waiting for Important Insurance',
    'reinsurance': 'Important Insurance',
    'hiddenDanger': 'Hazards',
    'noHiddenDanger': 'No Hazards',
    'basicBusinessInfo': 'Basic Business Information',
    'businessDetailsInfo': 'Business Details',
    'businessResourceTree': 'Business Resource Tree',
    'confirmOldPwd': 'The original password is wrong, please confirm',
    'pwdUnqualified': 'Password strength does not meet the requirements',
    'circuitStatus': 'Circuit Status',
    'oldSameToComfirm': 'Confirmed password must be the same as password',
    'groovyInput': 'Please enter the groovy script',
    'oneJudgment': 'Please configure the data in the first row of the judgment method first',
    'contacts': 'Contact Person',
    'completeJudgmentOne': 'Please fill in the whole judgment method at the ',
    'completeJudgmentTwo': 'Row Data',
    'responseDemoInput': 'Please enter the returned parameter template',
    'circuitUse': 'Circuit Use',
    'businessLevel': 'Business Level',
    'circuitProtectionMode': 'Circuit Protection Mode',
    'circuitMaintenanceLevel': 'Circuit Maintenance Level',
    'mainSpareType': 'Main and Standby Type',
    'circuitControlLevel': 'Circuit Control Level',
    'businessNum': 'Business No.',
    'circuitFeature': 'Circuit Characteristics',
    'startRentTime': 'Start Time',
    'dispatchListLevel': 'Dispatch Level',
    'spareMode': 'Standby Mode',
    'endRentTime': 'Withdrawal Time',
    'businessFeature': 'Business Characteristics',
    'changeTime': 'Change Time',
    'customerName': 'Customer Name',
    'qos': 'QOS',
    'isCenterNode': 'Center Point or Not',
    'accType': 'Access Mode',
    'accServiceCode': 'Cable Broadband Service No.',
    'vpnNetworkNumber': 'VPN Network No.',
    'networkTopo': 'Networking Topology',
    'groupServiceCode': 'Group Circuit Service No.',
    'vpnRateIn': 'VPN Rate (in)',
    'startAddress': 'Start Address',
    'virtualRoadNumber': 'Virtual Road No.',
    'vpnRateOut': 'VPN Rate (out)',
    'Threshold1': 'Threshold 1',
    'Threshold2': 'Threshold 2',
    'endAddress': 'End Address',
    'routeType': 'Route type',
    'configureThresholdOne': 'Please configure the data of threshold 1 in the judgment method',
    'configureVariableName': 'Please configure the variable name of the original returned result',
    'bpmnNotNull': 'bpmnXml can not be empty',
    'changesStatistics': 'Change Statistics',
    'editAssessmentSettings': 'Edit Assessment Setting Information',
    'businessChoice': 'Business Selection',
    'bulkChanges': 'Batch Modify',
    'dateType': 'Date Type',
    'typeMismatch': 'The mapping content of the parameter is inconsistent with the original parameter type',
    'setUp': 'Setting',
    'listOrderRule': 'Each entry is sorted according to this rule, and the first rule selected is prioritized.',
    'ascending': 'Ascending Order',
    'descending': 'Descending Order',
    'listOrderRemark': 'Note: the reset will take effect after saving again',
    'alarmSoundRule': 'Different voice alerts for different alarm configurations',
    'Audition': 'Listen (Current)',
    'listOrder': 'List Sorting',
    'alarmSound': 'Alarm Sound',
    'originalSound': 'Original Sound Effect',
    'sound': 'Sound Effect',
    'currentSound': 'Current Sound Effect',
    'soundTip': 'Sound effect configuration is for the current configuration. Click sound effect to listen. The upload format supports MP3, WMA and WAV, and the file size is not more than 10M, and the duration is not more than 1 minute',
    'sorting': 'Sorting',
    'useSystem': 'Use System Default',
    'uploadFile': 'Upload File',
    'sysSoundTip': 'Use the system default sound effect successfully',
    'formatTip': 'The upload format only supports MP3, WMA and WAV',
    'sizeTip': 'The file size can not exceed 10 M, duration can not exceed 1 minute',
    'elasticCloudServer': 'Elastic Cloud Server',
    'managementMachine': 'Management Virtual Machine',
    'dataNumTip': 'The system does not support viewing over 10,000 records. Please check the scope accurately through filtering criteria.',
    'localCity': 'City',
    'ip': 'IP',
    'accessEquipment': 'Route Device',
    'functionalAuthority': 'Funtional Permission',
    'dataAccess': 'Data Permission',
    'role': 'Role',
    'organizeEmployee': 'Organization Staff',
    'defeatedNum': 'Total Failed Tasks',
    'machineIP': 'Execution Machine IP',
    'taskElapsedTime': 'Task Time',
    'roleName': 'Role Name',
    'viewRole': 'View Role Information',
    'regionalAuthority': 'Region Permission',
    'resourceSelection': 'Resource Selection Interface',
    'timeUnit': 'Cycle Type',
    'taskInstanceType': 'Task Monitoring',
    'netCate': 'NE Category',
    'netStatus': 'NE Status',
    'repNo': 'Request Number',
    'callpart': 'Caller',
    'strucType': 'Instruction Type',
    'resStatus': 'Result Status',
    'returnBitNum': 'Return Bytes',
    'normalCtru': 'Ordinary Instruction',
    'hDangerCtru': 'High Risk Instruction',
    'authCtru': 'Authority Instruction',
    'authority': 'Privilege',
    'reqText': 'Request Message',
    'cmdResult': 'Response Message',
    'safetyLevel': 'Security Level',
    'cmdCode': 'Instruction Code',
    'forRecord': 'Forwarding Record',
    'reqSize': 'Request Message Size',
    'respSize': 'Return Message Size',
    'startReqT': 'Request Start Time',
    'endReqT': 'Request End Time',
    'reqUseT': 'Request Time',
    'currentConnNum': 'Total Current Connections',
    'integrity': 'Integrity',
    'executionTime': 'Execution Time',
    'fileNum': 'Total of Documents',
    'dataNum': 'Total of Data',
    'installationRate': 'Integrity Ratio',
    'integrityThireshold': 'Integrity Threshold',
    'fileName': 'File Name',
    'executeIP': 'Execution IP',
    'downLoadTime': 'Download Time',
    'downLoadExpend': 'Download Consumption Time',
    'resolutionStartTime': 'Parsing Time',
    'analysisExpend': 'Parsing Consumption Time',
    'intact': 'Complete',
    'noIntact': 'Incomplete',
    'checkWay': 'Detection Method',
    'accessCode': 'Privilege Code',
    'resourceIdent': 'Resource ID',
    'resourceName': 'Resource Name',
    'department': 'Department',
    'account': 'Account',
    'email': 'Email',
    'parentNode': 'Parent Node',
    'roleUser': 'Role User',
    'SiteName': 'Site Name',
    'resouceChoose': 'Please select resource source',
    'allSpecialty': 'All Specialities',
    'timeGranularity': 'Collection Period',
    'totalNeNum': 'Total of NEs',
    'connNeRate': 'NE Connection Ratio',
    'equipmentLayer': 'Device Layer',
    'determineScript': 'Judgement Script',
    'outputInfomation': 'Output Information',
    'treatment': 'Processing Suggestion',
    'sendSingleIdentity': 'Dispatch ID',
    'determineScriptInput': 'Please enter judgement script',
    'sendSingleIdentityChoose': 'Please select dispatch ID',
    'resultEncoding': 'The result code can not be a pure number',
    'resultCoded': 'Result code already exists, please re-enter',
    'spread': 'Expand',
    'retract': 'Collapse',
    'sceneManage': 'Scene Management',
    'storage': 'Storage',
    'exceptionName': 'Exception Name',
    'noEquipment': 'No Device',
    'parentScene': 'Parent Scene',
    'detectionCycle': 'Detection Cycle',
    'subScene': 'Sub Scene',
    'ScenceDesc': 'Scene Description',
    'scene': 'Scene',
    'exceptionDesc': 'Exception Description',
    'mainScene': 'Main Scene',
    'ruleManage': 'Rule Management',
    'abnormalManage': 'Abnormal Reason Management',
    'sketchMap': 'Schematic Diagram',
    'analysisRules': 'Analysis Rule',
    'ruleDetails': 'Rule Detail',
    'flowChart': 'Flow Chart',
    'allTypes': 'All Types',
    'objectCategory': 'Object Category',
    'newRule': 'Add Rule',
    'suggestions': 'Suggestion Contents',
    'newSubScene': 'Add Sub Scene',
    'programType': 'Program Type',
    'programName': 'Program Name',
    'processLogic': 'Processing Logic',
    'waitingTime': 'Waiting Time',
    'equipmentAlarm': 'Device Alarm',
    'errorLog': 'Error Log',
    'dymFlowConfig': 'Dynamic Subprocess Configuration',
    'configMode': 'Configuration Mode',
    'referenceTemplate': 'Input Parameter Template',
    'parameterSettings': 'Input Parameter Setting',
    'newVariable': 'Add Variable',
    'processingState': 'Processing Status',
    'processLogDetails': 'Process Log Details',
    'noFlowMsg': 'No Process Instance Information',
    'sourceType': 'Source Type',
    'resultsProperties': 'Result Attribute',
    'GroovyProperty': 'Response Result Attribute / Groovy Parameter',
    'RsetSource': 'Result setting - the source property in the original response can not be empty',
    'RsetDetection': 'Result setting - the source property in detection conclusion can not be empty',
    'RsetDetectionBenchmark': 'Result setting - the baseline value in the detection conclusion can not be empty',
    'RsetResultBenchmark': 'Result setting - the benchmark value in the request result can not be empty',
    'workDetails': 'Fault Ticket Detail',
    'cutOverDetails': 'Migration Detail',
    'relatedDetails': 'Associated Detail',
    'lifeState': 'Life Status',
    'dictionaryValue': 'Dictionary Value Query Exception',
    'causeProblem': 'Fault Cause',
    'regionalName': 'Region Name',
    'autoRefresh': 'Automatic Refresh',
    'refreshTime': 'Refresh Time',
    'configLink': 'Please configure link first',
    'failureMessage': 'Failure Information',
    'lifeCycle': 'Lifecycle State',
    'onlyValue': 'Unique Value',
    'standardizeIdent': 'Standardized Alarm ID',
    'alarmSubtypes': 'Alarm Sub Type',
    'alarmStandard': 'Alarm Standardized Configuration',
    'addAlarmStandard': 'Add Alarm Standardized Configuration',
    'editAlarmStandard': 'Edit Alarm Standardized Configuration',
    'copyAlarmStandard': 'Copy Alarm Standardized Configuration',
    'viewAlarmStandard': 'View Alarm Standardized Configuration',
    'pleaseAccNum': 'Please enter account',
    'pleaseRoleName': 'Please enter role name',
    'pleaseRole': 'Please select role',
    'coordinateMissingTip': 'Coordinate data missing, unable to locate',
    'remoteIP': 'Remote IP',
    'toEndIP': 'Opposite-end IP',
    'systemCode': 'System Code',
    'resend': 'Resend',
    'viewExceptionInfo': 'View Abnormal Information',
    'alarmSource': 'Alarm Source',
    'resendOrNot': 'Confirm to resend or not',
    'alarmNormal': 'Alarm Status: Normal',
    'alarmAbnormal': 'Alarm Status: Abnormal',
    'brokenAbnormal': 'Base Station Breakdown: Normal',
    'brokenNormal': 'Base Station Breakdown: Abnormal',
    'userCode': 'User Code',
    'staffName': 'Staff Name',
    'linkStatus': 'Connection Status: Normal',
    'AlinkStatus': 'Connection Status: Abnormal',
    'loginEquip': 'Login Device',
    'loginType': 'Login Type',
    'processConfSuccess': 'Process Configuration Successful',
    'acceptanceItemPlease': 'Please configure at least one acceptance item',
    'Tstation': 'Station',
    'maintainUnit': 'Maintenance Unit',
    'sendOrdersMessage': 'Dispatch Information',
    'configFlow': 'Please configure process',
    'effectBeginTime': 'Start Valid Time',
    'effectEndTime': 'End Valid Time',
    'deviceId': 'Device ID',
    'deviceManageIp': 'Device Management IP',
    'isNetwork': 'Whether the network reachable or not',
    'authePwd': 'Authentication Password',
    'encryptionScheme': 'Encryption Mode',
    'encryptedString': 'Encryption Text',
    'connectivity': 'Connectivity',
    'testTime': 'Test Time',
    'loginWay': 'Login Mode',
    'loginPort': 'Login Port',
    'loginAccount': 'Login Name',
    'loginPwd': 'Login Password',
    'snmpVersion': 'SNMP Version',
    'snmpPort': 'SNMP Port',
    'communityMsg': 'Community',
    'networkLoginConfig': 'NE Login Configuration',
    'snmpConfig': 'SNMP Protocol Configuration',
    'alarmCreateTime': 'INMS Created Time',
    'isCurrentTime': 'Current time should be in the selected time range',
    'authenticationMode': 'Authentication Protocol',
    'loginPortInput': 'Please enter login port',
    'loginAccountInput': 'Please enter login name',
    'loginPwdInput': 'Please enter login password',
    'snmpPortInput': 'Please enter SNMP port',
    'communityMsgInput': 'Please enter community',
    'authenticationModeChoose': 'Please select authentication protocol',
    'authePwdInput': 'Please enter authentication password',
    'encryptionSchemeChoose': 'Please select encryption mode',
    'encryptedStringInput': 'Please enter encryption text',
    'formExcel': 'Please upload correct format file',
    'neConfig': 'Confirm as trusted NE or not',
    'isVirtual': 'Virtual or not',
    'isVirtualChoose': 'Please select virtual or not',
    'snmpUserName': 'Security Name',
    'snmpUserNameInput': 'Please enter security name',
    'aStation': 'A Side Station',
    'aMaintainUnit': 'A Side Maintenance Unit',
    'aContactNumber': 'A Side Contact Number',
    'aOrganization': 'A Side Organization',
    'zStation': 'Z Side Station',
    'zMaintainUnit': 'Z Side Maintenance Unit',
    'zContactNumber': 'Z Side Contact Number',
    'zOrganization': 'Z Side Organization',
    'addressIp': 'Device IP',
    'productSaleCode': 'Offer Code',
    'ottName': 'OTT Name',
    'ottCode': 'OTT ID',
    'rgCode': 'RG Code',
    'fourProtocolType': '4 Layer Protocol Type',
    'sourceIp': 'Source IP',
    'sourceMask': 'Source Mask',
    'sourceStartPort': 'Source Start Port',
    'sourceEndPort': 'Source End Port',
    'destinationIp': 'Destination IP',
    'destinationMask': 'Destination Mask',
    'destinationStartPort': 'Destination Start Port',
    'destinationEndPort': 'Destination End Port',
    'sevenProtocolType': '7 Layer Protocol Type',
    'modifyStatus': 'Modification Status',
    'netMsg': 'NE Information',
    'singleBack': 'Return',
    'automated': 'Auto Execution',
    'isShutDown': 'Close or Not',
    'lock': 'Lock',
    'unlock': 'Unlock',
    'startDelay': 'Start Delay',
    'keywordInput': 'Please enter keywords and press enter',
    'ruleLogo': 'Rule ID',
    'batchLock': 'Batch Lock',
    'batchUnLock': 'Batch Unlock',
    'empty': 'Empty',
    'notEmpty': 'Not Empty',
    'changeTem': 'Changing conditions will clear the selected template, continue or not?',
    'mustAlarm': 'Required Alarm',
    'showLockAlarm': 'Display Locked Alarm',
    'hideLockAlarm': 'Hide Locked Alarm',
    'copyCheckItem': 'Copy Detection Item',
    'inspectionTip': 'No objects are selected, all that meet the filter condition will be added for inspection.',
    'handleClosed': 'Handle Closed',
    'dispatch': 'Dispatch',
    'convergence': 'Convergence',
    'parallel': 'Parallel',
    'updatedUserName': 'Update Username',
    'requiredItemNotEmpty': 'Required field can not be empty',
    'updateBeforeSave': 'Please save after modifying process',
    'saveBeforePublish': 'Please save process before releasing',
    'linkOutput': 'Activity Output',
    'linkInput': 'Activity Input',
    'queryFailed': 'Query Failed',
    'bbuType': 'BBU Type',
    'serviceType': 'Service Type',
    'originalSystem': 'Original System',
    'targetSystem': 'Target System',
    'requestWay': 'Request Mode',
    'InputOutput': 'I/O',
    'ExceptionInfo': 'Abnormal Information',
    'output': 'Output Result',
    'input': 'Input',
    'outputing': 'Output',
    'transferSideOdf': 'Transmission Side ODF Port',
    'AtransferSideOdf': 'A Side Transmission Side ODF Port',
    'ZtransferSideOdf': 'Z Side Transmission Side ODF Port',
    'residualCapacity': 'Remaining Capacity',
    'connectionStatus': 'Connection Status',
    'disconnectTime': 'Disconnected Time',
    'aEndTranSidePort': 'A Side Transmission Side Port',
    'zEndTranSidePort': 'Z Side Transmission Side Port',
    'aSideBusinessSidePort': 'A Side Business Side Port',
    'zSideBusinessSidePort': 'Z Side Business Side Port',
    'aSideBusinessSideOdf': 'A Side Business Side ODF Port',
    'zSideBusinessSideOdf': 'Z Side Business Side ODF Port',
    'hierarchicalChannelList': 'Hierarchical Channel List',
    'routingListAndCarry': 'Routing List and Bearer Circuits',
    'ownerType': 'Owner Type',
    'schedulingType': 'Schedule Type',
    'previousStep': 'Previous',
    'nextStep': 'Next step',
    'saveRoute': 'Save Route',
    'jump': 'Jump',
    'planConfirm': 'Scheme Confirmation',
    'pathSelect': 'Path Selection',
    'routeSave': 'Route Saving',
    'pleaseKeywords': 'Please enter keywords',
    'pleaseChoosePort': 'Please select  port',
    'useRecommendedName': 'Use Recommended Name',
    'subnetConDir': 'Subnet Connection Direction',
    'crossConDir': 'Cross Connection Direction',
    'crossConType': 'Cross Connection Type',
    'subnetConType': 'Subnet Connection Type',
    'choosePort': 'Select port',
    'chooseNetOrPort': 'To Be Selected NE / Port',
    'onlyObjects': 'Required Object',
    'avoidObject': 'Avoidance Object',
    'pathObject': 'Path Object',
    'aNetwork': 'A Side NE',
    'zNetwork': 'Z Side NE',
    'hop': 'Hop',
    'sncRouteInfo': 'SNC Route Information',
    'sourcePort': 'Source Port',
    'hostPort': 'Host Port',
    'crossConfigInfo': 'Cross Configuration Information',
    'crossType': 'Cross Type',
    'crossLinkaSide': 'Cross Link A Side',
    'crossLinkzSide': 'Cross Link Z Side',
    'existAvoidObject': 'The selected object already in the avoidance objects, please select again',
    'existOnlyObject': 'The selected object already in the required objects, please select again',
    'confirmEdit': 'Modify or not',
    'aSiteChoose': 'Please select A side site',
    'aPortChoose': 'Please select A side port',
    'zSiteChoose': 'Please select Z side site',
    'zPortChoose': 'Please select Z side port',
    'rateChoose': 'Please select rate',
    'circuitNameInput': 'Please enter circuit name',
    'sConnetDirChoose': 'Please select subnet connection direction',
    'sConnetTypeChoose': 'Please select cross connection direction',
    'crossConnetDirChoose': 'Please select cross connection type',
    'crossConnetTypeChoose': 'Please select subnet connection type',
    'topNChoose': 'Please select topN',
    'NetworkRateInstruct': 'Average Download Rate of Wireless Network Users',
    'BarangayQuantityInstruct': 'Total of Barangays Covered by Wireless Sites',
    'PopulationCoverageInstruct': 'Proportion of Population Covered by Wireless Sites in Total Population',
    '4GUserInstruct': 'Statistics of 4G Wireless Site Access Users',
    '5GuserInstruct': 'Statistics of 5G Wireless Site Access Users',
    'fiberOpticCableInstruct': 'Statistics of the Optical Cable Length Summary by Resource System',
    'IPCoreNodeInstruct': 'Statistics of IP Core Node Devices, including the Following Device Types: CR,PE,RR,E,IXR,MCE,NAT,BG',
    'OTNCoreNodeInstruct': 'Statistics of Transmission OTN Devices',
    'OTNBackBoneRingInstruct': 'Statistics of IPRAN B Devices',
    'VirtualMachineInstruct': 'Statistics of Cloud Pool VMs',
    'cpuInstruct': 'CPU Usage',
    'MemoryInstruct': 'Memory Usage',
    'StorageInstruct': 'Storage Usage',
    'fourSiteNumber': 'Total 4G Sites',
    'fiveSiteNumber': 'Total 5G Sites',
    'fourFlow': '4G Traffic',
    'fiveFlow': '5G Traffic',
    'fourConnectivity': '4G Connectivity',
    'fiveConnectivity': '5G Connectivity',
    'IPTrafficData': 'IP Network Traffic Data',
    'pue': 'Energy Consumption Indicator',
    'riskOperand': 'Total Risk Operands',
    'networkFailNum': 'Total Network Faults',
    'measured': 'Complaint Rate',
    'alarmTotalInstruct': 'Statistics of Total Uncleared Transmission Alarms',
    'alarmFaultOvertimeInstruct': 'Statistics of Timeout Ratio of Total Transmission Fault Tickets',
    'netRobustnessInstruct': 'The Overall Robustness Indicator of Transmission Devices, is Calculated by the Logic of Ring Network, Same Channel and Same Cable of Transmission Network.',
    'healthTranInstruct': 'The Transmission Inspection Task is the Summary of Normal Ratio of Device Inspection, to Reflect the Overall Health of the Transmission Network',
    'networkTranInstruct': 'Statistics of Total Transmission Ne Alarms',
    'fiberOpticCableAlarmInstruct': 'Statistics of Total Optical Cable Alarms',
    'submarineCableAlarmInstruct': 'Statistics of Total Submarine Cable Alarms',
    'circuitAlarmInstruct': 'Statistics of Total Circuit Alarms',
    'networkNumberInstruct': 'Statistics of Total Transmission Devices',
    'circuitNoInstruct': 'Statistics of Total Transmission Circuits',
    'cableNoInstruct': 'Statistics of Total Optical Cable Length by Resource System',
    'SubmarineCableNoInstruct': 'Statistics of Total Submarine Cable Length',
    'overWorkOrderInstruct': 'Statistics of Total Transmission Dispatched Timeout Tickets',
    'onWorkInstruct': 'Statistics of Total Transmission Dispatched In Progress Tickets',
    'hangWorkInstruct': 'Statistics of Total Transmission Dispatched Hang-Up Tickets',
    'insNumInstruct': 'Statistics of Total Transmission Inspection Tasks',
    'abINsInstruct': 'Statistics of Total Transmission Inspection Exception Tasks',
    'openInstruct': 'Statistics of Total Provisioning Tickets',
    'adjustInstruct': 'Statistics of Total Dismantlement Tickets',
    'demolitionInstruct': 'Statistics of Total Adjustment Tickets',
    'cutOverWaitInstruct': 'Statistics of Total To Be Migrated Tasks',
    'cutOveringInstruct': 'Statistics of Total Under Migrating Tasks',
    'cutOveredInstruct': 'Statistics of Total Finished Tasks',
    'storageUtilizationIndicator': 'Storage Utilization Indicator',
    'hostNumber': 'Total of Hosts',
    'virtualMachinesNum': 'Total of VMs',
    'storageUnitsNumber': 'Total of Storage Machines',
    'routersNumber': 'Total of Routers',
    'switchesNum': 'Total of Switches',
    'firewallsNumber': 'Total of Firewalls',
    'totalCpuCapacity': 'Total CPU Capacity',
    'cldSeriousAlarm': 'Total of Critical Cloud Network Alarms',
    'cldImpAlarm': 'Total of Major Cloud Network Alarms',
    'cldSecAlarm': 'Total of Minor Cloud Network Alarms',
    'letNumInstruct': 'Total of LTE BBU Devices / RRU Devices',
    'fiveNumInstruct': 'Total of 5G BBU Devices / AAU Devices',
    'wirSiteNum': 'Total of Wireless Sites',
    'showLteAlarm': 'Total of LTE Alarms',
    'stationBreakNumberInstruct': 'Total of LTE Base Station Breakdowns / Total of 5G Base Station Breakdowns',
    'workingOdd': 'Total of Tickets',
    'fiveBusyInstruct': 'Current Cycle / Total of 5G Super Busy Cells (the Same Period of Last Month)',
    'fourBusyInstruct': 'Current Cycle / Total of 4G Super Busy Cells (the Same Period of Last Month)',
    'hierarchicalTopo': 'Hierarchical Route Topology',
    'pathSelectTipOne': 'Do you want to save the current path selection information',
    'pathSelectTipTwo': 'Path information has not been selected, do you want to go to the next step directly',
    'createActivateCross': 'Create and Activate the Cross',
    'autoDiscoveryPath': 'Auto-discovery Path',
    'getSncName': 'Get SNC Information',
    'editSnc': 'Modify SNC Information',
    'executionNum': 'Total of Executing Tasks',
    'completedNum': 'Total of Completed Tasks',
    'plan': 'Scheme',
    'missPortPrompt': 'Missing A, Z side port data',
    'sncNameInput': 'Please enter SNC name',
    'ctpPortChoose': 'Please select CTP port data',
    'portDifPrompt': 'A, Z side port can not be the same',
    'siteDifPrompt': 'A, Z side site can not be the same',
    'sncLogo': 'SNC ID',
    'sncLogoInput': 'Please enter SNC ID',
    'isPlanConfig': 'Confirm the scheme or not?',
    'stationTopo': 'Instation Topology',
    'string': 'String',
    'array': 'List',
    'number': 'Value',
    'bool': 'Bool',
    'routeCalc': 'Calculate routing or not?',
    'cableLength': 'Optical Cable Length',
    'planTime': 'Planned Time',
    'impactOnThePhilippines': 'Affect on Philippines',
    'estimatedTimeArrival': 'Estimated Landing Time',
    'currentWindSpeed': 'Current Wind Speed',
    'currentWindLevel': 'Current Wind Scale',
    'planningArea': 'Planning Area',
    'infomation': 'information',
    'calculating': 'In Calculation',
    'siteStatus': 'Station status',
    'energy': 'Energy',
    'energySaving': 'Energy Saving Mode',
    'areaNetDetail': 'Area NE Details',
    'implement': 'Execution',
    'recover': 'Recovery',
    'createTask': 'Create Task',
    'theAreaName': 'Affected Area Name',
    'theTyphoonRadius': 'Typhoon Radius',
    'selectTyphoon': 'Choose Typhoon',
    'autoDraw': 'Automatic Drawing',
    'typhoonName': 'Typhoon name',
    'infomationSource': 'Information Sources',
    'radiusInput': 'Please enter the radius',
    'addCostomArea': 'Add Affected Area',
    'isSncName': 'Modify SNC name or not',
    'ipName': 'IP Name',
    'aPortIp': 'A Side IP',
    'zPortIp': 'Z Side IP',
    'aPortEquName': 'A Side Device Name',
    'aPortName': 'A Side Port Name',
    'zPortEquName': 'Z Side Device Name',
    'zPortName': 'Z Side Port Name',
    'packetLoss': 'Packet Loss',
    'portError': 'Port Errored Information',
    'currentInflowRate': 'Current Inflow Rate',
    'currentOutflowRate': 'Current Outflow Rate',
    'InflowBandwidth': 'Inflow Bandwidth Utilization',
    'outflowBandwidth': 'Outflow Bandwidth Utilization',
    'flowPattern': 'Traffic Fluctuation Chart',
    'circuitIndicators': 'Circuit Indicator',
    'circuitTopo': 'Circuit Topology',
    'iduNumber': 'Total of IDUs',
    'linkNumber': 'Total of Links',
    'bandwidthLoad': 'Bandwidth Load',
    'electricFrequency': 'Electric Frequency Value',
    'coreIndex': 'Core Indicator',
    'perforAlertConfig': 'Performance Alarm Configuration',
    'trunk': 'Trunk',
    'sceneNameIsRequired': 'Scene name is required',
    'planTimeIsRequired': 'Planned time is required',
    'estimatedLandingTimeIsRequired': 'Estimated landing time is required',
    'currentWindSpeedIsRequired': 'Current wind speed is required',
    'currentWindScaleIsRequired': 'Current wind scale is required',
    'areaNameIsRequired': 'Area name is required',
    'drawAreaIsRequired': 'New affected area must be added manually',
    'selectTyphoonIsRequired': 'Associated typhoon must be selected',
    'raduisIsRequired': 'Typhoon radius is required',
    'newSceneSuccess': 'New scene added successfully',
    'editSceneSuccess': 'Scene information edited successfully',
    'newAreaSuccess': 'New affected area added successfully',
    'editAreaSuccess': 'Affected area information edited successfully',
    'linkTyphoonSuccess': 'Typhoon associated successfully',
    'linkTyphoonFail': 'Typhoon associated failed',
    'typhoonIsSelected': 'The typhoon has been associated with other scenarios',
    'typhoonList': 'Typhoon List',
    'sceneList': 'Scene List',
    'newTyphoon': 'New Typhoon',
    'newScene': 'New Scene',
    'clickHereAddNewArea': 'Click custom to add the possible affected area',
    'energySaveingIsOpen': 'Energy saving mode on',
    'energySaveingIsClose': 'Energy saving mode off',
    'clickToStartDrawing': 'Click to start drawing',
    'perssToStartReleaseToFinish': 'Click to add graphics, or press and hold the mouse to start drawing, release the mouse to finish drawing',
    'doubleClickToComplete': 'Double click to finish drawing',
    'clickToContiuneDrawing': 'Click to continue drawing',
    'commandTaskNumber': 'Plan Ticket',
    'urgencyLevel': 'Urgency',
    'securityLevel': 'Security Level',
    'whetherAssess': 'Assess or Not',
    'requiredCompletionDate': 'Required Completion Time',
    'title': 'Title',
    'commandTaskSaveSuccess': 'Plan ticket saved successfully',
    'commandTaskSaveFail': 'Plan ticket save failed',
    'titleIsRequired': 'Title is required',
    'requiredCompletionIsRequired': 'Required completion time is required',
    'productionCommand': 'Production Task',
    'meetingNotification': 'Conference Notification',
    'urgent': 'Urgent',
    'confidential': 'Private',
    'urgencyLv': 'Urgency Level',
    'abnormalObjectNum': 'Total Abnormal Objects',
    'ruleSyncAlarm': 'Alarm Synchronization Rule',
    'addRuleSyncAlarm': 'Add Synchronization Rule',
    'editRuleSyncAlarm': 'Edit Synchronization Rule',
    'otnAlarmTips': 'Attention please, when modifying the configuration info of OTN circuit, the circuit will be deleted and then re-created, and this may affect the business use during modification process when circuit was deleted.',
    'otnChangeConfirm': 'The basic information has been changed, please confirm to adjust or not',
    'alarmReverse': 'Alarm Reverse',
    'cancelAlarmReverse': 'Cancle Alarm Reverse',
    'hasCircuitTip': 'The circuit already exists, to check or not?',
    'continueCircuitTip': 'There are on-going tasks, to jump and continue or not?',
    'sceneActionSuccessRate': 'Scene Execution Success Rate',
    'today': 'Today',
    'thisWeek': 'This Week',
    'thisMonth': 'This Month',
    'actionSuccessRate': 'Execution Success Rate',
    'manuallyCreate': 'Manual Creation',
    'operResult': 'Operation Results',
    'sensitiveOperations': 'Sensitive Operation',
    'executeTip': 'This operation will execute the plan, continue or not',
    'recoverTip': 'This operation will restore the implemented plan, continue or not',
    'planCount': 'Plan Statistics',
    'planActionNumTop5': 'TOP 5 Objects of Plan Executions',
    'softwareVer': 'OS Version',
    'roomName': 'Room Name',
    'currentAlarmInfo': 'Real-time Alarm Information',
    'planInfo': 'Plan Information',
    'planStatus': 'Plan Status',
    'isHasAlarm': 'Alarm or Not',
    'sceneName': 'Scene Name',
    'actionNum': 'Total of Executions',
    'currentLinkInfo': 'Link Real-time Situation',
    'linkGroup': 'Link Group',
    'logInfo': 'Log Information',
    'portStatus': 'Port Status',
    'errorPackageNum': 'Total of Error Pockets',
    'inFlow': 'Input Traffic',
    'outFlow': 'Output Traffic',
    'linkRate': 'Link Utilization',
    'otnOccupationTips': 'Current A / Z side has been occupied, the unfinished circuit can continue or be cancelled, and the completed circuit can be cancelled.',
    'circuitList': 'Circuit List',
    'cancelTip': 'Cancel operation, continue or not',
    'plansConfirm': 'Plan Confirmation',
    'inUseRate': 'Input Utilization',
    'outUseRate': 'Output Utilization',
    'isWrongPackage': 'Error Pocket or Not',
    'batteryCapacity': 'Battery Capacity',
    'ConfiguringBackups': 'waiting for translate',
    'backupLog': 'waiting for translate',
    '4GuserMigration': 'waiting for translate',
    '5GuserMigration': 'waiting for translate',
    'migrationPercent': 'waiting for translate',
    'initUserInfo': 'waiting for translate',
    'currentUserInfo': 'waiting for translate',
    'isolationFailure': 'waiting for translate',
    'openMMEAbility': 'waiting for translate',
    'openAMFAbility': 'waiting for translate',
    'ReleaseMEEAndAMFQuarantine': 'waiting for translate'
}