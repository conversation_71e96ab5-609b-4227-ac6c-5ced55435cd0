# vue 项目开发模板

## 相关依赖及配置

1、util 通用工具类

2、打包的相关配置

3、默认依赖饿了么

4、默认安装 less 解析器

5、统一国际化引入(请搭配微型统一配置系统进行使用)

## 使用步骤

1、`git clone  <http://*************/oss-framework/front-end/vue-ele-i18n-example.git`>

2、删除 git 文件夹

3、修改 package.json 中的 相关信息（主要更改 description、name、author 三项）

4、确保 npm 源路径指向正常

5、执行 npm ci 进行包的安装（若发生下载依赖包失败的情况，可以删除`package-lock.json`和`node_module`然后执行`npm i`命令进行包的下载）

6、搭建在项目模板上进行开发

## 构建命令

```bash
# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report

# run unit tests
npm run unit

# run all tests
npm test
```

## 国际化使用说明(项目本身默认关闭国际化)

1、若开发过程中需要使用国际化，请在/src/main.js 中设置
let useI18nLanguage = false; 若不需要则为 false

2、若项目打包过程中需要使用国际化，请在 build\webpack.prod.conf.js 中设置
let WhetherUseI18n = true; 若不需要则为 false

## 后端联调接口说明

1、联调接口的案例请参考/basicSearch 基本查询页面的调用示范

2、若需要设置接口超时时间，请在 src\libs\util.js 中第 36 行设置 timeout 值，接口超时情况处理请参考基本查询页面的 queryList 接口调用示范
